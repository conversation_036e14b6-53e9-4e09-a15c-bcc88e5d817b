-- =====================================================
-- SETUP DATABASE FOR GAME PORTAL APPLICATION
-- =====================================================

-- Create databases
CREATE DATABASE IF NOT EXISTS `newapi_account` CHARACTER SET utf8 COLLATE utf8_unicode_ci;
CREATE DATABASE IF NOT EXISTS `newapi_game_1` CHARACTER SET utf8 COLLATE utf8_unicode_ci;
CREATE DATABASE IF NOT EXISTS `laravel` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use account database
USE `newapi_account`;

-- =====================================================
-- ACCOUNT DATABASE TABLES
-- =====================================================

-- Account table (main user accounts)
CREATE TABLE IF NOT EXISTS `zt_account` (
  `UserID` varchar(50) NOT NULL PRIMARY KEY,
  `UserName` varchar(50) NOT NULL UNIQUE,
  `Password` varchar(255) NOT NULL,
  `Email` varchar(100) DEFAULT NULL,
  `fullName` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `Money` bigint(20) DEFAULT 0,
  `Role` int(11) DEFAULT 0,
  `IPAddress` varchar(45) DEFAULT NULL,
  `code_secret` varchar(255) DEFAULT NULL,
  `chip` bigint(20) DEFAULT 0,
  `luot_quay` int(11) DEFAULT 0,
  `groupid` int(11) DEFAULT 0,
  `RegTime` timestamp DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_username` (`UserName`),
  INDEX `idx_email` (`Email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Server data table
CREATE TABLE IF NOT EXISTS `zt_serverdata` (
  `Id` int(11) NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `ServerName` varchar(100) NOT NULL,
  `ServerURL` varchar(255) NOT NULL,
  `DatabaseName` varchar(100) NOT NULL,
  `ServerPort` int(11) DEFAULT 3306,
  `Status` tinyint(1) DEFAULT 1,
  `OnlineNum` int(11) DEFAULT 0,
  `StartTime` timestamp DEFAULT CURRENT_TIMESTAMP,
  `MaintainStarTime` timestamp NULL DEFAULT NULL,
  `MaintainTerminalTime` timestamp NULL DEFAULT NULL,
  `MaintainTxt` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- =====================================================
-- GAME DATABASE TABLES
-- =====================================================

USE `newapi_game_1`;

-- Roles table (game characters)
CREATE TABLE IF NOT EXISTS `t_roles` (
  `rid` bigint(20) NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `userid` varchar(50) NOT NULL,
  `rname` varchar(50) NOT NULL,
  `occupation` int(11) DEFAULT 0,
  `level` int(11) DEFAULT 1,
  `experience` bigint(20) DEFAULT 0,
  `money` bigint(20) DEFAULT 0,
  `regtime` timestamp DEFAULT CURRENT_TIMESTAMP,
  `lasttime` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `logofftime` timestamp NULL DEFAULT NULL,
  `deltime` timestamp NULL DEFAULT NULL,
  `isdel` tinyint(1) DEFAULT 0,
  INDEX `idx_userid` (`userid`),
  INDEX `idx_rname` (`rname`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Temp money table (for transactions)
CREATE TABLE IF NOT EXISTS `t_tempmoney` (
  `id` bigint(20) NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `cc` varchar(255) NOT NULL,
  `uid` varchar(50) NOT NULL,
  `rid` bigint(20) DEFAULT 0,
  `addmoney` bigint(20) NOT NULL,
  `itemid` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_uid` (`uid`),
  INDEX `idx_cc` (`cc`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Input log table (transaction history)
CREATE TABLE IF NOT EXISTS `t_inputlog` (
  `id` bigint(20) NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `amount` bigint(20) NOT NULL,
  `u` varchar(50) NOT NULL,
  `rid` bigint(20) DEFAULT 0,
  `order_no` varchar(100) NOT NULL,
  `cporder_no` varchar(100) NOT NULL,
  `time` bigint(20) NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_u` (`u`),
  INDEX `idx_order_no` (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Mail goods table (webshop items)
CREATE TABLE IF NOT EXISTS `t_mailgoods` (
  `id` bigint(20) NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `rid` bigint(20) NOT NULL,
  `goodsid` int(11) NOT NULL,
  `goodsnum` int(11) DEFAULT 1,
  `binding` tinyint(1) DEFAULT 0,
  `level` int(11) DEFAULT 0,
  `quality` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_rid` (`rid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- =====================================================
-- LARAVEL DATABASE TABLES
-- =====================================================

USE `laravel`;

-- Laravel users table
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL UNIQUE,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Password resets table
CREATE TABLE IF NOT EXISTS `password_resets` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  INDEX `idx_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Migrations table
CREATE TABLE IF NOT EXISTS `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
