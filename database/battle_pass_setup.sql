-- =====================================================
-- BATTLE PASS SYSTEM TABLES
-- =====================================================

USE `newapi_account`;

-- Battle Pass Seasons table
CREATE TABLE IF NOT EXISTS `battle_pass_seasons` (
  `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `name` varchar(255) NOT NULL,
  `description` text,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `max_level` int(11) DEFAULT 100,
  `price` bigint(20) DEFAULT 50000,
  `status` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Battle Pass Rewards table
CREATE TABLE IF NOT EXISTS `battle_pass_rewards` (
  `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `season_id` int(11) NOT NULL,
  `level` int(11) NOT NULL,
  `reward_type` varchar(50) NOT NULL, -- 'item', 'coin', 'exp'
  `reward_id` int(11) NOT NULL,
  `reward_amount` int(11) DEFAULT 1,
  `is_premium` tinyint(1) DEFAULT 0, -- 0=free, 1=premium
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_season_level` (`season_id`, `level`),
  FOREIGN KEY (`season_id`) REFERENCES `battle_pass_seasons`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Battle Pass Progress table
CREATE TABLE IF NOT EXISTS `user_battle_pass` (
  `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `user_id` varchar(50) NOT NULL,
  `season_id` int(11) NOT NULL,
  `current_level` int(11) DEFAULT 1,
  `current_exp` int(11) DEFAULT 0,
  `is_premium` tinyint(1) DEFAULT 0,
  `purchased_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `unique_user_season` (`user_id`, `season_id`),
  INDEX `idx_user_id` (`user_id`),
  FOREIGN KEY (`season_id`) REFERENCES `battle_pass_seasons`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Battle Pass Reward Claims table
CREATE TABLE IF NOT EXISTS `battle_pass_claims` (
  `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `user_id` varchar(50) NOT NULL,
  `season_id` int(11) NOT NULL,
  `level` int(11) NOT NULL,
  `reward_type` varchar(50) NOT NULL,
  `reward_id` int(11) NOT NULL,
  `reward_amount` int(11) NOT NULL,
  `is_premium` tinyint(1) NOT NULL,
  `claimed_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_user_season` (`user_id`, `season_id`),
  FOREIGN KEY (`season_id`) REFERENCES `battle_pass_seasons`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- SAMPLE BATTLE PASS DATA
-- =====================================================

-- Sample Season
INSERT INTO `battle_pass_seasons` (`name`, `description`, `start_date`, `end_date`, `max_level`, `price`, `status`) VALUES 
('Season 1: Warriors Rise', 'First battle pass season with exclusive warrior rewards', '2025-01-01 00:00:00', '2025-03-31 23:59:59', 100, 50000, 1),
('Season 2: Magic Awakening', 'Second season featuring magical items and spells', '2025-04-01 00:00:00', '2025-06-30 23:59:59', 100, 60000, 0);

-- Sample Rewards for Season 1
INSERT INTO `battle_pass_rewards` (`season_id`, `level`, `reward_type`, `reward_id`, `reward_amount`, `is_premium`) VALUES 
-- Free rewards
(1, 1, 'coin', 0, 1000, 0),
(1, 2, 'item', 1001, 1, 0),
(1, 3, 'coin', 0, 2000, 0),
(1, 4, 'item', 1002, 1, 0),
(1, 5, 'coin', 0, 5000, 0),
(1, 10, 'item', 1003, 1, 0),
(1, 15, 'coin', 0, 10000, 0),
(1, 20, 'item', 1004, 1, 0),
(1, 25, 'coin', 0, 15000, 0),
(1, 30, 'item', 1005, 1, 0),

-- Premium rewards
(1, 1, 'coin', 0, 2000, 1),
(1, 2, 'item', 2001, 1, 1),
(1, 3, 'coin', 0, 3000, 1),
(1, 4, 'item', 2002, 1, 1),
(1, 5, 'coin', 0, 8000, 1),
(1, 10, 'item', 2003, 1, 1),
(1, 15, 'coin', 0, 20000, 1),
(1, 20, 'item', 2004, 1, 1),
(1, 25, 'coin', 0, 30000, 1),
(1, 30, 'item', 2005, 1, 1),
(1, 50, 'item', 2010, 1, 1),
(1, 100, 'item', 2020, 1, 1);

-- Sample user progress
INSERT INTO `user_battle_pass` (`user_id`, `season_id`, `current_level`, `current_exp`, `is_premium`, `purchased_at`) VALUES 
('ZT00002', 1, 5, 1200, 1, '2025-01-15 10:30:00'),
('ZT00003', 1, 3, 800, 0, NULL),
('ZT00004', 1, 8, 2100, 1, '2025-01-20 14:20:00');

-- Sample claims
INSERT INTO `battle_pass_claims` (`user_id`, `season_id`, `level`, `reward_type`, `reward_id`, `reward_amount`, `is_premium`) VALUES 
('ZT00002', 1, 1, 'coin', 0, 1000, 0),
('ZT00002', 1, 1, 'coin', 0, 2000, 1),
('ZT00002', 1, 2, 'item', 1001, 1, 0),
('ZT00002', 1, 2, 'item', 2001, 1, 1),
('ZT00002', 1, 3, 'coin', 0, 2000, 0),
('ZT00002', 1, 3, 'coin', 0, 3000, 1);
