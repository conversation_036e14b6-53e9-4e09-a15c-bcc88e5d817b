-- =====================================================
-- SAMPLE DATA FOR GAME PORTAL APPLICATION
-- =====================================================

USE `newapi_account`;

-- =====================================================
-- DEMO ACCOUNTS
-- =====================================================

-- Admin account
INSERT INTO `zt_account` (
    `UserID`, `UserName`, `Password`, `Email`, `fullName`, 
    `Money`, `Role`, `IPAddress`, `groupid`, `RegTime`
) VALUES (
    'ZT00001', 'admin', MD5('admin123'), '<EMAIL>', 'Administrator',
    1000000, 1, '127.0.0.1', 1, NOW()
);

-- Demo user accounts
INSERT INTO `zt_account` (
    `UserID`, `UserName`, `Password`, `Email`, `fullName`, 
    `Money`, `Role`, `IPAddress`, `groupid`, `RegTime`
) VALUES 
('ZT00002', 'demo', MD5('demo123'), '<EMAIL>', 'Demo User', 50000, 1, '127.0.0.1', 0, NOW()),
('ZT00003', 'player1', MD5('player123'), '<EMAIL>', 'Player One', 25000, 1, '127.0.0.1', 0, NOW()),
('ZT00004', 'player2', MD5('player123'), '<EMAIL>', 'Player Two', 30000, 1, '127.0.0.1', 0, NOW()),
('ZT00005', 'testuser', MD5('test123'), '<EMAIL>', 'Test User', 15000, 1, '127.0.0.1', 0, NOW());

-- =====================================================
-- SERVER DATA
-- =====================================================

INSERT INTO `zt_serverdata` (
    `Id`, `ServerName`, `ServerURL`, `DatabaseName`, `ServerPort`, `Status`, `OnlineNum`
) VALUES 
(1, 'Server Alpha', '127.0.0.1', 'newapi_game_1', 3306, 1, 150),
(2, 'Server Beta', '127.0.0.1', 'newapi_game_2', 3306, 1, 89),
(3, 'Server Gamma', '127.0.0.1', 'newapi_game_3', 3306, 0, 0);

-- =====================================================
-- GAME DATA
-- =====================================================

USE `newapi_game_1`;

-- Demo characters for users
INSERT INTO `t_roles` (
    `userid`, `rname`, `occupation`, `level`, `experience`, `money`
) VALUES 
('ZT00001', 'AdminChar', 1, 99, 999999, 5000000),
('ZT00002', 'DemoWarrior', 1, 45, 125000, 250000),
('ZT00002', 'DemoMage', 2, 38, 98000, 180000),
('ZT00003', 'PlayerKnight', 1, 52, 180000, 320000),
('ZT00004', 'PlayerArcher', 3, 41, 110000, 200000),
('ZT00005', 'TestChar', 1, 25, 45000, 85000);

-- Sample transaction data
INSERT INTO `t_inputlog` (
    `amount`, `u`, `rid`, `order_no`, `cporder_no`, `time`
) VALUES 
(10000, 'ZT00002', 2, 'ORD001', 'CP001', UNIX_TIMESTAMP()),
(5000, 'ZT00003', 4, 'ORD002', 'CP002', UNIX_TIMESTAMP()),
(15000, 'ZT00004', 5, 'ORD003', 'CP003', UNIX_TIMESTAMP());

-- =====================================================
-- LARAVEL DATA
-- =====================================================

USE `laravel`;

-- Laravel admin user
INSERT INTO `users` (`name`, `email`, `password`, `created_at`, `updated_at`) VALUES 
('Administrator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW(), NOW()),
('Demo User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW(), NOW());

-- Migration records
INSERT INTO `migrations` (`migration`, `batch`) VALUES 
('2014_10_12_000000_create_users_table', 1),
('2014_10_12_100000_create_password_resets_table', 1);

-- =====================================================
-- ADDITIONAL GAME TABLES (if needed)
-- =====================================================

USE `newapi_account`;

-- Giftcode table
CREATE TABLE IF NOT EXISTS `giftcodes` (
  `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `code` varchar(255) NOT NULL UNIQUE,
  `reward_type` varchar(50) NOT NULL,
  `reward_amount` bigint(20) NOT NULL,
  `limit_uses` int(11) DEFAULT 1,
  `used_count` int(11) DEFAULT 0,
  `zoneid` int(11) DEFAULT 0,
  `period` int(11) DEFAULT 0,
  `status` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Sample giftcodes
INSERT INTO `giftcodes` (`code`, `reward_type`, `reward_amount`, `limit_uses`, `status`) VALUES 
('WELCOME2025', 'money', 10000, 100, 1),
('NEWBIE500', 'money', 5000, 50, 1),
('PREMIUM1000', 'money', 15000, 20, 1);

-- Webshop items table
CREATE TABLE IF NOT EXISTS `webshop_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `name` varchar(255) NOT NULL,
  `description` text,
  `price` bigint(20) NOT NULL,
  `item_id` int(11) NOT NULL,
  `category_id` int(11) DEFAULT 1,
  `stock` int(11) DEFAULT -1,
  `status` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Sample webshop items
INSERT INTO `webshop_items` (`name`, `description`, `price`, `item_id`, `category_id`) VALUES 
('Sword of Power', 'Legendary sword with +100 attack', 25000, 1001, 1),
('Magic Shield', 'Protective shield with +50 defense', 15000, 1002, 1),
('Health Potion', 'Restores 500 HP instantly', 1000, 2001, 2),
('Mana Potion', 'Restores 300 MP instantly', 800, 2002, 2),
('Experience Scroll', 'Grants 10000 experience points', 5000, 3001, 3);

-- History table for transactions
CREATE TABLE IF NOT EXISTS `transaction_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `user_id` varchar(50) NOT NULL,
  `type` varchar(50) NOT NULL,
  `amount` bigint(20) NOT NULL,
  `description` text,
  `status` varchar(20) DEFAULT 'completed',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
