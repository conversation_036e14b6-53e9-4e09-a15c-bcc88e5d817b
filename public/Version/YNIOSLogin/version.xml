﻿<?xml version="1.0" encoding="utf-8"?>
<Config URL="https://api.muhanoimobile.com/Resources/IOS/">
  <Application VerText="Beta 1" VerCode="********" />
  <Resource VerText="Beta 1" VerCode="********" />
  <PackageType IsMiniPack="true" />
  <AppLevelUp VerCode="22000" linkurl="" isOpen="1" msg="Phiên bản hiện tại của bạn đã cũ, bấm OK để tải phiên bản mới nhất !" />
  <Info serverip="**************" voiceserverip="yy.qj.tianmashikong.com/yy/audiochat/" pushserverip="" serverlisturl="https://api.muhanoimobile.com/ZTServerList/" serverlisturlsecond="https://api.muhanoimobile.com/ZTServerList/" verifyaccountserverip="https://api.muhanoimobile.com/" payserverip="https://www.0001.mu-atlan.mobi/" bakurl="*************/GATLogin/" versionurl="https://api.muhanoimobile.com/Version/" updateclientname="MImini_2015_3_5_15_50.apk" serverid="1" gameid="local" login="1" lang="0" isolateresid="1" roleNum="0" deleteRole="1" loginport="4402" gameport="3004" isZip="1" IsAppVerify="0" IsInner="0" />
  <Test TestURL="https://api.muhanoimobile.com/Version/TGIOSLogin/" />
</Config>