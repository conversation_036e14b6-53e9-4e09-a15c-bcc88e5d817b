<?xml version="1.0" encoding="utf-8"?>
<!--<Config URL="http://apiv20.muvn.fun/Assets1/VN/"> -->
	<Config URL="http://tlmb.gamingseavn.com/Static/Android/Assets/VN/"> 
	<Application VerText="Beta 1" VerCode="20220901" /> 
	<Resource VerText="Beta 1" VerCode="********" /> 
	<PackageType IsMiniPack="true" /> 
	<AppLevelUp VerCode="200000" linkurl="" isOpen="1" msg="Phiên bản hiện tại không phải bản mới nhất, nhấn Xác định để tải lại và vào game." /> 
	<Info 
		serverip="**************" 
		voiceserverip="yy.qj.tianmashikong.com/yy/audiochat/" 
		pushserverip="" 
		serverlisturl="http://apiv20.muvn.fun/api/Server/ZTServerList/" 
		serverlisturlsecond="http://apiv20.muvn.fun/api/Server/ZTServerList/" 
		verifyaccountserverip="http://apiv20.muvn.fun/api/Verify/" 
		payserverip="http://apiv20.muvn.fun/api/"
		bakurl="*************/GATLogin/" 
		versionurl="http://apiv20.muvn.fun/Version/" 
		updateclientname="MImini_2015_3_5_15_50.apk" 
		serverid="1" 
		gameid="local" 
		login="1" 
		lang="0" 
		isolateresid="1" 
		roleNum="0" 
		deleteRole="1" 
		loginport="4402" 
		gameport="3004" 
		isZip="1" 
		IsAppVerify="0" 
		IsInner="0" /> 
	<Test TestURL="http://apiv20.muvn.fun/Version/YNGWLogin/" /> 
</Config>