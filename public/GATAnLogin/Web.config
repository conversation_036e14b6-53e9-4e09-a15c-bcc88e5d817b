<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.web>
    <compilation debug="true" targetFramework="4.0">
      <assemblies>
        <add assembly="System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
      </assemblies>
    </compilation>
    <customErrors mode="Off" />
  </system.web>
  <appSettings>
    <add key="MD5Key" value="none" />
    <add key="ServerKey" value="uJ7FAhPOVQhplySuxQ8u" />
    <!-- uJ7FAhPOVQhplySuxQ8u -->
    <add key="ChongZhiKey" value="QXxrcTk8ndp4ncWIxeTu" />
    <!--QXxrcTk8ndp4ncWIxeTu-->
    <add key="LimitIP" value="" />
    <add key="MyIP" value="" />
    <add key="serverUrl" value="" />
    <add key="cpId" value="37252" />
    <add key="gameId" value="100001054" />
    <add key="channelId" value="1" />
    <add key="serverId" value="1" />
    <add key="apiKey" value="54768e5bfd98c526640015a7" />
    <!-- 1VENsZAKX4ZaMlv6d6m6P8DH5FafdLm9 -->
    <add key="PlatformID" value="1" />
    <add key="PlatformName" value="QMQJ" />
    <add key="LogAll" value="0" />
    <!--143:YNGW-->
    <add key="StartServerID" value="0" />
    <add key="secretKey" value="582df15de91b3f12d8e710073e43f4f8" />
    <add key="allow_reject" value="reject" />
    <!--
    动态服务器均衡参数
    参数1：总在线小于此值，直接作为推荐服务器
    参数2：总在线大于此值，不能作为推荐服务器
    参数3：(第一张图在线人数)小于此值时，作为推荐服务器
    参数4：(第一张图在线人数)大于此值时，不能作为推荐服务器
    参数5：(第一张图在线人数+第二张图在线人数)，小于此值时，作为推荐服务器
    参数6：(第一张图在线人数+第二张图在线人数)，大于此值时，不能作为推荐服务器
    参数7：(第一张图在线人数+第二张图在线人数+第三张图在线人数)，小于此值时，作为推荐服务器
    参数8：(第一张图在线人数+第二张图在线人数+第三张图在线人数)，大于此值时，不能作为推荐服务器
    -->
    <add key="DynamicServerParam" value="150,700,120,180,280,350,400,500" />
    <!--
    最大推荐服务器随机基数
    -->
    <add key="MaxRecommandNum" value="3" />
    <add key="ConnectionString" value="host=127.0.0.1; user id=root; password=******; database=vv_account; pooling=true; charset=utf8;" />
    <add key="accountEntities" value="server=127.0.0.1; userid=root; password=******; database=vv_account;" />
  </appSettings>
    <system.webServer>
        <staticContent>
            <remove fileExtension=".*" />
            <mimeMap fileExtension=".*" mimeType="application/octet-stream" />
        </staticContent>
    </system.webServer>
</configuration>