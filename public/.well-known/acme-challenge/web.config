<?xml version="1.0" encoding="UTF-8"?>
<!--
  Example web.config to allow access to extensionless json text files under <website>/.well-known/acme-challenge folder

  * map extensionless files (. wildcard) to json
  * allow all users
  * does not attempt to override static file handler from parent config
-->
<configuration>
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" />
    <staticContent>
      <mimeMap fileExtension=".*" mimeType="text/json" />
    </staticContent>
  </system.webServer>
  <system.web>
    <authorization>
      <allow users="*" />
    </authorization>
  </system.web>
</configuration>