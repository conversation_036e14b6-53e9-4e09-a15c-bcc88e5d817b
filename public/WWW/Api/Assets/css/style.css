/*
--- Basic Styling
*/

* {
    box-sizing: border-box;
}

html,
body {
    font-family: 'Montserrat', sans-serif;
    display: flex;
    width: 100%;
    height: 100%;
    background: #2c2c2c;
    justify-content: center;
    align-items: center;
    color: #2c2c2c;
}

/*
--- Checkout Panel
*/
.checkout-panel {
    display: flex;
    flex-direction: column;
    width: 900px;
    background-color: rgb(255, 255, 255);
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, .2);
    border-radius: 16px;
}

/* Panel Body */
.panel-body {
    padding: 20px 80px 0;
    flex: 1;
    min-height: 320px;
}

.title {
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 20px;
    color: #2e2e2e;
    text-align: center;
    padding-bottom: 10px;
}

/* Progress Bar */
.progress-bar {
    display: flex;
    margin-bottom: 30px;
    justify-content: space-between;
}

.step {
    box-sizing: border-box;
    position: relative;
    z-index: 1;
    display: block;
    width: 25px;
    height: 25px;
    margin-bottom: 30px;
    border: 4px solid #fff;
    border-radius: 50%;
    background-color: #efefef;
}

    .step:after {
        position: absolute;
        z-index: -1;
        top: 5px;
        left: 22px;
        width: 225px;
        height: 6px;
        content: '';
        background-color: #efefef;
    }

    .step:before {
        color: #2e2e2e;
        position: absolute;
        top: 30px;
    }

    .step:last-child:after {
        content: none;
    }

    .step.active {
        background-color: #f62f5e;
    }

        .step.active:after {
            background-color: #f62f5e;
        }

        .step.active:before {
            color: #f62f5e;
        }

        .step.active + .step {
            background-color: #f62f5e;
        }

            .step.active + .step:before {
                color: #f62f5e;
            }

    .step:nth-child(1):before {
        content: 'Details';
    }

    .step:nth-child(2):before {
        right: -40px;
        content: 'Payment';
    }

    .step:nth-child(3):before {
        right: -30px;
        content: 'Waiting';
    }

    .step:nth-child(4):before {
        right: 0;
        content: 'Finish';
    }

/* Payment Method */
.payment-method {
    display: flex;
    margin-bottom: 40px;
    justify-content: center;
    position: relative;
}

.method {
    display: flex;
    flex-direction: column;
    min-width: 220px;
    height: 122px;
    padding-top: 20px;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 5px;
    background-color: #0e0e0e;
    justify-content: center;
    align-items: center;
    margin: 0 10px;
    color: white;
    font-size: 15px;
}

.selected {
    background-color: #0e0e0e !important;
    border: 2px solid #f62f5e;
}

.method .instant {
    color: rgb(119, 219, 119);
}

.method .manual {
    color: orange;
}

.method .disabled {
    color: gray;
}

.blue-border {
    border: 1px solid rgb(110, 178, 251);
    background: rgb(48 48 48);
}

.card-logos {
    display: flex;
    width: 150px;
    justify-content: space-between;
    align-items: center;
}

.radio-input {
    margin-top: 15px;
}

input[type='radio'] {
    display: inline-block;
}


/* Input Fields */
.input-fields {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

    .input-fields label {
        display: block;
        margin-bottom: 10px;
        color: #b4b4b4;
    }

.warning {
    border-color: #f62f5e !important;
}

.info {
    font-size: 12px;
    font-weight: 300;
    display: block;
    margin-top: 24px;
    opacity: .5;
    color: #2e2e2e;
}

div[class*='column'] {
    width: 360px;
}

input[type='text'],
input[type='password'] {
    font-size: 16px;
    width: 100%;
    height: 50px;
    padding-right: 40px;
    padding-left: 16px;
    color: rgba(46, 46, 46, .8);
    border: 1px solid rgb(225, 225, 225);
    border-radius: 4px;
    outline: none;
}

    input[type='text']:focus,
    input[type='password']:focus {
        border-color: rgb(119, 219, 119);
    }

#date {
    background: url(img/icons_calendar_black.png) no-repeat 90%;
}

#cardholder {
    background: url(img/icons_person_black.png) no-repeat 95%;
}

#cardnumber {
    background: url(img/icons_card_black.png) no-repeat 95%;
}

#verification {
    background: url(img/icons_lock_black.png) no-repeat 90%;
}

.small-inputs {
    display: flex;
    margin-top: 20px;
    justify-content: space-between;
}

    .small-inputs div {
        width: 182px;
    }

/* Panel Footer */
.panel-footer {
    display: flex;
    width: 100%;
    height: 86px;
    padding: 0 80px;
    /*background-color: rgb(239, 239, 239);*/
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid rgba(0, 0, 0, .2);
}

/* Buttons */
.btn {
    font-size: 16px;
    width: 180px;
    height: 48px;
    cursor: pointer;
    transition: all .2s ease-in-out;
    letter-spacing: 1px;
    border: none;
    border-radius: 23px;
}

.back-btn {
    color: #f62f5e;
    background: #fff;
}

.next-btn {
    color: #fff;
    background: #f62f5e;
}

.next-btn, .back-btn {
    line-height: 46px;
    text-align: center;
    text-decoration: none;
    border: 1px solid #f62f5e;
}

.btn:focus {
    outline: none;
}

.btn:hover {
    transform: scale(1.1);
}

.accountnum-label {
    margin-top: 16px;
}

.accountnum {
    font-size: 1.5em;
    color: #2c2c2c;
    font-weight: 600;
}

em {
    font-weight: 600;
    color: #f62f5e;
}

/*Loader*/

#loader-wrapper {
    position: absolute;
    top: 56px;
    left: 0;
    width: 100%;
    height: 100%;
}

#loader {
    display: block;
    position: relative;
    left: 50%;
    top: 50%;
    width: 150px;
    height: 150px;
    margin: -75px 0 0 -75px;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: #3498db;
    -webkit-animation: spin 2s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
    animation: spin 2s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
}

    #loader:before {
        content: "";
        position: absolute;
        top: 5px;
        left: 5px;
        right: 5px;
        bottom: 5px;
        border-radius: 50%;
        border: 3px solid transparent;
        border-top-color: #e74c3c;
        -webkit-animation: spin 3s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
        animation: spin 3s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
    }

    #loader:after {
        content: "";
        position: absolute;
        top: 15px;
        left: 15px;
        right: 15px;
        bottom: 15px;
        border-radius: 50%;
        border: 3px solid transparent;
        border-top-color: #f9c922;
        -webkit-animation: spin 1.5s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
        animation: spin 1.5s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
    }

@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(0deg); /* IE 9 */
        transform: rotate(0deg); /* Firefox 16+, IE 10+, Opera */
    }

    100% {
        -webkit-transform: rotate(360deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg); /* IE 9 */
        transform: rotate(360deg); /* Firefox 16+, IE 10+, Opera */
    }
}

@keyframes spin {
    0% {
        -webkit-transform: rotate(0deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(0deg); /* IE 9 */
        transform: rotate(0deg); /* Firefox 16+, IE 10+, Opera */
    }

    100% {
        -webkit-transform: rotate(360deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg); /* IE 9 */
        transform: rotate(360deg); /* Firefox 16+, IE 10+, Opera */
    }
}


/* Additions */
/* =========================================================== */
.cart {
    margin: 0;
    background: #f7f7f7;
    border-radius: 10px;
    padding: 16px;
}

    .cart .media {
        /*width: 15%;*/
    }

        .cart .media img {
            max-width: 100px;
        }

    .cart .details {
        flex-grow: 1;
        padding: 10px;
    }

        .cart .details h3 {
            margin: 0 0 10px;
        }

        .cart .details p {
            margin: 0 0 5px;
            color: #626262;
        }

        .cart .details small {
            color: #cf8546;
            font-size: 12px;
            padding-right: 8px;
        }

            .cart .details small b {
                font-weight: 600;
            }

    .cart .price {
        /*width: 15%;*/
        text-align: right;
        font-weight: 700;
        align-self: center;
        color: #f62f5e;
    }



/*Success icon*/
.svg {
    position: relative;
    left: 50%;
    top: 50%;
    width: 150px;
    height: 150px;
    margin: -25px 0 0 -75px;
    border-radius: 50%;
    border: 3px solid transparent;
}

.circle {
    fill: none;
    stroke: #f62f5e;
    stroke-width: 3;
    stroke-linecap: round;
    stroke-miterlimit: 10;
}

.circle-dash {
    fill: none;
    stroke: #f62f5e;
    stroke-width: 3;
    stroke-linecap: round;
    stroke-miterlimit: 10;
}

.check {
    fill: none;
    stroke: #f62f5e;
    stroke-width: 3;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke-miterlimit: 10;
}

.check-dash {
    fill: none;
    stroke: #f62f5e;
    stroke-width: 3;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke-miterlimit: 10;
}

.check {
    stroke-dasharray: 60 100;
    animation: check 1.2s cubic-bezier(0.5, 0, 0.6, 1) forwards 0.15s;
    -webkit-animation: check 1.2s cubic-bezier(0.5, 0, 0.6, 1) forwards 0.15s;
    -moz-animation: check 1.2s cubic-bezier(0.5, 0, 0.6, 1) forwards 0.15s;
    -o-animation: check 1.2s cubic-bezier(0.5, 0, 0.6, 1) forwards 0.15s;
    opacity: 0;
}

@-webkit-keyframes check {
    from {
        stroke-dashoffset: 60;
        opacity: 1;
    }

    to {
        stroke-dashoffset: 00;
        opacity: 1;
    }
}

@-moz-keyframes check {
    from {
        stroke-dashoffset: 60;
        opacity: 1;
    }

    to {
        stroke-dashoffset: 00;
        opacity: 1;
    }
}

@keyframes check {
    from {
        stroke-dashoffset: 60;
        opacity: 1;
    }

    to {
        stroke-dashoffset: 00;
        opacity: 1;
    }
}

.check-dash {
    stroke-dasharray: 10 100;
    animation: check-dash 1.2s cubic-bezier(0.5, 0, 0.6, 1) forwards;
    -webkit-animation: check-dash 1.2s cubic-bezier(0.5, 0, 0.6, 1) forwards;
    -moz-animation: check-dash 1.2s cubic-bezier(0.5, 0, 0.6, 1) forwards;
    -o-animation: check-dash 1.2s cubic-bezier(0.5, 0, 0.6, 1) forwards;
}

@-webkit-keyframes check-dash {
    from {
        stroke-dashoffset: 120;
    }

    to {
        stroke-dashoffset: 45;
    }
}

@-moz-keyframes check-dash {
    from {
        stroke-dashoffset: 120;
    }

    to {
        stroke-dashoffset: 45;
    }
}

@keyframes check-dash {
    from {
        stroke-dashoffset: 120;
    }

    to {
        stroke-dashoffset: 45;
    }
}

.circle {
    stroke-dasharray: 300 300;
    animation: circle 1.5s cubic-bezier(0.5, 0, 0.5, 1) forwards 0.15s;
    -webkit-animation: circle 1.5s cubic-bezier(0.5, 0, 0.5, 1) forwards 0.15s;
    -moz-animation: circle 1.5s cubic-bezier(0.5, 0, 0.5, 1) forwards 0.15s;
    -o-animation: circle 1.5s cubic-bezier(0.5, 0, 0.5, 1) forwards 0.15s;
    opacity: 0;
}

@-webkit-keyframes circle {
    from {
        stroke-dashoffset: 300;
        opacity: 1;
    }

    to {
        stroke-dashoffset: 0;
        opacity: 1;
    }
}

@-moz-keyframes circle {
    from {
        stroke-dashoffset: 300;
        opacity: 1;
    }

    to {
        stroke-dashoffset: 0;
        opacity: 1;
    }
}

@keyframes circle {
    from {
        stroke-dashoffset: 300;
        opacity: 1;
    }

    to {
        stroke-dashoffset: 0;
        opacity: 1;
    }
}

.circle-dash {
    stroke-dasharray: 10 300;
    animation: circledash 1.5s cubic-bezier(0.5, 0, 0.5, 1) forwards 0.05s;
    -webkit-animation: circledash 1.5s cubic-bezier(0.5, 0, 0.5, 1) forwards 0.05s;
    -moz-animation: circledash 1.5s cubic-bezier(0.5, 0, 0.5, 1) forwards 0.05s;
    -o-animation: circledash 1.5s cubic-bezier(0.5, 0, 0.5, 1) forwards 0.05s;
    opacity: 0;
}

@-webkit-keyframes circledash {
    from {
        stroke-dashoffset: 320;
        opacity: 1;
    }

    to {
        stroke-dashoffset: 20;
        opacity: 1;
    }
}

@-moz-keyframes circledash {
    from {
        stroke-dashoffset: 320;
        opacity: 1;
    }

    to {
        stroke-dashoffset: 20;
        opacity: 1;
    }
}

@keyframes circledash {
    from {
        stroke-dashoffset: 320;
        opacity: 1;
    }

    to {
        stroke-dashoffset: 20;
        opacity: 1;
    }
}
