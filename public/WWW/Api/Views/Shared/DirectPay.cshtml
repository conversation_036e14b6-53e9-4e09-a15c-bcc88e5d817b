﻿@model DirectPayModel
@{

    var step = (int)Model.Step;
    var price = @Model.Package?.Price.ToString("C", System.Globalization.CultureInfo.GetCultureInfo("en-us"));
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Checkout</title>
    <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700" rel="stylesheet">
    <link rel="stylesheet" href="~/Assets/css/style.css">
    <meta name="robots" content="noindex,follow" />
</head>
<body>
    <form class="checkout-panel" method="post">
        <div class="panel-body">
            <h2 class="title">Checkout</h2>

            <div class="progress-bar">
                <div class="step @(step>=1?"active":"")"></div>
                <div class="step @(step>=2?"active":"")"></div>
                <div class="step @(step>=3?"active":"")"></div>
                <div class="step @(step>=3?"active":"")"></div>
            </div>

            @switch (Model.Step)
            {
                case DirectPayModel.Steps.Pending:
                    {
                        <div class="input-fields cart" id="step-1">
                            <div class="media">
                                <img class="thumbnail" src="@Url.Content($"~/Assets/img/chongzhi/{Model.Package.ChongZhiId}.png")" alt="@Model.Package.Name"
                                     onerror="this.src='@Url.Content("~/Assets/img/chongzhi.png")';this.onerror='';" />
                            </div>
                            <div class="details">
                                <h3>@Model.Package.Name</h3>
                                <p> @Model.Package.Description</p>
                                @foreach (var item in Model.Package.Items)
                                {
                                    <small><b>@item.Qty</b> @item.Name</small>
                                }
                            </div>
                            <span class="price">@price</span>
                        </div>
                        <div class="payment-method" id="step-2" style="display:none">
                            @if (Model.Providers == null || !Model.Providers.Any())
                            {
                                <span>No payment provider available, please try again later</span>
                            }
                            else
                            {
                                foreach (var provider in Model.Providers)
                                {
                                    <label for="@provider.PID" class="method">
                                        <img src="@Url.Content($"~/Assets/img/payment/{provider.PID}.png")" onerror="this.src='@Url.Content("~/Assets/img/payment.png")';this.onerror='';" />
                                        <div class="radio-input">
                                            <input id="@provider.PID" type="radio" name="provider" value="@provider.PID">
                                            @provider.Title <span class="instant">(Instant)</span>
                                        </div>
                                    </label>
                                }
                            }
                        </div>
                        break;
                    }
                case DirectPayModel.Steps.Waiting:
                    {
                        <div class="payment-method" style="justify-content: space-between;">
                            <div id="loader-wrapper">
                                <div id="loader"></div>
                            </div>
                            <span>
                                Redirecting... please wait!
                            </span>
                        </div>
                        break;
                    }
                case DirectPayModel.Steps.Unknown:
                case DirectPayModel.Steps.Completed:
                    {
                        <span>Checkout successfull...</span>
                        <div class="loader-wrapper">
                            <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 100 100" style="enable-background:new 0 0 100 100;display: block;" xml:space="preserve" preserveAspectRatio="xMidYMid meet" class="svg">
                            <style type="text/css"></style>
                            <g>
                            <circle class="circle" cx="50" cy="49.9999657" r="47.5" />
                            <circle class="circle-dash" cx="50" cy="49.9999657" r="47.5" />
                            <polyline class="check" points="28.6469955,52.0561066 42.2152748,65.6243896 71.3530045,36.4866562   " />
                            <polyline class="check-dash" points="28.6469955,52.0561066 42.2152748,65.6243896 71.3530045,36.4866562  " />
                                </g>
                            </svg>
                        </div>
                        break;
                    }
            }



        </div>
        <div class="panel-footer">
            @switch (Model.Step)
            {
                case DirectPayModel.Steps.Pending:
                    <button type="button" class="btn back-btn" onclick="closePage()">Close</button>
                    <button type="button" class="btn next-btn" id="btn-checkout">Checkout</button>
                    <button type="submit" class="btn next-btn" id="btn-paynow" style="display:none">Continue</button>
                    break;
                case DirectPayModel.Steps.Waiting:
                    <button type="button" class="btn back-btn" onclick="closePage()">Close</button>
                    <a href="@Model.Redirect" class="btn next-btn" id="redirect">Pay Now</a>
                    break;
                case DirectPayModel.Steps.Unknown:
                case DirectPayModel.Steps.Completed:
                default:
                    <button type="button" style="width: 100%;" class="btn next-btn" onclick="closePage()">Close</button>
                    break;

            }
        </div>
    </form>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.1.1/jquery.min.js"></script>
    <script src="~/Assets/js/script.js"></script>
    <script type="text/javascript">
        $('#btn-checkout').on('click', function(){
            $(this).hide();
            $('#step-1').hide();
            $('#step-2').show();
            $('#btn-paynow').show();
        })
        $('#btn-paynow').click(function(){
            if (!$('input[name=provider]').length)
            {
                alert("No payment provider available, please try again later");

                return false;
            }
            if (!$('input[name=provider]:checked').length)
            {
                alert("Select payment provider!");

                return false;
            }
        });

        var deviceType = '@(this.Context.Request.GetQueryData("deviceType", "android"))';

        function closePage() {
            if (deviceType == 'android') {
                window.js2java.kunlunClose();
            }
            else if (deviceType == 'ios') {
                document.location = 'js-oc_kunlunClose_null.html';
            }
            else {
                window.js2java.kunlunClose();
            }
        }

    </script>
    @*
        @if (!string.IsNullOrEmpty(Model.Redirect))
        {
            <script type="text/javascript">
                $(document).ready(function (){
                    $('#redirect')[0].click();
                });
            </script>
        }*@
</body>
</html>