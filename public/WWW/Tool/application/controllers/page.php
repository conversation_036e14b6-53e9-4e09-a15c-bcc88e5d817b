<?php
defined('BASEPATH') OR exit('No direct script access allowed');
date_default_timezone_set('Asia/Ho_Chi_Minh');
set_time_limit(5000000);
class Page extends CI_Controller {

	public function index()
	{
		$userdata = $this->session->userdata('username');
		if($userdata == null)
		{
			$this->load->view('login');
		}
		else
		{
			$transactions = $this->default_model->quantri_getTransactions($userdata);
			$data = array(
				'transactions'=>$transactions,
				'content'=>'charge'
			);
			$this->load->view('main',$data);
		}
	}

	public function login()
	{
		$userdata = $this->session->userdata('username');
		if($userdata == null)
		{
			$this->load->view('login');
		}
		else
		{
			redirect('index');
		}
	}

	public function login_process()
	{
		$username = $this->input->post("username");
		$password = $this->input->post("password");
		$qtv = $this->default_model->sys_checkLogin($username, $password);
		if($qtv != null)
		{
			$this->session->set_userdata('username',strtolower($username));
			redirect('index');
		}
		else
		{
			$this->session->set_flashdata('error','Tên truy cập hoặc mật khẩu không đúng !');
			redirect('login');
		}
		
	}

	public function create_user()
	{
		$userdata = $this->session->userdata('username');
		if($userdata == null)
		{
			$this->load->view('login');
		}
		else
		{
			$data = array(
				'content'=>'create_user'
			);
			$this->load->view('main',$data);
		}
	}

	public function report()
	{
		$userdata = $this->session->userdata('username');
		if($userdata == null)
		{
			$this->load->view('login');
		}
		else
		{
			$reports = $this->default_model->getReport(4, null, null, null);
			$data = array(
				'content'=>'report',
				'reports'=>$reports,
				'today'=>1
			);
			$this->load->view('main',$data);
		}
	}

	public function merge_server()
	{
		$userdata = $this->session->userdata('username');
		if($userdata == null)
		{
			$this->load->view('login');
		}
		else
		{
			$serverList = $this->default_model->getServerList();
			$data = array(
				'content'=>'merge_server',
				'serverList'=>$serverList
			);
			$this->load->view('main',$data);
		}
	}


	public function top_bonus()
	{
		$userdata = $this->session->userdata('username');
		if($userdata == null)
		{
			$this->load->view('login');
		}
		else
		{
			$serverList = $this->default_model->getServerList();
			$data = array(
				'content'=>'top_bonus',
				'serverList'=>$serverList
			);
			$this->load->view('main',$data);
		}
	}

	public function topbonus_process()
	{
		$server = $this->input->post('server');
		$serverData = $this->default_model->getServer($server);
		$topCombatRoles = $this->server_model->getTop10Combatforce($serverData['DatabaseName']);
		$topLevelRoles = $this->server_model->getTop10Level($serverData['DatabaseName']);
		echo "TOP LC S".$server.": </br>";
        $i = 0;
        foreach($topCombatRoles as $roledata)
        {
            $rid = $roledata['rid'];
            $rname = $roledata['rname'];
            echo $rname."</br>";
            if($i == 0)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 1 Lực Chiến","Chúc mừng bạn đã dành giải thưởng top 1 Lực Chiến", "50056,1,0,0,0|1030914,1,0,0,95158272|");
            }
            else if($i == 1)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 2 Lực Chiến","Chúc mừng bạn đã dành giải thưởng top 2 Lực Chiến", "50055,1,0,0,0|1030913,1,0,0,158597120|");
            }
            else if($i == 2)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 3 Lực Chiến","Chúc mừng bạn đã dành giải thưởng top 3 Lực Chiến", "50054,3,0,0,0|1030912,1,0,0,441581568|");
            }
            else if($i == 3)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 4 Lực Chiến","Chúc mừng bạn đã dành giải thưởng top 4 Lực Chiến", "50054,2,0,0,0|1030909,1,0,0,95158272|");
            }
            else if($i == 4)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 5 Lực Chiến","Chúc mừng bạn đã dành giải thưởng top 5 Lực Chiến", "50054,2,0,0,0|1030909,1,0,0,95158272|");
            }
            else if($i == 5)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 6 Lực Chiến","Chúc mừng bạn đã dành giải thưởng top 6 Lực Chiến", "50054,1,0,0,0|");
            }
            else if($i == 6)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 7 Lực Chiến","Chúc mừng bạn đã dành giải thưởng top 7 Lực Chiến", "50054,1,0,0,0|");
            }
            else if($i == 7)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 8 Lực Chiến","Chúc mừng bạn đã dành giải thưởng top 8 Lực Chiến", "50054,1,0,0,0|");
            }
            else if($i == 8)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 9 Lực Chiến","Chúc mừng bạn đã dành giải thưởng top 9 Lực Chiến", "50054,1,0,0,0|");
            }
            else if($i == 9)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 10 Lực Chiến","Chúc mừng bạn đã dành giải thưởng top 10 Lực Chiến", "50054,1,0,0,0|");
            }
            $i++;
        }
        echo "TOP LV S".$server.": </br>";

        $j = 0;
        foreach($topLevelRoles as $roledata)
        {
            $rid = $roledata['rid'];
            $rname = $roledata['rname'];
			echo $rname."</br>";
            if($j == 0)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 1 Cấp Độ","Chúc mừng bạn đã dành giải thưởng top 1 cấp độ", "50056,1,0,0,0|1032209,1,1,0,0|");
            }
            else if($j == 1)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 2 Cấp Độ","Chúc mừng bạn đã dành giải thưởng top 2 cấp độ", "50055,1,0,0,0|1032208,1,1,0,0|");
            }
            else if($j == 2)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 3 Cấp Độ","Chúc mừng bạn đã dành giải thưởng top 3 cấp độ", "50054,3,0,0,0|1032207,1,1,0,0|");
            }
            else if($j == 3)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 4 Cấp Độ","Chúc mừng bạn đã dành giải thưởng top 4 cấp độ", "50054,2,0,0,0|1032206,1,1,0,0|");
            }
            else if($j == 4)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 5 Cấp Độ","Chúc mừng bạn đã dành giải thưởng top 5 cấp độ", "50054,2,0,0,0|1032206,1,1,0,0|");
            }
            else if($j == 5)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 6 Cấp Độ","Chúc mừng bạn đã dành giải thưởng top 6 cấp độ", "50054,1,0,0,0|");
            }
            else if($j == 6)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 7 Cấp Độ","Chúc mừng bạn đã dành giải thưởng top 7 cấp độ", "50054,1,0,0,0|");
            }
            else if($j == 7)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 8 Cấp Độ","Chúc mừng bạn đã dành giải thưởng top 8 cấp độ", "50054,1,0,0,0|");
            }
            else if($j == 8)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 9 Cấp Độ","Chúc mừng bạn đã dành giải thưởng top 9 cấp độ", "50054,1,0,0,0|");
            }
            else if($j == 9)
            {
            	$this->sendMailGood($serverData['DatabaseName'], $rid, "Thưởng TOP 10 Cấp Độ","Chúc mừng bạn đã dành giải thưởng top 10 cấp độ", "50054,1,0,0,0|");
            }
            $j++;
        }
        echo "Done !";
	}
	
	public function top_rolan()
	{
		$userdata = $this->session->userdata('username');
		if($userdata == null)
		{
			$this->load->view('login');
		}
		else
		{
			$serverList = $this->default_model->getServerList();
			$data = array(
				'content'=>'top_rolan',
				'serverList'=>$serverList
			);
			$this->load->view('main',$data);
		}
	}
	

public function toprolan_process()
{
    $server = $this->input->post('server');
    $serverData = $this->default_model->getServer($server);
    
    // Lấy danh sách tất cả các thành viên guild từ mô hình server
    $allMemberRolan = $this->server_model->getAllMemberGuild($serverData['DatabaseName']);
    
    // Hiển thị tiêu đề
    echo "Rolan Win S".$server.": <br>";

    // Duyệt qua từng thành viên và hiển thị tên
    foreach ($allMemberRolan as $memberRolan) {
        $rid = $memberRolan['rid'];
        $rname = $memberRolan['rname'];
        echo $rname . "<br>";

        // Gửi email chúc mừng
        $this->sendMailGood($serverData['DatabaseName'], $rid, "Rolan Win", "Chúc mừng bạn đã giành giải thưởng Rolan", "302722807,1,1,0,0|50056,10,0,0,0|");
    }
    
    // Hiển thị thông báo khi hoàn thành
    echo "TOP Rolan done ".$server.": <br>";
}

	public function kiemtra_thienphu()
	{
		$userdata = $this->session->userdata('username');
		if($userdata == null)
		{
			$this->load->view('login');
		}
		else
		{
			$serverList = $this->default_model->getServerList();
			$data = array(
				'content'=>'kiemtra_thienphu',
				'serverList'=>$serverList
			);
			$this->load->view('main',$data);
		}
	}
	
	public function kiemtra_thienphu_process()
	{
		$server = $this->input->post('server');
		$serverData = $this->default_model->getServer($server);
		$allThienPhu = $this->server_model->getMemberGuildWithLogValueDiscrepancies($serverData['DatabaseName']);
		echo "Những tài khoản có vấn đề ở Server ".$server.": </br>";

		echo "<style>";
			echo "table {
				border-collapse: collapse;
				width: 100%; /* Adjust width as needed */
			}";
			echo "th, td {
				border: 1px solid #ccc;
				padding: 5px;
			}";
			echo "</style>";
			
			echo "<table>";
			echo "<thead>";
			echo "<tr>";
			echo "<th>Rid</th>";
			echo "<th>UserID</th>";
			echo "<th>Username</th>";
			echo "<th>Tên nhân vật</th>";
			echo "<th>Level thiên phú (tatalCount - t_talent)</th>";
			echo "<th>Tổng exp thực tế (LogValue - t_talent_log)</th>";
			echo "<th>Time (t_talent_log)</th>";
			echo "</tr>";
			echo "</thead>";
			echo "<tbody>";
        foreach($allThienPhu as $thienphu)
        {
            $rid = $thienphu['rid'];
            $rname = $thienphu['rname'];
            $userid = $thienphu['userid'];
            $username = $thienphu['username'];
			$levelThienPhu = $this->server_model->getLevelThienPhu($serverData['DatabaseName'], $rid);
			$expReal = $this->server_model->getExpReal($serverData['DatabaseName'], $rid);
			$timeReal = $this->server_model->getTimeReal($serverData['DatabaseName'], $rid);
			
            
			
			echo "<tr>";
			echo "<td>" . $rid . "</td>";
			echo "<td>" . $userid . "</td>";
			echo "<td>" . $username . "</td>";
			echo "<td>" . $rname . "</td>";
			echo "<td>" . $levelThienPhu . "</td>";
			echo "<td>" . $expReal . "</td>";
			echo "<td>" . $timeReal . "</td>";
			echo "</tr>";
			
        }
		echo "</tbody>";
			echo "</table>";
        echo "Kiểm tra thiên phú server: ".$server." XONG! </br>";
	}
	
	public function sendMailGood($serverDB, $rid, $title, $message, $goods)
	{
		$goodlist = explode('|', $goods);
		$role = $this->server_model->getRole($serverDB, $rid);
		$mail_id = $this->server_model->insertMail($serverDB, "Hệ Thống", $rid, $role['rname'], $title, $message);
		for($i = 0; $i < count($goodlist); $i++)
		{
			$good = $goodlist[$i];
			if($good == "")
			{
				continue;
			}
			$good_props = explode(',', $good);
			$good_id = $good_props[0];
			$good_count = $good_props[1];
			$good_binding = $good_props[2];
			$luck = $good_props[3];
			$good_excellenceinfo = $good_props[4];
			$this->server_model->insertMailGoods($serverDB, $mail_id, $good_id, $good_count, $good_binding, $luck, $good_excellenceinfo);
		}
	}

	public function report_process()
	{
		$userdata = $this->session->userdata('username');
		if($userdata == null)
		{
			$this->load->view('login');
		}
		else
		{
			$username = $this->input->post('username');
			$from_date = $this->input->post('from_date');
			$to_date = $this->input->post('to_date');
			$reports = $this->default_model->getReport(4, $username, $from_date, $to_date);
			$this->session->set_flashdata('reports',$reports);
			$data = array(
				'content'=>'report',
				'reports'=>$reports
			);
			if($from_date != null)
			{
				$data['today'] = 0;
				$data['from_date'] = $from_date;
				$data['to_date'] = $to_date;
			}
			$this->load->view('main',$data);
		}
	}

	public function merge_process()
	{
		$from_server = $this->input->post('from-server');
		$to_server = $this->input->post('to-server');
		$fromServerData = $this->default_model->getServer($from_server);
		$toServerData = $this->default_model->getServer($to_server);
		//echo $fromServerData['DatabaseName']."|".$toServerData['DatabaseName']."|".$from_server."|".$to_server;
		$this->server_model->updateZoneID($fromServerData['DatabaseName'], $toServerData['DatabaseName'], $from_server, $to_server);
	}

	public function send_good()
	{
		$userdata = $this->session->userdata('username');
		if($userdata == null)
		{
			$this->load->view('login');
		}
		else
		{
			$serverList = $this->default_model->getServerList();
			$data = array(
				'serverList'=>$serverList,
				'content'=>'send_good'
			);
			$this->load->view('main',$data);
		}
	}

	public function send_good_process()
	{
		$userdata = $this->session->userdata('username');
		if($userdata == null)
		{
			$this->load->view('login');
		}
		else
		{
			$server = $this->input->post('server');
			$goods = $this->input->post('goodid');
			$rid = $this->input->post('rid');
			$title = $this->input->post('title');
			$message = $this->input->post('message');
			$goodlist = explode('|', $goods);
			$serverData = $this->default_model->getServer($server);
			$role = $this->server_model->getRole($serverData['DatabaseName'], $rid);
			$mail_id = $this->server_model->insertMail($serverData['DatabaseName'], "Hệ Thống", $rid, $role['rname'], $title, $message);
			for($i = 0; $i < count($goodlist); $i++)
			{
				$good = $goodlist[$i];
				if($good == "")
				{
					continue;
				}
				$good_props = explode(',', $good);
				$good_id = $good_props[0];
				$good_count = $good_props[1];
				$good_binding = $good_props[2];
				$luck = $good_props[3];
				$good_excellenceinfo = $good_props[4];
				$this->server_model->insertMailGoods($serverData['DatabaseName'], $mail_id, $good_id, $good_count, $good_binding, $luck, $good_excellenceinfo);
			}
			$this->session->set_flashdata('success', 'Send successfully.');
			redirect('send_good');
		}
	}

	public function create_user_process()
	{
		$userdata = $this->session->userdata('username');
		if($userdata == null)
		{
			$this->load->view('login');
		}
		else
		{
			$sys_username = $this->session->userdata('username');
			$qtv_username = $this->input->post("username");
			$qtv_password = $this->input->post("password");
			if($qtv_username == null || $qtv_username == "")
			{
				$this->session->set_flashdata('error', 'Vui lòng nhập tên tài khoản của CTV');
				redirect('create_user');
			}
			if($qtv_password == null || $qtv_password == "")
			{
				$this->session->set_flashdata('error', 'Vui lòng nhập mật khẩu CTV');
				redirect('create_user');
			}
			$qtvUser = $this->default_model->getQTUser($qtv_username);
			if($qtvUser != null)
			{
				$this->session->set_flashdata('error', 'Tài khoản '.$qtv_username.' đã tồn tại, vui lòng tạo một tài khoản khác');
				redirect('create_user');
			}
			$this->default_model->quantri_insertUser($qtv_username, $qtv_password);
			$this->session->set_flashdata('success', 'Bạn đã tạo thành công tài khoản '.$qtv_username);
			redirect('create_user');
		}
	}

	public function charge_process()
	{
		$sys_username = $this->session->userdata('username');
		$quantri_username = $this->input->post("username");
		$amount = $this->input->post("quantity");
		if($quantri_username == null || $quantri_username == "")
		{
			$this->session->set_flashdata('error', 'Vui lòng nhập tài khoản QTV !');
			redirect('index');
		}
		if($amount == null || $amount == "" || $amount == 0)
		{
			$this->session->set_flashdata('error', 'Vui lòng nhập số kim cương cần nạp !');
			redirect('index');
		}
		$this->default_model->quantri_increaseBalance($quantri_username, $amount);
		$balance = $this->default_model->quantri_getBalance($quantri_username);
		$this->default_model->ctv_insertTransLog(5, $sys_username, $quantri_username, NULL, NULL, NULL, $amount, $balance);
		$this->session->set_flashdata('success', 'Đã nạp thành công '.$amount.' kim cương cho tài khoản '.$quantri_username);
		redirect('index');
	}

	public function get_user()
	{
		$q = $this->input->post('q');
		$data = $this->default_model->queryUser($q);
		echo "<ul id=\"country-list\">";
		foreach($data as $user) {
			echo "<li style=\"padding-bottom: 10px;\" onClick=\"selectUser('".$user["username"]."');\">".$user["username"]."</li>";
		}
		echo "</ul>";
		//echo json_encode($data);
	}

	public function get_quantri_user()
	{
		$q = $this->input->post('q');
		$data = $this->default_model->queryQTUser($q);
		echo "<ul id=\"country-list\">";
		foreach($data as $user) {
			echo "<li style=\"padding-bottom: 10px;\" onClick=\"selectUser('".$user["username"]."');\">".$user["username"]."</li>";
		}
		echo "</ul>";
		//echo json_encode($data);
	}

	public function get_qtv_user()
	{
		$q = $this->input->post('q');
		$data = $this->default_model->queryQTVUser($q);
		echo "<ul id=\"country-list\">";
		foreach($data as $user) {
			echo "<li style=\"padding-bottom: 10px;\" onClick=\"selectUser('".$user["username"]."');\">".$user["username"]."</li>";
		}
		echo "</ul>";
		//echo json_encode($data);
	}

	public function get_ctv_user()
	{
		$q = $this->input->post('q');
		$data = $this->default_model->queryCTVUser($q);
		echo "<ul id=\"country-list\">";
		foreach($data as $user) {
			echo "<li style=\"padding-bottom: 10px;\" onClick=\"selectUser('".$user["username"]."');\">".$user["username"]."</li>";
		}
		echo "</ul>";
		//echo json_encode($data);
	}

	public function logout()
	{
		$this->session->sess_destroy();
		redirect('login');
	}
	public function code() {
		$userdata = $this->session->userdata('username');
		if($userdata == null)
		{
			$this->load->view('login');
		}
		else
		{
			$serverList = $this->default_model->getServerList();
			$data = array(
				'serverList'=>$serverList,
				'content'=>'create_code'
			);
			$this->load->view('main',$data);
		}
	}
}
