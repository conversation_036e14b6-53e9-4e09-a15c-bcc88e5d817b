<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Default_model extends CI_Model {

	public function __construct()
    {
        parent::__construct();
        $this->load->database('default');
    }

	public function checkPassword($username, $password)
    {
    	$this->db->where('username',$username);
    	$this->db->where('userpwd',md5($password));
    	$query = $this->db->get('t_users');
    	$row = $query->row_array();
    	return $row;
    }

    public function getUser($username)
    {
        $this->db->where('username',$username);
        $query = $this->db->get('t_users');
        $row = $query->row_array();
        return $row;
    }

    public function queryUser($str)
    {
        $this->db->select('username');
        $this->db->like('username',$str,'after');
        $query = $this->db->get('qtv_users', 10);
        $result = $query->result_array();
        return $result;
    }

    public function queryCTVUser($str)
    {
        $this->db->select('username');
        $this->db->like('username',$str,'after');
        $query = $this->db->get('ctv_users', 10);
        $result = $query->result_array();
        return $result;
    }

    public function getReport($type, $username, $from_date, $to_date)
    {
        if($from_date == null)
        {
            $from_date = date("Y-m-d");
        }
        if($to_date == null)
        {
            $to_date = date("Y-m-d");
        }
        $query_string = "SELECT SUM(amount) kimcuong, TRUNCATE(SUM(amount)/6500*100000*0.7,0) sotien, ctv_username username FROM ctv_transactions WHERE 1=1";
        $query_string .= " AND TYPE = $type";
        if($username != null)
        {
            $query_string .= " AND ctv_username = '$username'";
        }
        $query_string .= " AND ctv_username NOT IN ('Accnap','banknap')";
        $query_string .= " AND created_time > '$from_date 00:00:00' AND created_time < '$to_date 23:59:59'";
        $query_string .= " GROUP BY ctv_username ORDER BY sotien DESC";
        $query = $this->db->query($query_string);
        return $query->result_array();
    }

    public function queryQTUser($str)
    {
        $this->db->select('username');
        $this->db->like('username',$str,'after');
        $query = $this->db->get('quantri_users', 10);
        $result = $query->result_array();
        return $result;
    }

    public function getServerList()
    {
        $this->db->select('Id');
        $this->db->select('ServerName');
        $query = $this->db->get('t_serverdata');
        $result = $query->result_array();
        return $result;
    }

    public function getServer($server)
    {
        $this->db->where('Id',$server);
        $query = $this->db->get('t_serverdata');
        return $query->row_array();
    }

    public function checkPhone($phone)
    {
        $this->db->where('phone',$phone);
        $query = $this->db->get('t_users');
        $row = $query->row_array();
        return $row;
    }

    public function checkEmail($email)
    {
        $this->db->where('email',$email);
        $query = $this->db->get('t_users');
        $row = $query->row_array();
        return $row;
    }

    public function updatePhoneEmail($username, $phone, $email)
    {
        $data = array(
        'phone'=>$phone,
        'email'=>$email
        );
        $this->db->where('username',$username);
        $this->db->update('t_users', $data);
    }

    public function updatePassword($username, $password)
    {
        $data = array(
            'userpwd'=>md5($password)
        );
        $this->db->where('username',$username);
        $this->db->update('t_users', $data);
    }

    public function getOrder($order_id)
    {
        $this->db->where('order_no',$order_id);
        $query = $this->db->get('t_plat_chongzhi');
        $row = $query->row_array();
        return $row;
    }

    public function getHistory($username)
    {
        $this->db->where('uid',$username);
        $query = $this->db->get('t_tranlog');
        $result = $query->result_array();
        return $result;
    }

    public function qtv_checkLogin($username, $password)
    {
        $this->db->where('username',$username);
        $this->db->where('password',$password);
        $query = $this->db->get('qtv_users');
        $row = $query->row_array();
        return $row;
    }

    public function quantri_checkLogin($username, $password)
    {
        $this->db->where('username',$username);
        $this->db->where('password',$password);
        $query = $this->db->get('quantri_users');
        $row = $query->row_array();
        return $row;
    }

    public function sys_checkLogin($username, $password)
    {
        $this->db->where('username',$username);
        $this->db->where('password',$password);
        $query = $this->db->get('sys_users');
        $row = $query->row_array();
        return $row;
    }

    public function ctv_getBalance($username)
    {
        $this->db->where('username',$username);
        $query = $this->db->get('ctv_users');
        $row = $query->row_array();
        return $row['balance'];
    }

    public function qtv_getBalance($username)
    {
        $this->db->where('username',$username);
        $query = $this->db->get('qtv_users');
        $row = $query->row_array();
        return $row['balance'];
    }

    public function quantri_getBalance($username)
    {
        $this->db->where('username',$username);
        $query = $this->db->get('quantri_users');
        $row = $query->row_array();
        return $row['balance'];
    }

    public function ctv_increaseBalance($username, $amount)
    {
        $this->db->where('username',$username);
        $query = $this->db->get('ctv_users');
        $row = $query->row_array();
        $balance = $row['balance'];
        $new_balance = $balance + $amount;
        $data = array(
            'balance'=>$new_balance
        );
        $this->db->where('username',$username);
        $this->db->update('ctv_users',$data);
    }

    public function ctv_decreaseBalance($username, $amount)
    {
        $this->db->where('username',$username);
        $query = $this->db->get('ctv_users');
        $row = $query->row_array();
        $balance = $row['balance'];
        $new_balance = $balance - $amount;
        $data = array(
            'balance'=>$new_balance
        );
        $this->db->where('username',$username);
        $this->db->update('ctv_users',$data);
    }

    public function qtv_increaseBalance($username, $amount)
    {
        $this->db->where('username',$username);
        $query = $this->db->get('qtv_users');
        $row = $query->row_array();
        $balance = $row['balance'];
        $new_balance = $balance + $amount;
        $data = array(
            'balance'=>$new_balance
        );
        $this->db->where('username',$username);
        $this->db->update('qtv_users',$data);
    }

    public function qtv_decreaseBalance($username, $amount)
    {
        $this->db->where('username',$username);
        $query = $this->db->get('qtv_users');
        $row = $query->row_array();
        $balance = $row['balance'];
        $new_balance = $balance - $amount;
        $data = array(
            'balance'=>$new_balance
        );
        $this->db->where('username',$username);
        $this->db->update('qtv_users',$data);
    }

    public function ctv_insertTransLog($type,$sys_username, $quantri_username, $qtv_username, $server, $username, $amount, $balance)
    {
        $data = array(
            'type'=>$type,
            'sys_username'=>$sys_username,
            'quantri_username'=>$quantri_username,
            'qtv_username'=>$qtv_username,
            'server'=>$server,
            'player_username'=>$username,
            'amount'=>$amount,
            'quantri_balance'=>$balance,
            'created_time'=>date('Y-m-d H:i:s')
        );

        $this->db->insert('ctv_transactions',$data);
    }

    public function quantri_increaseBalance($username, $amount)
    {
        $this->db->where('username',$username);
        $query = $this->db->get('quantri_users');
        $row = $query->row_array();
        $balance = $row['balance'];
        $new_balance = $balance + $amount;
        $data = array(
            'balance'=>$new_balance
        );
        $this->db->where('username',$username);
        $this->db->update('quantri_users',$data);
    }

    public function quantri_decreaseBalance($username, $amount)
    {
        $this->db->where('username',$username);
        $query = $this->db->get('quantri_users');
        $row = $query->row_array();
        $balance = $row['balance'];
        $new_balance = $balance - $amount;
        $data = array(
            'balance'=>$new_balance
        );
        $this->db->where('username',$username);
        $this->db->update('quantri_users',$data);
    }

    public function ctv_countTrans($ctv_username)
    {
        $this->db->where('ctv_username',$ctv_username);
        $query = $this->db->get('ctv_transactions');
        return $query->num_rows();
    }

    public function ctv_countPlayers($ctv_username)
    {
        $this->db->where('ctv_username',$ctv_username);
        $this->db->group_by('player_username');
        $query = $this->db->get('ctv_transactions');
        return $query->num_rows();
    }

    public function ctv_getTransactions($ctv_username)
    {
        $this->db->where('ctv_username',$ctv_username);
        $this->db->order_by('id', 'DESC');
        $query = $this->db->get('ctv_transactions');
        return $query->result_array();
    }

    public function quantri_getTransactions($sys_username)
    {
        $this->db->where('sys_username',$sys_username);
        $this->db->where('type',5);
        $this->db->order_by('id', 'DESC');
        $query = $this->db->get('ctv_transactions');
        return $query->result_array();
    }

    public function qtv_getTransactions($qtv_username)
    {
        $this->db->where('type',2);
        $this->db->where('qtv_username',$qtv_username);
        $this->db->order_by('id', 'DESC');
        $query = $this->db->get('ctv_transactions');
        return $query->result_array();
    }

    public function ctv_insertUser($username, $password, $qtv_username)
    {
        $data = array(
            'username'=>$username,
            'password'=>$password,
            'status'=>1,
            'balance'=>0,
            'created_by'=>$qtv_username,
            'created_time'=>date('Y-m-d H:i:s')
        );

        $this->db->insert('ctv_users',$data);
    }

    public function getCTVUser($username)
    {
        $this->db->where('username',$username);
        $query = $this->db->get('ctv_users');
        $row = $query->row_array();
        return $row;
    }

    public function qtv_insertUser($username, $password)
    {
        $data = array(
            'username'=>$username,
            'password'=>$password,
            'status'=>1,
            'balance'=>0,
            'created_time'=>date('Y-m-d H:i:s')
        );

        $this->db->insert('qtv_users',$data);
    }

    public function quantri_insertUser($username, $password)
    {
        $data = array(
            'username'=>$username,
            'password'=>$password,
            'status'=>1,
            'balance'=>0,
            'created_time'=>date('Y-m-d H:i:s')
        );

        $this->db->insert('quantri_users',$data);
    }

    public function getQTVUser($username)
    {
        $this->db->where('username',$username);
        $query = $this->db->get('qtv_users');
        $row = $query->row_array();
        return $row;
    }

    public function getQTUser($username)
    {
        $this->db->where('username',$username);
        $query = $this->db->get('quantri_users');
        $row = $query->row_array();
        return $row;
    }

}
?>