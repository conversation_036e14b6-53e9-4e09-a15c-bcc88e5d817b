<?php
ini_set('MAX_EXECUTION_TIME', '-1');
defined('BASEPATH') OR exit('No direct script access allowed');

class Server_model extends CI_Model {

	public function __construct()
    {
        parent::__construct();
    }

	function loadServer($server)
    {
        $this->db = $this->load->database($server, TRUE);
    }

    public function insertMail($server, $sender, $rid, $rname, $subject, $content)
    {
        $this->loadServer($server);
        $data = array(
        'senderrid' => 0,
        'senderrname' => $sender,
        'sendtime' => date('Y-m-d H:i:s'),
        'receiverrid' => $rid,
        'reveiverrname' => $rname,
        'readtime' => date('Y-m-d H:i:s'),
        'isread' => 0,
        'mailtype' => 0,
        'hasfetchattachment' => 0,
        'subject' => $subject,
        'content' => $content,
        'yinliang' => 0,
        'tongqian' => 0,
        'yuanbao' => 0
        );
        $this->db->insert('t_mail',$data);
        return $this->db->insert_id();
    }

    public function insertMailGoods($server, $mail_id, $goodsid, $count, $binding, $luck, $excellenceinfo)
    {
        $this->loadServer($server);
        $data = array(
        'mailid' => $mail_id,
        'goodsid' => $goodsid,
        'forge_level' => 0,
        'gcount' => $count,
        'binding' => $binding,
        'lucky' => $luck,
        'excellenceinfo' => $excellenceinfo,
        'appendproplev' => 0,
        'Props'=>'',
        'jewellist' => '',
        'quality' => 0,
        'origholenum' => 0,
        'rmbholenum' => 0,
        'bornindex' => 0,
        'strong' => 0,
        'equipchangelife' =>0
        );
        $this->db->insert('t_mailgoods',$data);
    }

    public function getRole($server, $id)
    {
        $this->loadServer($server);
        $this->db->where('rid',$id);
        $query = $this->db->get('t_roles');
        return $query->row_array();
    }

    public function updateZoneID($from_server, $to_server, $from_zoneid, $to_zoneid)
    {
        $this->db = $this->load->database($from_server, TRUE);
        $this->db->update('t_banghui',array('zoneid'=>$to_zoneid));
        $this->db->update('t_huodongpaihang',array('zoneid'=>$to_zoneid));
        $this->db->update('t_jingjichang',array('zoneId'=>$to_zoneid));
        $this->db->update('t_login',array('zoneid'=>$to_zoneid));
        $this->db->update('t_usemoney_log',array('zoneID'=>$to_zoneid));
        $this->db->update('t_zajindanhist',array('zoneid'=>$to_zoneid));
        $this->db->update('t_roles',array('zoneid'=>$to_zoneid));
        $this->db->update('t_talent',array('zoneID'=>$to_zoneid));
        $this->db->update('t_talent_effect',array('zoneID'=>$to_zoneid));
        $this->db->update('t_talent_log',array('zoneID'=>$to_zoneid));
        
        $from_db = $this->db->database;
        $this->db = $this->load->database($to_server, TRUE);
        $to_db = $this->db->database;
        
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_adorationinfo (roleid,adorationroleid,dayid) SELECT roleid,adorationroleid,dayid FROM '.$from_db.'.t_adorationinfo');
        
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_banggonghist (bhid,rid,goods1num,goods2num,goods3num,goods4num,goods5num,tongqian,banggong,addtime) SELECT bhid,rid,goods1num,goods2num,goods3num,goods4num,goods5num,tongqian,banggong,addtime FROM '.$from_db.'.t_banggonghist');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_ban_trade (rid,day,hour,distinct_roles,market_times,market_in_price,market_out_price,trade_times,trade_in_price,trade_out_price) SELECT rid,day,hour,distinct_roles,market_times,market_in_price,market_out_price,trade_times,trade_in_price,trade_out_price FROM '.$from_db.'.t_ban_trade');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_buffer (rid,bufferid,starttime,buffersecs,bufferval)  SELECT rid,bufferid,starttime,buffersecs,bufferval FROM '.$from_db.'.t_buffer');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_building (rid,buildid,taskid_1,taskid_2,taskid_3,taskid_4,level,exp,developtime) SELECT rid,buildid,taskid_1,taskid_2,taskid_3,taskid_4,level,exp,developtime FROM '.$from_db.'.t_building');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_change_name (roleid,oldname,newname,type,cost_diamond,time) SELECT roleid,oldname,newname,type,cost_diamond,time FROM '.$from_db.'.t_change_name');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_consumelog (rid,amount,cdate)  SELECT rid,amount,cdate FROM '.$from_db.'.t_consumelog');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_dailydata (rid,expdayid,todayexp,linglidayid,todaylingli,killbossdayid,todaykillboss,fubendayid,todayfubennum,wuxingdayid,wuxingnum) SELECT rid,expdayid,todayexp,linglidayid,todaylingli,killbossdayid,todaykillboss,fubendayid,todayfubennum,wuxingdayid,wuxingnum FROM '.$from_db.'.t_dailydata');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_dailytasks (rid,huanid,rectime,recnum,taskClass,extdayid,extnum) SELECT rid,huanid,rectime,recnum,taskClass,extdayid,extnum FROM '.$from_db.'.t_dailytasks');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_dayactivityinfo (roleid,activityid,timeinfo,triggercount,totalpoint,lastgettime) SELECT roleid,activityid,timeinfo,triggercount,totalpoint,lastgettime FROM '.$from_db.'.t_dayactivityinfo');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_everyday_activity (rid,groupid,actid,purchaseNum,countNum,activeDay) SELECT rid,groupid,actid,purchaseNum,countNum,activeDay FROM '.$from_db.'.t_everyday_activity');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_exchange3 (rid,yuanbao,leftyuanbao,otherroleid,rectime) SELECT rid,yuanbao,leftyuanbao,otherroleid,rectime FROM '.$from_db.'.t_exchange3');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_friends (myid,otherid,friendType) SELECT myid,otherid,friendType FROM '.$from_db.'.t_friends');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_fuben (rid,fubenid,dayid,enternum,quickpasstimer,finishnum) SELECT rid,fubenid,dayid,enternum,quickpasstimer,finishnum FROM '.$from_db.'.t_fuben');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_fuwen (rid,tabid,name,fuwenequip,shenshiactive,skillequip) SELECT rid,tabid,name,fuwenequip,shenshiactive,skillequip FROM '.$from_db.'.t_fuwen');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_goldbuy (rid,goodsid,goodsnum,totalprice,leftgold,buytime) SELECT rid,goodsid,goodsnum,totalprice,leftgold,buytime FROM '.$from_db.'.t_goldbuy');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_goods (rid,goodsid,isusing,forge_level,starttime,endtime,site,quality,Props,gcount,origholenum,rmbholenum,jewellist,binding,bagindex,salemoney1,saleyuanbao,saleyinpiao,addpropindex,bornindex,lucky,strong,excellenceinfo,appendproplev,equipchangelife,washprops,ehinfo,juhun) SELECT rid,goodsid,isusing,forge_level,starttime,endtime,site,quality,Props,gcount,origholenum,rmbholenum,jewellist,binding,bagindex,salemoney1,saleyuanbao,saleyinpiao,addpropindex,bornindex,lucky,strong,excellenceinfo,appendproplev,equipchangelife,washprops,ehinfo,juhun FROM '.$from_db.'.t_goods');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_goodslimit (rid,goodsid,dayid,usednum) SELECT rid,goodsid,dayid,usednum FROM '.$from_db.'.t_goodslimit');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_goods_bak (rid,goodsid,isusing,forge_level,starttime,endtime,site,quality,Props,gcount,origholenum,rmbholenum,jewellist,binding,bagindex,salemoney1,saleyuanbao,saleyinpiao,addpropindex,bornindex,lucky,strong,excellenceinfo,appendproplev,equipchangelife,washprops,ehinfo,juhun,opstate,optime,oprole) SELECT rid,goodsid,isusing,forge_level,starttime,endtime,site,quality,Props,gcount,origholenum,rmbholenum,jewellist,binding,bagindex,salemoney1,saleyuanbao,saleyinpiao,addpropindex,bornindex,lucky,strong,excellenceinfo,appendproplev,equipchangelife,washprops,ehinfo,juhun,opstate,optime,oprole FROM '.$from_db.'.t_goods_bak');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_huodong (rid,loginweekid,logindayid,loginnum,newstep,steptime,lastmtime,curmid,curmtime,songliid,logingiftstate,onlinegiftstate,lastlimittimehuodongid,lastlimittimedayid,limittimeloginnum,limittimegiftstate,everydayonlineawardstep,geteverydayonlineawarddayid,serieslogingetawardstep,seriesloginawarddayid,seriesloginawardgoodsid,everydayonlineawardgoodsid) SELECT rid,loginweekid,logindayid,loginnum,newstep,steptime,lastmtime,curmid,curmtime,songliid,logingiftstate,onlinegiftstate,lastlimittimehuodongid,lastlimittimedayid,limittimeloginnum,limittimegiftstate,everydayonlineawardstep,geteverydayonlineawarddayid,serieslogingetawardstep,seriesloginawarddayid,seriesloginawardgoodsid,everydayonlineawardgoodsid FROM '.$from_db.'.t_huodong');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_huodongpaihang (rid,rname,zoneid,type,paihang,phvalue,paihangtime) SELECT rid,rname,zoneid,type,paihang,phvalue,paihangtime FROM '.$from_db.'.t_huodongpaihang');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_holyitem (roleid, shengwu_type, part_slot, part_suit, part_slice, fail_count) SELECT roleid, shengwu_type, part_slot, part_suit, part_slice, fail_count FROM '.$from_db.'.t_holyitem');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_inputlog (amount,u,rid,order_no,cporder_no,time,sign,inputtime,result,zoneid,itemid,chargetime) SELECT amount,u,rid,order_no,cporder_no,time,sign,inputtime,result,zoneid,itemid,chargetime FROM '.$from_db.'.t_inputlog');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_jingjichang (roleId,roleName,name,zoneId,level,changeLiveCount,occupationId,winCount,ranking,nextRewardTime,nextChallengeTime,version,baseProps,extProps,equipDatas,skillDatas,CombatForce,sex,wingData,settingFlags,maxwincnt,shenshiequip) SELECT roleId,roleName,name,zoneId,level,changeLiveCount,occupationId,winCount,ranking,nextRewardTime,nextChallengeTime,version,baseProps,extProps,equipDatas,skillDatas,CombatForce,sex,wingData,settingFlags,maxwincnt,shenshiequip FROM '.$from_db.'.t_jingjichang');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_lingyu (roleid,type,level,suit) SELECT roleid,type,level,suit FROM '.$from_db.'.t_lingyu');
        $query = $this->db->query('INSERT IGNORE INTO '.$to_db.'.t_login (userid,dayid,rid,logintime,logouttime,ip,mac,zoneid,onlinesecs,loginnum,c1,c2) SELECT userid,dayid,rid,logintime,logouttime,ip,mac,zoneid,onlinesecs,loginnum,c1,c2 FROM '.$from_db.'.t_login');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_mail (mailid,senderrid,senderrname,sendtime,receiverrid,reveiverrname,readtime,isread,mailtype,hasfetchattachment,subject,content,yinliang,tongqian,yuanbao) SELECT mailid,senderrid,senderrname,sendtime,receiverrid,reveiverrname,readtime,isread,mailtype,hasfetchattachment,subject,content,yinliang,tongqian,yuanbao FROM '.$from_db.'.t_mail');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_mailgoods (mailid,goodsid,forge_level,quality,Props,gcount,binding,origholenum,rmbholenum,jewellist,addpropindex,bornindex,lucky,strong,excellenceinfo,appendproplev,equipchangelife) SELECT mailid,goodsid,forge_level,quality,Props,gcount,binding,origholenum,rmbholenum,jewellist,addpropindex,bornindex,lucky,strong,excellenceinfo,appendproplev,equipchangelife FROM '.$from_db.'.t_mailgoods');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_mallbuy (rid,goodsid,goodsnum,totalprice,leftmoney,buytime) SELECT rid,goodsid,goodsnum,totalprice,leftmoney,buytime FROM '.$from_db.'.t_mallbuy');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_marry (roleid,spouseid,marrytype,ringid,goodwillexp,goodwillstar,goodwilllevel,givenrose,lovemessage,autoreject,changtime) SELECT roleid,spouseid,marrytype,ringid,goodwillexp,goodwillstar,goodwilllevel,givenrose,lovemessage,autoreject,changtime FROM '.$from_db.'.t_marry');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_marryparty (roleid,partytype,joincount,starttime,husbandid,wifeid) SELECT roleid,partytype,joincount,starttime,husbandid,wifeid FROM '.$from_db.'.t_marryparty');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_marryparty_join (roleid,partyroleid,joincount) SELECT roleid,partyroleid,joincount FROM '.$from_db.'.t_marryparty_join');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_mojingexchangeinfo (roleid,exchangeid,exchangenum,dayid) SELECT roleid,exchangeid,exchangenum,dayid FROM '.$from_db.'.t_mojingexchangeinfo');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_npcbuy (rid,goodsid,goodsnum,totalprice,leftmoney,moneytype,buytime) SELECT rid,goodsid,goodsnum,totalprice,leftmoney,moneytype,buytime FROM '.$from_db.'.t_npcbuy');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_ornament (roleid,goodsid,param1,param2) SELECT roleid,goodsid,param1,param2 FROM '.$from_db.'.t_ornament');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_picturejudgeinfo (roleid,picturejudgeid,refercount) SELECT roleid,picturejudgeid,refercount FROM '.$from_db.'.t_picturejudgeinfo');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_ptbag (rid,extgridnum) SELECT rid,extgridnum FROM '.$from_db.'.t_ptbag');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_resourcegetinfo (roleid,type,leftcount,exp,bandmoney,mojing,chengjiu,shengwang,zhangong,bangzuan,xinghun,hasget,yuansufenmo) SELECT roleid,type,leftcount,exp,bandmoney,mojing,chengjiu,shengwang,zhangong,bangzuan,xinghun,hasget,yuansufenmo FROM '.$from_db.'.t_resourcegetinfo');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_roleparams_char (rid,idx,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9) SELECT rid,idx,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9 FROM '.$from_db.'.t_roleparams_char');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_roleparams_long (rid,idx,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9,v10,v11,v12,v13,v14,v15,v16,v17,v18,v19,v20,v21,v22,v23,v24,v25,v26,v27,v28,v29,v30,v31,v32,v33,v34,v35,v36,v37,v38,v39) SELECT rid,idx,v0,v1,v2,v3,v4,v5,v6,v7,v8,v9,v10,v11,v12,v13,v14,v15,v16,v17,v18,v19,v20,v21,v22,v23,v24,v25,v26,v27,v28,v29,v30,v31,v32,v33,v34,v35,v36,v37,v38,v39 FROM '.$from_db.'.t_roleparams_long');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_seven_day_act (roleid,act_type,id,award_flag,param1,param2) SELECT roleid,act_type,id,award_flag,param1,param2 FROM '.$from_db.'.t_seven_day_act');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_skills (rid,skillid,skilllevel,usednum) SELECT rid,skillid,skilllevel,usednum FROM '.$from_db.'.t_skills');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_starconstellationinfo (roleid,starsiteid,starslotid) SELECT roleid,starsiteid,starslotid FROM '.$from_db.'.t_starconstellationinfo');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_tasks (taskid,rid,focus,value1,value2,isdel,addtime,starlevel) SELECT taskid,rid,focus,value1,value2,isdel,addtime,starlevel FROM '.$from_db.'.t_tasks');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_taskslog (rid,taskid,count) SELECT rid,taskid,count FROM '.$from_db.'.t_taskslog');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_usemoney_log (DBId,userid,ObjName,optFrom,currEnvName,tarEnvName,optType,optTime,optAmount,zoneID,optSurplus) SELECT DBId,userid,ObjName,optFrom,currEnvName,tarEnvName,optType,optTime,optAmount,zoneID,optSurplus FROM '.$from_db.'.t_usemoney_log');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_vipdailydata (rid,prioritytype,dayid,usedtimes) SELECT rid,prioritytype,dayid,usedtimes FROM '.$from_db.'.t_vipdailydata');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_wanmota (roleID,roleName,flushTime,passLayerCount,sweepLayer,sweepReward,sweepBeginTime) SELECT roleID,roleName,flushTime,passLayerCount,sweepLayer,sweepReward,sweepBeginTime FROM '.$from_db.'.t_wanmota');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_warning (rid,usedmoney,goodsmoney,warningtime) SELECT rid,usedmoney,goodsmoney,warningtime FROM '.$from_db.'.t_warning');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_wings (rid,rname,occupation,wingid,forgeLevel,addtime,isdel,failednum,equiped,starexp,zhulingnum,zhuhunnum) SELECT rid,rname,occupation,wingid,forgeLevel,addtime,isdel,failednum,equiped,starexp,zhulingnum,zhuhunnum FROM '.$from_db.'.t_wings');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_talent (roleID,tatalCount,exp,zoneID) SELECT roleID,tatalCount,exp,zoneID FROM '.$from_db.'.t_talent');

        $query = $this->db->query('INSERT INTO '.$to_db.'.t_talent_effect (roleID,talentType,effectID,effectLevel,zoneID) SELECT roleID,talentType,effectID,effectLevel,zoneID FROM '.$from_db.'.t_talent_effect');

        $query = $this->db->query('INSERT INTO '.$to_db.'.t_talent_log (zoneID,roleID,logType,logValue,logTime) SELECT zoneID,roleID,logType,logValue,logTime FROM '.$from_db.'.t_talent_log');
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_zajindanhist (rid,rname,zoneid,timesselected,usedyuanbao,usedjindan,gaingoodsid,gaingoodsnum,gaingold,gainyinliang,gainexp,strprop,operationtime) SELECT rid,rname,zoneid,timesselected,usedyuanbao,usedjindan,gaingoodsid,gaingoodsnum,gaingold,gainyinliang,gainexp,strprop,operationtime FROM '.$from_db.'.t_zajindanhist');

        $query = $this->db->query('SELECT a.bhname FROM '.$to_db.'.t_banghui a, '.$from_db.'.t_banghui b WHERE a.bhname = b.bhname');
        $result = $query->result_array();
        foreach($result as $bhdata)
        {
            $bhname = $bhdata['bhname'];
            $this->db = $this->load->database($from_server, TRUE);
            $this->db->where('bhname',$bhname);
            $this->db->update('t_banghui',array('bhname'=>$bhname.'-'.$from_zoneid.'-2'));

            $this->db = $this->load->database($to_server, TRUE);
            $this->db->where('bhname',$bhname);
            $this->db->update('t_banghui',array('bhname'=>$bhname.'-'.$to_zoneid.'-3'));
        }

        $query = $this->db->query('INSERT INTO '.$to_db.'.t_banghui (bhid,bhname,zoneid,rid,totalnum,totallevel,isverfiy,bhbulletin,buildtime,qiname,qilevel,goods1num,goods2num,goods3num,goods4num,goods5num,tongqian,jitan,junxie,guanghuan,isdel,totalcombatforce,fubenid,fubenstate,openday,killers,can_mod_name_times,zhengduoweek,zhengduousedtime,voice) SELECT bhid,bhname,zoneid,rid,totalnum,totallevel,isverfiy,bhbulletin,buildtime,qiname,qilevel,goods1num,goods2num,goods3num,goods4num,goods5num,tongqian,jitan,junxie,guanghuan,isdel,totalcombatforce,fubenid,fubenstate,openday,killers,can_mod_name_times,zhengduoweek,zhengduousedtime,voice FROM '.$from_db.'.t_banghui');


        $query = $this->db->query('SELECT a.rname FROM '.$to_db.'.t_roles a, '.$from_db.'.t_roles b WHERE a.rname = b.rname');

        $result = $query->result_array();
        foreach($result as $roledata)
        {
            $rname = $roledata['rname'];
            $this->db = $this->load->database($from_server, TRUE);
            $this->db->where('rname',$rname);
            $this->db->update('t_roles',array('rname'=>$rname.'-S'.$from_zoneid.'-2'));

            $this->db = $this->load->database($to_server, TRUE);
            $this->db->where('rname',$rname);
            $this->db->update('t_roles',array('rname'=>$rname.'-S'.$to_zoneid.'-3'));
        }

        $query = $this->db->query('INSERT INTO '.$to_db.'.t_roles (rid,userid,rname,sex,occupation,level,pic,faction,money1,money2,experience,pkmode,pkvalue,position,regtime,lasttime,isdel,deltime,predeltime,bagnum,othername,main_quick_keys,other_quick_keys,loginnum,leftfightsecs,horseid,petid,interpower,totalonlinesecs,antiaddictionsecs,logofftime,biguantime,yinliang,total_jingmai_exp,jingmai_exp_num,lasthorseid,skillid,autolife,automagic,numskillid,maintaskid,pkpoint,lianzhan,killboss,equipjifen,xueweinum,skilllearnednum,horsejifen,battlenamestart,battlenameindex,cztaskid,battlenum,heroindex,logindayid,logindaynum,zoneid,bhname,bhverify,bhzhiwu,chenghao,bgdayid1,bgmoney,bgdayid2,bggoods,banggong,huanghou,jiebiaodayid,jiebiaonum,username,lastmailid,onceawardflag,banchat,banlogin,isflashplayer,changelifecount,admiredcount,combatforce,autoassignpropertypoint,vipawardflag,store_yinliang,store_money,magic_sword_param,fluorescent_point,ban_trade_to_ticks,juntuanzhiwu,huiji,huijiexp) SELECT rid,userid,rname,sex,occupation,level,pic,faction,money1,money2,experience,pkmode,pkvalue,position,regtime,lasttime,isdel,deltime,predeltime,bagnum,othername,main_quick_keys,other_quick_keys,loginnum,leftfightsecs,horseid,petid,interpower,totalonlinesecs,antiaddictionsecs,logofftime,biguantime,yinliang,total_jingmai_exp,jingmai_exp_num,lasthorseid,skillid,autolife,automagic,numskillid,maintaskid,pkpoint,lianzhan,killboss,equipjifen,xueweinum,skilllearnednum,horsejifen,battlenamestart,battlenameindex,cztaskid,battlenum,heroindex,logindayid,logindaynum,zoneid,bhname,bhverify,bhzhiwu,chenghao,bgdayid1,bgmoney,bgdayid2,bggoods,banggong,huanghou,jiebiaodayid,jiebiaonum,username,lastmailid,onceawardflag,banchat,banlogin,isflashplayer,changelifecount,admiredcount,combatforce,autoassignpropertypoint,vipawardflag,store_yinliang,store_money,magic_sword_param,fluorescent_point,ban_trade_to_ticks,juntuanzhiwu,huiji,huijiexp FROM '.$from_db.'.t_roles');

        $query = $this->db->query('INSERT INTO '.$to_db.'.t_firstcharge (uid,charge_info, notget) SELECT b.uid, b.charge_info, b.notget FROM '.$from_db.'.t_firstcharge b WHERE b.uid NOT IN (SELECT a.uid FROM '.$to_db.'.t_firstcharge a)');

        $query = $this->db->query('INSERT INTO '.$to_db.'.t_secondpassword (userid,secpwd) SELECT b.userid, b.secpwd FROM '.$from_db.'.t_secondpassword b WHERE b.userid NOT IN (SELECT a.userid FROM '.$to_db.'.t_secondpassword a)');

        $query = $this->db->query('SELECT b.userid,b.money,b.realmoney FROM '.$from_db.'.t_money b WHERE b.userid IN (SELECT a.userid FROM '.$to_db.'.t_money a)');

        $query_insert = $this->db->query('INSERT INTO '.$to_db.'.t_money (userid,money,realmoney,cc,giftid,giftjifen,points,specjifen,everyjifen) SELECT b.userid,b.money,b.realmoney,b.cc,b.giftid,b.giftjifen,b.points,b.specjifen,b.everyjifen FROM '.$from_db.'.t_money b WHERE b.userid NOT IN (SELECT a.userid FROM '.$to_db.'.t_money a)');
        
        $result = $query->result_array();
        $this->db = $this->load->database($to_server, TRUE);
        foreach($result as $moneydata)
        {
            $userid = $moneydata['userid'];
            $money = $moneydata['money'];
            $realmoney = $moneydata['realmoney'];
            $this->db->where('userid',$userid);
            $qr = $this->db->get('t_money');
            $row = $qr->row_array();
            $currentMoney = $row['money'];
            $currentRealMoney = $row['realmoney'];
            $this->db->where('userid',$userid);
            $this->db->update('t_money',array('money'=>$currentMoney+$money,'realmoney'=>$currentRealMoney+$realmoney));
        }
        
        
        $query = $this->db->query('INSERT INTO '.$to_db.'.t_huodongawarduserhist (userid,activitytype,keystr,hasgettimes,lastgettime) SELECT a.* FROM '.$from_db.'.t_huodongawarduserhist a WHERE a.userid NOT IN (SELECT b.userid FROM '.$to_db.'.t_huodongawarduserhist b WHERE activitytype = 39 GROUP BY b.userid) AND a.activitytype = 39');

        $query = $this->db->query('INSERT INTO '.$to_db.'.t_huodongawarduserhist (userid,activitytype,keystr,hasgettimes,lastgettime) SELECT a.* FROM '.$from_db.'.t_huodongawarduserhist a WHERE a.userid NOT IN (SELECT b.userid FROM '.$to_db.'.t_huodongawarduserhist b WHERE activitytype = 38 GROUP BY b.userid) AND a.activitytype = 38');

        $query = $this->db->query('INSERT INTO '.$to_db.'.t_huodongawarduserhist (userid,activitytype,keystr,hasgettimes,lastgettime) SELECT a.* FROM '.$from_db.'.t_huodongawarduserhist a WHERE a.userid NOT IN (SELECT b.userid FROM '.$to_db.'.t_huodongawarduserhist b WHERE activitytype = 27 GROUP BY b.userid) AND a.activitytype = 27');
        
        $query = $this->db->query('SELECT * FROM '.$from_db.'.t_huodongawarduserhist c WHERE c.userid IN (SELECT a.userid FROM '.$to_db.'.t_huodongawarduserhist a WHERE a.userid IN (SELECT b.userid FROM '.$from_db.'.t_huodongawarduserhist b)) AND c.activitytype = 39');
        $result = $query->result_array();
        $this->db = $this->load->database($to_server, TRUE);
        foreach($result as $awarddata)
        {
            $userid = $awarddata['userid'];
            $hasgettimes_from = $awarddata['hasgettimes'];
            $this->db->where('userid',$userid);
            $this->db->where('activitytype',39);
            $qr = $this->db->get('t_huodongawarduserhist');
            $row = $qr->row_array();
            $hasgettimes_to = $row['hasgettimes'];
            if(strlen($hasgettimes_from) > strlen($hasgettimes_to))
            {
                $this->db->where('userid',$userid);
                $this->db->where('activitytype',39);
                $this->db->update('t_huodongawarduserhist', array('hasgettimes'=>$hasgettimes_from));
            }
        }

        $query = $this->db->query('SELECT * FROM '.$from_db.'.t_huodongawarduserhist c WHERE c.userid IN (SELECT a.userid FROM '.$to_db.'.t_huodongawarduserhist a WHERE a.userid IN (SELECT b.userid FROM '.$from_db.'.t_huodongawarduserhist b)) AND c.activitytype = 38');
        $result = $query->result_array();
        $this->db = $this->load->database($to_server, TRUE);
        foreach($result as $awarddata)
        {
            $userid = $awarddata['userid'];
            $hasgettimes_from = $awarddata['hasgettimes'];
            $this->db->where('userid',$userid);
            $this->db->where('activitytype',38);
            $qr = $this->db->get('t_huodongawarduserhist');
            $row = $qr->row_array();
            $hasgettimes_to = $row['hasgettimes'];
            if(strlen($hasgettimes_from) > strlen($hasgettimes_to))
            {
                $this->db->where('userid',$userid);
                $this->db->where('activitytype',38);
                $this->db->update('t_huodongawarduserhist', array('hasgettimes'=>$hasgettimes_from));
            }
        }

        $query = $this->db->query('SELECT * FROM '.$from_db.'.t_huodongawarduserhist c WHERE c.userid IN (SELECT a.userid FROM '.$to_db.'.t_huodongawarduserhist a WHERE a.userid IN (SELECT b.userid FROM '.$from_db.'.t_huodongawarduserhist b)) AND c.activitytype = 27');
        $result = $query->result_array();
        $this->db = $this->load->database($to_server, TRUE);
        foreach($result as $awarddata)
        {
            $userid = $awarddata['userid'];
            $hasgettimes_from = $awarddata['hasgettimes'];
            $this->db->where('userid',$userid);
            $this->db->where('activitytype',27);
            $qr = $this->db->get('t_huodongawarduserhist');
            $row = $qr->row_array();
            $hasgettimes_to = $row['hasgettimes'];
            if(strlen($hasgettimes_from) > strlen($hasgettimes_to))
            {
                $this->db->where('userid',$userid);
                $this->db->where('activitytype',27);
                $this->db->update('t_huodongawarduserhist', array('hasgettimes'=>$hasgettimes_from));
            }
        }

        $query = $this->db->query('SELECT * FROM '.$to_db.'.t_jingjichang where ranking not in (-1) order BY ranking ASC, CombatForce DESC');
        $result = $query->result_array();
        $this->db = $this->load->database($to_server, TRUE);
        $k = 1;
        foreach($result as $rankdata)
        {
            $roleId = $rankdata['roleId'];
            $ranking = $rankdata['ranking'];
            $this->db->where('roleId',$roleId);
            $this->db->update('t_jingjichang', array('ranking'=>$k));
            $k++;
        }
        //print_r($result);
        echo "Done !";

    }

        
    public function getTop10Combatforce($server)
    {
        $this->loadServer($server);
        $query = $this->db->query('SELECT * FROM t_roles WHERE 1=1 ORDER BY combatforce DESC LIMIT 10');
        //$query = $this->db->query('SELECT * FROM t_roles WHERE rname = \'CoolGirl\'');
        return $query->result_array();
    }

    public function getTop10Level($server)
    {
        $this->loadServer($server);
        $query = $this->db->query('SELECT * FROM t_roles WHERE 1=1 ORDER BY changelifecount DESC, LEVEL DESC LIMIT 10');
        //$query = $this->db->query('SELECT * FROM t_roles WHERE rname = \'CoolGirl\'');
        return $query->result_array();
    }
	
	public function getAllMemberGuild($server) 
	{
		$this->loadServer($server);

		$roleIdQuery = $this->db->query('SELECT role_id FROM t_king_role_data WHERE king_type = 2');
		$roleIdResult = $roleIdQuery->result_array();
	
		// Extract the first role_id
		if (!empty($roleIdResult)) {
			$roleId = $roleIdResult[0]['role_id'];
		} else {
			// Handle no role_id found
			return [];
		}
	
		// Get bhname from t_roles using the extracted role_id
		$bhnameQuery = $this->db->query('SELECT bhname FROM t_roles WHERE rid = ?', [$roleId]);
		$bhnameResult = $bhnameQuery->result_array();
	
		// Extract the first bhname
		if (!empty($bhnameResult)) {
			$bhname = $bhnameResult[0]['bhname'];
		} else {
			// Handle no bhname found
			return [];
		}
	
		// Query all data from t_roles with the extracted bhname
		$allMemberQuery = $this->db->query('SELECT * FROM t_roles WHERE bhname = ?', [$bhname]);
		$allMemberResult = $allMemberQuery->result_array();
	
		// Return the retrieved data
		return $allMemberResult;
	}

public function getMemberGuildWithLogValueDiscrepancies($server)
	{
		$this->loadServer($server);
	
		$logValueSumsQuery = $this->db->query('
			SELECT roleID
			FROM t_talent_log
			WHERE logType = 1 and logValue = 0
			GROUP BY roleID
		');
		$logValueSums = $logValueSumsQuery->result_array();
	
		$errorRoleIDs = [];
		foreach ($logValueSums as $logValueSum) {
			$roleID = $logValueSum['roleID'];
			$errorRoleIDs[] = $roleID; 
			//$totalCountQuery = $this->db->query('SELECT tatalCount FROM t_talent WHERE roleID = ?', [$roleID]);
			//if ($totalCountQuery) {
			//	$totalCountResult = $totalCountQuery->row();
			//	if ($totalCountResult) {
			//		$totalCount = $totalCountResult->tatalCount;
			//	} else {
			//		var_dump("XXXXXXXXXXXXXXX", $roleID);
			//		//return;
			//		}
			// } else {
			//	return;}
			var_dump("11111111111111111111", $roleID);
			$useridQuery = $this->db->query('SELECT userid FROM t_roles WHERE rid = ?', [$roleID]);
			
			if ($useridQuery) {
				$useridResult = $useridQuery->row();
				if ($useridResult) {
					$userid = $useridResult->userid;
					if($userid) {
						$this->addBlackUserID($server, $userid);
					} else {
						var_dump("Loi userid query", $roleID);
					}
				} else {
					var_dump("Loi userid query", $roleID);
				}
			} else {
				var_dump("Loi userid query", $roleID);
			}
			
		}
		if ($errorRoleIDs) {
			// Sử dụng implode để tạo chuỗi giá trị RID ngăn cách bằng dấu phẩy
			$placeholders = implode(',', array_fill(0, count($errorRoleIDs), '?'));
			$query = 'SELECT * FROM t_roles WHERE rid IN (' . $placeholders . ')';
			
			// Truyền mảng các giá trị RID vào câu truy vấn
			$rolesQuery = $this->db->query($query, $errorRoleIDs);
			$resultRoles = $rolesQuery->result_array();
		} else {
			$resultRoles = [];
		}
		return $resultRoles;
	}
	
	
	public function getLevelThienPhu($server, $roleID)
	{
		$this->loadServer($server);
	
		$totalCountQuery = $this->db->query('SELECT tatalCount FROM t_talent WHERE roleID = ?', [$roleID]);
		if ($totalCountQuery) {
			$totalCountResult = $totalCountQuery->row();
			if ($totalCountResult) {
				$totalCount = $totalCountResult->tatalCount;
			} else { $totalCount = 0; } 
		} else { $totalCount = 0; }
		//$totalCountResult = $totalCountQuery->row();
		//$totalCount = $totalCountResult->tatalCount;
	
		return $totalCount;
	}
	
	
	public function getExpReal($server, $roleID)
	{
		$this->loadServer($server);
	
		// Lấy tổng logValue cho từng roleID
		$logValueSumsQuery = $this->db->query('
			SELECT SUM(logValue) AS logValueSum
			FROM t_talent_log
			WHERE logType = 1 and roleID = ?
			GROUP BY roleID
		', [$roleID]);
		$logValueSums = $logValueSumsQuery->row();
	
		return $logValueSums->logValueSum;
	}
	
	public function getTimeReal($server, $roleID)
	{
		$this->loadServer($server);
	
		// Lấy tổng logValue cho từng roleID
		$logValueSumsQuery = $this->db->query('
			SELECT logTime
			FROM t_talent_log
			WHERE roleID = ?
		', [$roleID]);
		$logTime = $logValueSumsQuery->row();
	
		return $logTime->logTime;
	}
	
	public function addBlackUserID($server, $userID) {
		$this->loadServer($server);
	
		$existingUserIDQuery = $this->db->query('SELECT * FROM t_blackuserid WHERE userID = ?', [$userID]);
		$existingUserIDResult = $existingUserIDQuery->result_array();
	
		if (!empty($existingUserIDResult)) {
			return false;
		}
	
		$insertQuery = $this->db->query('INSERT INTO t_blackuserid (userID) VALUES (?)', [$userID]);
		if ($insertQuery !== false) {
			$affectedRows = $this->db->affected_rows();
			return $affectedRows > 0; 
		} else {
			return false;
		}
	}

	public function removeBlackUserID($server, $userID) {
		$this->loadServer($server);
	
		$deleteQuery = $this->db->query('DELETE FROM t_blackuserid WHERE userID = ?', [$userID]);
	
		return $deleteQuery->affected_rows() > 0; // Return true if deletion successful
	}
}
?>