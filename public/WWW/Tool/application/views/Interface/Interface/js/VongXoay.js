﻿var isProcessing = false;
var rotation = function (itemidx, Message) {
    var angle = 0;
    var nangletmp = 0;
    var percent1item = Math.floor(360 / 8);
    var nrom = Math.floor(((Math.random() * 10) + 2));
    var nprocess = (nrom * 360) + (itemidx * percent1item) + Math.floor((Math.random() * (percent1item - 10)) + 5);
    var idInterval = setInterval(function () {
        if (nprocess > 1000)
            nangletmp = 7;
        else
            if (nprocess > 800)
                nangletmp = 6;
            else
                if (nprocess > 400)
                    nangletmp = 5;
                else
                    if (nprocess > 300)
                        nangletmp = 4;
                    else
                        if (nprocess > 200)
                            nangletmp = 3;
                        else
                            if (nprocess > 200)
                                nangletmp = 2.5;
                            else
                                if (nprocess > 100)
                                    nangletmp = 2;
                                else
                                    if (nprocess > 30)
                                        nangletmp = 1;
                                    else
                                        if (nprocess > 0.5)
                                            nangletmp = 0.5;
                                        else
                                            if (nprocess <= 0.5) {

                                                window.clearInterval(idInterval);
                                                OnEndRotation(Message);
                                            }
        nprocess -= nangletmp;
        angle += nangletmp;
        $("#VongXoay").rotate(Math.floor(angle));

    }, 20);
}

function getLog(updatestate) {
    var obj = {};
    var jsonData = JSON.stringify(obj);
    $.ajax({
        type: "POST",
        url: window.location.pathname + "/getLogVongXoay",
        data: jsonData,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (data) {
            retData = JSON.parse(data.d);
            strData = "";
            for (i = 0; i < retData.length; i++) {
                strData += "<tr class=\"ph_td2\"><td class=\"ph_td1\" >" + retData[i].Time + "</td><td  class=\"ph_td1\">" + retData[i].Msg + "</td></tr>";
            }
            $("#dataBodyLog").html(strData);

            if (updatestate) {
                $("#frmListChar").hide();
                $("#frmVongXoayMayMan").show();
            }
        },
        error: function (request, status, error) {
            document.write(request.responseText);
            alert("Wwoops something went wrong !");
        }
    });
}


function OnEndRotation(response) {
    isProcessing = false;
    getLog();
    alert(response);
}
