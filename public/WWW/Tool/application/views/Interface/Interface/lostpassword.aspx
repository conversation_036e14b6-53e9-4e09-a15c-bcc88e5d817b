﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="lostpassword.aspx.cs" Inherits="Users.Interface.lostpastword" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
     <meta http-equiv="cache-control" content="max-age=0" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <link rel="stylesheet" href="css/style.css" />
    <script src="js/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="js/jAlert.js"></script>
    <script src="js/jAlert-functions.js"></script>
    <link href="css/jAlert.css" rel="stylesheet" />
    <title></title>
</head>
<body>
    <form method="post" action="./lostpassword.aspx" id="form1">
        <div class="wrapper">
            <div class="main pr cf">
                <div class="tool_bar">
                    <a href="#" onclick="document.location = 'js-oc:kunlunClose:null';return false" class="close"></a>
                    <a href="?" class="home"></a>
                    <div class="title">Quên mật khẩu</div>
                </div>
                <div class="box-data">
                    <div class="box-news" id="frmkiemtra">
                        <h1>KIỂM TRA THÔNG TIN TÀI KHOẢN</h1>
                        <h2>Bạn vui lòng điền thông tin tài khoản và số điện thoại đăng ký của tài khoản, ấn kiểm tra để tiếp tục.</h2>
                        <input type="text" class="login_input" id="txtAccount" placeholder="Tài khoản" />
                        <input type="text" class="login_input" id="txtPhone" placeholder="Số điện thoại đăng ký tài khoản trên" />
                        <input type="text" class="login_input" id="txtMail" placeholder="Email đăng ký tài khoản trên" />
                        <a class="read-more" href="javascript:void(0);" id="btCheckInfo"><span>Kiểm tra</span></a>
                    </div>
                    <div class="box-news" id="frmChangePass" style="display: none;">
                        <h1>ĐỔI MẬT KHẨU</h1>
                        <h2>Thông tin tài khoản và số điện thoại đăng ký chính xác, nhập mật khẩu mới để thay đổi</h2>
                         <input type="text" class="login_input" id="txtOldAccount" placeholder="Tài khoản" disabled="disabled" />
                         <input type="password" class="login_input" placeholder="Nhập mật khẩu mới" id="txtNewPass" />
                        <input type="password" class="login_input" placeholder="Nhập lại mật khẩu mới" id="txtReNewPass" />
                        <a class="read-more" href="javascript:void(0);" id="btChangePass"><span>Đổi mật khẩu</span></a>
                    </div>
                    <div class="box-news" id="frmFinish" style="display: none;">
                        <h1>KẾT THÚC QUÁ TRÌNH</h1>
                        <h2><span>Xin chúc mừng</span><br />
                            Bạn đã thay đổi mật khẩu thành công. Xin đăng nhập lại để vào trò chơi.</h2>
                        <br />
                        <a class="read-more" href="javascript:void(0);" onclick="document.location = 'js-oc:kunlunClose:null';return false"><span>Đóng </span></a>
                    </div>

                </div>
            </div>
        </div>
    </form>
    <script type="text/javascript">
        $(document).ready(function (e) {
            $("#btCheckInfo").click(function (e) {
                var obj = {};
                obj.UserName = $('#txtAccount').val();
                obj.Phone = $('#txtPhone').val();
                obj.Mail = $('#txtMail').val();
                if ((obj.UserName == "") || (obj.Phone == "") || (obj.Mail == "")) {
                    jAlert("Vui lòng nhập tài khoản, số điện thoại và email đăng ký");
                    return;
                }
                var jsonData = JSON.stringify(obj);
                $.ajax({
                    type: "POST",
                    url: window.location.pathname + "/CheckInfo",
                    data: jsonData,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: function (msg) {
                        $('#txtAccount').removeClass("error");
                        $('#txtPhone').removeClass("error");
                        switch (msg.d) {
                            case 0:
                                $("#frmkiemtra").hide();
                                $("#frmChangePass").show();
                                $('#txtOldAccount').val($('#txtAccount').val());
                                return;
                            case 1:
                                strMsg = 'Tài khoản không tồn tại';
                                $('#txtAccount').addClass("error");
                                break;
                            case 2:
                                strMsg = 'Email không khớp với tài khoản';
                                $('#txtPhone').addClass("error");
                                break;
                            case 3:
                                strMsg = 'Số điện thoại không khớp với tài khoản';
                                $('#txtPhone').addClass("error");
                                break;
                            case 4:
                                strMsg = 'Tài khoản chưa đăng ký email';
                                $('#txtPhone').addClass("error");
                                break;
                            case 5:
                                strMsg = 'Tài khoản chưa đăng ký số điện thoại';
                                $('#txtPhone').addClass("error");
                                break;
                            default:
                                strMsg = 'Lỗi không xác định, code: (' + msg.d + ')';
                                break;
                        }
                        jAlert(strMsg);
                        return false;

                    },
                    error: function (request, status, error) {
                        //document.write(request.responseText);
                        jAlert(request.responseText);
                        return false;
                    }
                });
            });

            $("#btChangePass").click(function (e) {
                var obj = {};
                obj.UserName = $('#txtAccount').val();
                obj.NewPass = $('#txtNewPass').val();
                if ((obj.NewPass == "") || (obj.OTP == "")) {
                    jAlert("Vui lòng nhập tài khoản và số điện thoại đăng ký");
                    return;
                }
                if ($('#txtNewPass').val() != $('#txtReNewPass').val()) {
                    jAlert("Mật khẩu mới và nhập lại mật khẩu mới không giống nhau.");
                    return;
                }
                var jsonData = JSON.stringify(obj);
                $.ajax({
                    type: "POST",
                    url: window.location.pathname + "/ResetPassword",
                    data: jsonData,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: function (msg) {
                        $('#txtNewPass').removeClass("error");
                        $('#txtOTP').removeClass("error");
                        switch (msg.d) {
                            case 0:
                                $("#frmChangePass").hide();
                                $("#frmFinish").show();
                                return;
                            case 1:
                                strMsg = 'Mật khẩu quá yếu, xin chọn lại';
                                $('#txtNewPass').addClass("error");
                                break;
                            case 2:
                                strMsg = 'Cập nhật dữ liệu thất bại';
                                $('#txtNewPass').addClass("error");
                                break;                           
                            default:
                                strMsg = 'Lỗi không xác định, code: (' + msg.d + ')';
                                break;
                        }
                        jAlert(strMsg);
                        return;

                    },
                    error: function (msg) {
                        jAlert("Err" + msg);
                    }
                });
            });
        });
    </script>
</body>

</html>
