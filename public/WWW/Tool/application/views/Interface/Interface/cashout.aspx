﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="cashout.aspx.cs" Inherits="Users.Interface.cashout" %>

<!DOCTYPE html>
<!-- 
Interface/cashout.aspx/CashOut
{"NumCash":"10000"}
{"d":"{\"Code\":3,\"Money1\":0}"}  
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="cache-control" content="max-age=0" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <link rel="stylesheet" href="css/style.css" />
    <script src="js/jquery-1.8.3.min.js" type="text/javascript"></script>
   <script src="js/jAlert.js"></script>
    <script src="js/jAlert-functions.js"></script>
    <link href="css/jAlert.css" rel="stylesheet" />
    <title></title>
</head>
<body>
    <form id="form1" runat="server">
     <div class="wrapper">
            <div class="main pr cf">
                <div class="tool_bar">
                    <a href="#" onclick="document.location = 'js-oc:kunlunClose:null';return false" class="close"></a>
                    <a href="./" class="home"></a>
                    <div class="title">Máy chủ:<asp:Label ID="txtServerName" runat="server" Text="Server Name"></asp:Label></div>
                </div>
                <div class="top_bar"> Chào, <asp:Label ID="txtUserName" runat="server" Text="Label"></asp:Label>! Bạn đang có: <span id="curKimCuong" style="font-weight:bold"><span id="lbKimCuong"><asp:Label ID="txtMoney" runat="server" Text="Label"></asp:Label></span></span> Xu</div>
                <div class="box-data">
                    
    
    <div class="box-news">
        <h1>RÚT KIM CƯƠNG</h1>
        <h2><span id="ContentPlaceHolder1_lbMsgKhuyenMai"></span></h2>
        <select name="CountDiamond" id="count_diamond" class="login_input">
            <%
                foreach(string val in diamondMethodList)
                {
                    Response.Write(string.Format("<option value=\"{0}\">{0} Kim cương</option>", val));
                }
            %>

</select>        
        <a class="read-more" href="#" title="Xác nhận" id="btRutKC"><span>Xác Nhận</span></a>
    </div>
    <script type="text/javascript">
        $(document).ready(function (e) {
            $("#btRutKC").click(function (e) {
                $("#btRutKC").attr('disabled', 'disabled');
                e.preventDefault();
                var obj = {};
                obj.NumCash = $('#count_diamond :selected').val();
                var jsonData = JSON.stringify(obj);
                $.ajax({
                    type: "POST",
                    url: window.location.pathname + "/CashOut",
                    data: jsonData,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: function (msg) {
                        var message;
                        var Ret = JSON.parse(msg.d);
                        if (Ret.code == 0) {
                            $("#curKimCuong").html(Ret.money);
                            jAlertNotice(Ret.msg);
                        }
                        else {
                            jAlert(Ret.msg);
                        }
                        $("#btRutKC").removeAttr('disabled');
                        return;

                    },
                    error: function (msg) {
                        jAlert("Err" + msg);
                    }
                });
            })
        });
    </script>

    


                </div>
            </div>
        </div>
    </form>
</body>
</html>
