﻿html, body {
    height: 100%;
}

article, aside, details, figcaption, figure, footer, header, nav, section {
    display: block;
}

audio, canvas, video {
    display: inline-block;
}

    audio:not([controls]) {
        display: none;
    }

svg:not(:root) {
    overflow: hidden;
}

figure {
    margin: 0;
}

html, body, form, fieldset, p, div, h1, h2, h3, h4, h5, h6 {
    -webkit-text-size-adjust: none;
}



.cf:before, .cf:after {
    content: "";
    display: table;
}

.cf:after {
    clear: both;
}

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

a {
    text-decoration: none;
}

h1, h2, h3, h4, h5, p, ul {
    margin: 0;
    padding: 0;
    font-weight: normal;
}

.rs {
    margin: 0;
    padding: 0;
}

h1.rs, h2.rs, h3.rs, h4.rs {
    font-size: 100%;
    font-weight: bold;
}

ul.rs {
    list-style: none;
}

h1, h2, h3, h4 {
    margin: 0;
    padding: 0;
}

.rsbs, .rsbs:after, .rsbs:before {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
}

img {
    border: none;
}

a, input {
    outline: none;
}

a {
    color: #00f;
}

    a, a:hover {
        text-decoration: none;
    }

.fb {
    font-weight: bold;
}

button:focus {

}

li {
    list-style: none;
}

a:focus {
    text-decoration: none;
    border: none;
}

.pa {
    position: absolute;
}

.pr {
    position: relative;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

.tr {
    text-align: right;
}

.tc {
    text-align: center;
}



button::-moz-focus-inner, input[type="reset"]::-moz-focus-inner, input[type="button"]::-moz-focus-inner, input[type="submit"]::-moz-focus-inner, input[type="file"] > input[type="button"]::-moz-focus-inner {
    border: 0 !important;
    padding: 0 !important;
}

.fixCen {
    width: 1000px;
    margin: 0 auto;
}

.cf {
    clear: both;
}

@font-face {
    font-family: myfont;
    src: url(../font/MYRIADPRO-BOLD.OTF);
}

@font-face {
    font-family: myfont1;
    src: url(../font/MYRIADPRO-REGULAR.OTF);
}

img.bgf {
    width: 100%;
}


body {
    font-family: arial;
    font-size: 12px;
    width: 100%;
    background: #ffffff;
}

.wrapper {
    width: 100%;
    overflow: hidden;
}


.box-data {
    width: 97%;
    display: block;
    margin: 20px auto;
    padding-bottom: 20px;
    background: #fff;
}

.data-news1 {
    float: left;
    display: block;
    width: 25%;
    margin: 10px auto;
    text-align: center;
}

.td_value {   
    text-align: left;
}
.td_header {
    text-align: center;
    font:100;
}
.td_buttom {
    text-align: center;
}
.box-data a {
    text-align: center;
}

    .box-data a span {
        color: #2e2e2e;
        line-height: 35px;
        text-align: center;
        font-family: myfont1;
        font-size: 18px;
    }

/*######################## taikhoan ############################*/

.tool_bar {
    width: 100%;
    padding: 12px 15px;
    text-align: left;
    background-color: #ffffff;
    color: #000000;
    font-family: myfont1;
    font-size: 16px;
}

    .tool_bar div.title {
        color: #F00;
        font-size: 18px;
        font-weight: bold;
        text-align: center;
    }

    .tool_bar a {
        color: #36F;
    }

        .tool_bar a.home {
            background: url(../images/icon/icon-menu-home.png) no-repeat center;
            display: block;
            width: 40px;
            height: 54px;
            position: absolute;
            top: 0;
            left: 10px;
        }


        .tool_bar a.home_cart {
            background: url(../images/icon/icon-menu-cart.png) no-repeat center;
            display: block;
            width: 40px;
            height: 54px;
            position: absolute;
            top: 0;
            left: 10px;
        }

        .tool_bar a.close {
            background: url(../images/icon/icon-right.png) no-repeat center;
            display: block;
            width: 40px;
            height: 54px;
            position: absolute;
            top: 0;
            left: auto;
            right: 10px;
        }

.top_bar {
    width: 100%;
    text-align: left;
    padding-left: 15px;
    background-color: #e5e5e5;
    color: #000000;
    font-family: myfont1;
    font-size: 12px;
}

    .top_bar span {
        color: #F00;
    }

    .top_bar a {
        color: #36F;
    }

.box-news {
    width: 97%;
    display: block;
    margin: 10px auto;
    padding-bottom: 10px;
    background: #fff;
}

    .box-news.fs {
        border-top: none;
    }

    .box-news H1 {
        font-size: 16px;
        font-family: myfont1;
        padding: 10px 15px 10px 15px;
    }

        .box-news H1 span {
            font-size: 16px;
            font-family: myfont1;
            color: #F00;
        }

            .box-news H1 span.text1 {
                font-size: 16px;
                font-family: myfont1;
                color: #03F;
            }

    .box-news H2 {
        font-size: 14px;
        font-family: myfont1;
        padding: 10px 15px 10px 15px;
    }

        .box-news H2 span {
            font-size: 14px;
            font-family: myfont1;
            color: #F00;
        }

            .box-news H2 span.text1 {
                font-size: 16px;
                font-family: myfont1;
                color: #03F;
            }

    .box-news ul.lst-news {
        display: flex;
        height: 52px;
        background: #0e63d0;
        border-radius: 7px 7px 0 0;
        padding: 0 2%;
    }

        .box-news ul.lst-news li {
            width: 30%;
            height: 40px;
            margin-top: 12px;
            position: relative;
            float: left;
        }

            .box-news ul.lst-news li.active {
                background: #fff;
            }

                .box-news ul.lst-news li.active a {
                    color: #0e63d0;
                }

                .box-news ul.lst-news li.active:after {
                    content: "";
                    position: absolute;
                    right: -11px;
                    top: 0;
                    border-right: 10px solid rgba(131, 58, 3, 0);
                    border-bottom: 40px solid #07398e;
                    border-left: 0 solid rgba(131, 58, 3, 0);
                }

            .box-news ul.lst-news li a {
                display: block;
                text-align: center;
                line-height: 40px;
                color: #fff;
                font-family: myfont;
                text-transform: uppercase;
                font-size: 16px;
            }

    .box-news .login_input {
        width: 100%;
        height: 42px;
        margin: 0 0 5px 0;
        text-align: left;
        border: 1px solid #999;
        color: #868686;
        font-size: 14px;
        padding: 10px 10px;
    }

    .box-news .login_input1 {
        width: 50%;
        height: 42px;
        margin: 0 5px 5px 0;
        text-align: left;
        border: 1px solid #999;
        color: #868686;
        font-size: 14px;
        padding: 10px 10px;
    }


    .box-news a.read-more {
        display: block;
        width: 100%;
        margin: 0 auto;
        border-radius: 4px;
        background: #279aea;
        height: 35px;
        text-align: center;
    }

        .box-news a.read-more span {
            color: #fff;
            padding-right: 22px;
            background: #279aea;
            line-height: 35px;
            text-align: center;
            font-family: myfont1;
            font-size: 18px;
        }

/*######################## THE CAO ############################*/

.card a.card-more {
    float: left;
    display: block;
    width: 133px;
    margin: 5px 5px;
    border-radius: 4px;
    background: #e6e6e6;
    height: 66px;
    text-align: center;
}

    .card a.card-more span {
        padding: auto auto;
        background: #e6e6e6;
        line-height: 35px;
        text-align: center;
    }
/*######################## lichsugiaodich ############################*/
.ls_td {
    padding: 10px;
    font-weight: bold;
    border-top: 1px solid #e5e5e5;
    border-left: 1px solid #e5e5e5;
    border-right: 1px solid #e5e5e5;
    border-bottom: 3px solid #e5e5e5;
}
/*######################## Phuchoinhanvat ############################*/
.ph_td {
    padding: 10px;
    /*font-weight: bold;*/
    font-size: 14px;
}

.ph_td1 {
    padding: 10px;
    font-size: 14px;
    background: #f9f9f9;
    border-top: 3px solid #e5e5e5;
}

.ph_td2 {
    padding: 10px;
    font-size: 14px;
    background: #ffffff;
    border-top: 1px solid #e5e5e5;
}

/*######################## giftcode ############################*/
.giftcode a {
    float: left;
    width: 130px;
    height: 120px;
    border: 1px solid #999;
    display: block;
    border-radius: 4px 0 0 4px;
    background: #ffffff;
    padding: 5px 10px;
    text-align: center;
    font-size: 14px;
    margin: 0 0 10px 0;
}

    .giftcode a.active {
        float: left;
        width: 130px;
        height: 120px;
        border: 1px solid #999;
        display: block;
        border-radius: 4px 0 0 4px;
        background: #f4f4f4;
        padding: 5px 10px;
        text-align: center;
        font-size: 14px;
    }


.giftcode1 a {
    float: left;
    width: 130px;
    height: 120px;
    border-top: 1px solid #999;
    border-right: 1px solid #999;
    border-bottom: 1px solid #999;
    display: block;
    border-radius: 0px 0 0 0px;
    background: #ffffff;
    padding: 5px 10px;
    text-align: center;
    font-size: 14px;
    margin: 0 0 10px 0;
}

    .giftcode1 a.active {
        float: left;
        width: 130px;
        height: 120px;
        border-top: 1px solid #999;
        border-right: 1px solid #999;
        border-bottom: 1px solid #999;
        display: block;
        border-radius: 4px 0 0 4px;
        background: #f4f4f4;
        padding: 5px 10px;
        text-align: center;
        font-size: 14px;
    }


.giftcode2 a {
    float: left;
    width: 130px;
    height: 120px;
    border-top: 1px solid #999;
    border-right: 1px solid #999;
    border-bottom: 1px solid #999;
    display: block;
    border-radius: 0px 4px 4px 0px;
    background: #ffffff;
    padding: 5px 10px;
    text-align: center;
    font-size: 14px;
    margin: 0 0 10px 0;
}

    .giftcode2 a.active {
        float: left;
        width: 130px;
        height: 120px;
        border-top: 1px solid #999;
        border-right: 1px solid #999;
        border-bottom: 1px solid #999;
        display: block;
        border-radius: 4px 0 0 4px;
        background: #f4f4f4;
        padding: 5px 10px;
        text-align: center;
        font-size: 14px;
    }

.giftcode3 {
    float: left;
    width: 1000px;
    margin: 0 0 0 0;
}



.clearfix {
    clear: both;
}

.btn-radio {
    margin-bottom: 20px;
    clear: both;
}


    .btn-radio label {
        display: inline-block;
        color: #000;
        cursor: pointer;
        padding: 10px;
        margin-right: 3px;
    }

    .btn-radio input[type=radio] + label {
        border: 0.0625em solid rgb(192,192,192);
        border-radius: 2px;
        vertical-align: middle;
    }



#lean_overlay {
    position: fixed;
    z-index: 100;
    top: 0px;
    left: 0px;
    height: 100%;
    width: 100%;
    background: #000;
    display: none;
}
