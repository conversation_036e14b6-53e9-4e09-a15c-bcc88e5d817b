﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="card.aspx.cs" Inherits="Users.Interface.card" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="cache-control" content="max-age=0" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <link rel="stylesheet" href="css/style.css" />
    <script src="js/jquery-1.8.3.min.js" type="text/javascript"></script>
   <script src="js/jAlert.js"></script>
    <script src="js/jAlert-functions.js"></script>
    <link href="css/jAlert.css" rel="stylesheet" />
    <title></title>
</head>
<body>
    <form id="form1" runat="server">
       <div class="wrapper">
            <div class="main pr cf">
                <div class="tool_bar">
                    <a href="#" onclick="document.location = 'js-oc:kunlunClose:null';return false" class="close"></a>
                    <a href="./" class="home"></a>
                    <div class="title">Máy chủ:<asp:Label ID="txtServerName" runat="server" Text="Server Name"></asp:Label></div>
                </div>
                <div class="top_bar"> Chào, <asp:Label ID="txtUserName" runat="server" Text="Label"></asp:Label>! Bạn đang có: <asp:Label ID="txtMoney" runat="server" Text="Label"></asp:Label> Xu</div>
                <div class="box-data">
                    
    <div class="box-news">     
                <h1>NẠP THẺ CÀO
                    <span id="ContentPlaceHolder1_lbMsgKhuyenMai"></span></h1>
                <div class="card">
                    
                            <span class="btn-radio">
                        
                            <input type="radio" value="gate" id="row0" name="listCardTypeItems" />
                            <label for="row0">
                                <img src="images/card/gate.png" width="121" height="62"></label>
                        
                            <input type="radio" value="mobi" id="row1" name="listCardTypeItems" />
                            <label for="row1">
                                <img src="images/card/mobifone.png" width="121" height="62"></label>
                        
                            <input type="radio" value="viettel" id="row2" name="listCardTypeItems" checked="checked" />
                            <label for="row2">
                                <img src="images/card/viettel.png" width="121" height="62"></label>
                        
                            <input type="radio" value="vina" id="row3" name="listCardTypeItems" />
                            <label for="row3">
                                <img src="images/card/vinaphone.png" width="121" height="62"></label>
                        
                            </span>
                        
                </div>
                <div class="clearfix"></div>
                <br />
                <br />
                <input type="text" class="login_input" placeholder="Số thẻ (Serial)" id="cardserial" />
                <input type="text" class="login_input" placeholder="Mã thẻ (Pin)" id="cardnumber" />
                <br />
                <br />
                <a class="read-more" href="javascript:void(0);" id="btCardExchan" title="Xem thêm"><span>Xác Nhận</span></a>
                <br />
                <h2 id="ContentPlaceHolder1_lbTiLe">Tỉ lệ nạp thẻ <b>10,000 tương ứng <asp:Label ID="txtRateCharge" runat="server" Text="Label"></asp:Label></b> Xu</h2>


                <script type="text/javascript">
                    var isProcessing = false;
                    function doCardExchan(type, serial, number) {
                        if (isProcessing)
                            return;
                        isProcessing = true;
                        var obj = {};
                        obj.cardType = type;
                        obj.Number = number;
                        obj.Serial = serial;
                        var jsonData = JSON.stringify(obj);
                        $.ajax({
                            type: "POST",
                            url: window.location.pathname + "/CardExchan",
                            data: jsonData,
                            contentType: "application/json; charset=utf-8",
                            dataType: "json",
                            success: function (msg) {
                                isProcessing = false;
                                var Ret = JSON.parse(msg.d);
                                if (Ret.type == 0) {
                                    $("#txtMoney").html(Ret.moneygame);
                                    jAlertNotice(Ret.content);
                                }
                                else {
                                    jAlert(Ret.content);
                                }
                            },
                            error: function (msg) {
                                isProcessing = false;
                                jAlert("Err" + msg);
                            }
                        });
                    }

                    $(document).ready(function (e) {
                        $("#btCardExchan").click(function () {
                            cardType = $('input[name=listCardTypeItems]:checked').val();
                            if (cardType == undefined) {
                                jAlert("Xin chọn loại thẻ");
                                return "";
                            }
                            Serial = $("#cardserial").val();
                            Number = $("#cardnumber").val();
                            doCardExchan(cardType, Serial, Number);
                        });
                    });

                </script>
            
    </div>

                </div>
            </div>
        </div>
    </form>
</body>
</html>
