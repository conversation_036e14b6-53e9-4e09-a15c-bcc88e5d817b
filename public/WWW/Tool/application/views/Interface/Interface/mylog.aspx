﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="mylog.aspx.cs" Inherits="Users.Interface.mylog" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
      <meta http-equiv="cache-control" content="max-age=0" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <link rel="stylesheet" href="css/style.css" />
    <script src="js/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="js/jAlert.js"></script>
    <script src="js/jAlert-functions.js"></script>
    <link href="css/jAlert.css" rel="stylesheet" />
    <title></title>
</head>
<body>
    <form id="form1" runat="server">
        <div class="wrapper">
            <div class="main pr cf">
                <div class="tool_bar">
                    <a href="#" onclick="document.location = 'js-oc:kunlunClose:null';return false" class="close"></a>
                    <a href="./" class="home"></a>
                    <div class="title">Máy chủ:<span id="lbServerName"><asp:Label ID="txtServerName" runat="server" Text="Server Name"></asp:Label></span></div>
                </div>
                <div class="top_bar"> Chào, <asp:Label ID="txtUserName" runat="server" Text="Label"></asp:Label>! Bạn đang có: <span id="curKimCuong" style="font-weight:bold"><span id="lbKimCuong"><asp:Label ID="txtMoney" runat="server" Text="Label"></asp:Label></span></span> Xu</div>
                <div class="box-data">
                    
    <div class="box-news">
        <h1>LỊCH SỬ GIAO DỊCH</h1>
        <h2>Thống kê 50 lịch sử gần nhất</h2>
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
        </table>


    </div>

    
                    <table class="table" id="example3" style="width: 100%">
                        <thead>
                            <tr>
                                <td width="10%" class="ls_td">STT</td>
                                <td width="10%" class="ls_td">Loại</td>
                                <td width="55%" class="ls_td">Mô tả</td>
                                <td width="25%" class="ls_td">Thời gian</td>
                            </tr>
                        </thead>
                        <tbody>
                            <%
                                if (LogList.Count > 0)
                                {
                                    foreach (Users.Models.TranLogInfo info in LogList)
                                    {
                            %>

                            <tr>
                                <td width="10%" class="ph_td"><%=info.Id %></td>
                                <td width="10%" class="ph_td"><%=info.title %></td>
                                <td width="55%" class="ph_td"><%=info.content %></td>
                                <td width="25%" class="ph_td"><%=info.timecreate.ToString() %></td>
                            </tr>

                            <%
                                    }
                                }
                            %>
                        </tbody>
                    </table>
        


                </div>
            </div>
        </div>
    </form>
</body>
</html>
