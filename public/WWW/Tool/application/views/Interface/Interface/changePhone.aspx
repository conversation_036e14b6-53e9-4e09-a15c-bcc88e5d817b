﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ChangePhone.aspx.cs" Inherits="Users.Interface.ChangePhone" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
      <meta http-equiv="cache-control" content="max-age=0" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <link rel="stylesheet" href="css/style.css" />
    <script src="js/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="js/jAlert.js"></script>
    <script src="js/jAlert-functions.js"></script>
    <link href="css/jAlert.css" rel="stylesheet" />
    <title></title>
</head>
<body>
    <form id="form1" runat="server">
       <div class="wrapper">
            <div class="main pr cf">
                <div class="tool_bar">
                    <a href="#" onclick="document.location = 'js-oc:kunlunClose:null';return false" class="close"></a>
                    <a href="./" class="home"></a>
                    <div class="title">Máy chủ:<asp:Label ID="txtServerName" runat="server" Text="Server Name"></asp:Label></div>
                </div>
                <div class="top_bar"> Chào, <asp:Label ID="txtUserName" runat="server" Text="Label"></asp:Label>!</div>
                <div class="box-data">
                    
    <div class="box-data">
        <div class="box-news" id="frmChangePhone">
            <h1>XÁC THỰC THÔNG TIN</h1>
            <h2>Bạn hãy sử dụng số điện thoại <span id="ContentPlaceHolder1_txtOldPhone">01674344444</span> soạn tin nhắn <span>DTP MUOL gửi 8098</span> để lấy mã OTP.</h2>
            <input type="text" class="login_input" id="txtOTP" placeholder="Mã OTP" />
            <input type="text" class="login_input" id="txtNewPhone" placeholder="Số điện thoại mới" />
            <a class="read-more" href="javascript:void(0);" id="btChangePhone"><span>Đổi số điện thoại</span></a>
        </div>
        <div class="box-news" id="frmFinish" style="display: none;">
            <h1>KẾT THÚC QUÁ TRÌNH</h1>
            <h2><span>Xin chúc mừng</span><br />
                Bạn đã thay đổi số điện thoại thành công.</h2>
            <br />
            <a class="read-more" href="javascript:void(0);" onclick="document.location = 'js-oc:kunlunClose:null';return false"><span>Đóng </span></a>
        </div>
    </div>
    <script type="text/javascript">
        $("#btChangePhone").click(function (e) {
            var obj = {};
            obj.newPhone = $('#txtNewPhone').val();
            obj.OTP = $('#txtOTP').val();
            if ((obj.NewPhone == "") || (obj.OTP == "")) {
                jAlert("Vui lòng nhập số điện thoại đăng ký và mã OTP");
                return;
            }
            var jsonData = JSON.stringify(obj);
            $.ajax({
                type: "POST",
                url: window.location.pathname + "/ChangePhone",
                data: jsonData,
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (msg) {
                    $('#txtNewPass').removeClass("error");
                    $('#txtOTP').removeClass("error");
                    switch (msg.d) {
                        case 0:
                            $("#frmChangePhone").hide();
                            $("#frmFinish").show();
                            return;
                        case 1:
                            strMsg = 'Số điện thoại mới không đúng cấu trúc';
                            $('#txtNewPhone').addClass("error");
                            break;
                        case 2:
                            strMsg = 'Cập nhật dữ liệu thất bại';
                            break;
                        case 3:
                            strMsg = 'OTP không chính xác';
                            $('#txtOTP').addClass("error");
                            break;
                        default:
                            strMsg = 'Không rõ lỗi (' + msg.d + ')';
                            break;
                    }
                    jAlert(strMsg);
                    return;

                },
                error: function (msg) {
                    jAlert("Err" + msg);
                }
            });
        });

    </script>
                </div>
            </div>
        </div>
    </form>
</body>
</html>
