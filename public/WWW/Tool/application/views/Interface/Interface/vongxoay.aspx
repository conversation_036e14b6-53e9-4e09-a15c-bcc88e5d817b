﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="vongxoay.aspx.cs" Inherits="Users.Interface.vongxoay" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
      <meta http-equiv="cache-control" content="max-age=0" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <link rel="stylesheet" href="css/style.css" />
    <script src="js/jquery-1.8.3.min.js" type="text/javascript"></script>
   <script src="js/jAlert.js"></script>
    <script src="js/jAlert-functions.js"></script>
    <link href="css/jAlert.css" rel="stylesheet" />
     <link href="css/vongxoay.css" rel="stylesheet" />
    <script src="js/jQueryRotate.js"></script>
    <script src="js/VongXoay.js"></script>
    <title></title>
</head>
<body>
    <form id="form1" runat="server">
      <div class="wrapper">
            <div class="main pr cf">
                 <div class="tool_bar">
                    <a href="#" onclick="document.location = 'js-oc:kunlunClose:null';return false" class="close"></a>
                    <a href="./" class="home"></a>
                    <div class="title">Máy chủ:<asp:Label ID="txtServerName" runat="server" Text="Server Name"></asp:Label></div>
                </div>
                <div class="top_bar"> Chào, <asp:Label ID="txtUserName" runat="server" Text="Label"></asp:Label>! Bạn đang có: <span id="curKimCuong" style="font-weight:bold"><span id="lbKimCuong"><asp:Label ID="txtMoney" runat="server" Text="Label"></asp:Label></span></span> Xu</div>
                <div class="box-data">
                    
    <div class="box-news">
        <table style="width: 100%; margin: 14px;" id="frmVongXoayMayMan">
            <tr>
                <td style="width: 345px; vertical-align: top;">
                    <div id="vxmm" style="padding: 10px;">
                        <a id="btQuay" href="#"></a>
                        <div id="VongXoay">
                            <span id="item1"></span>
                            <span id="item2"></span>
                            <span id="item3"></span>
                            <span id="item4"></span>
                            <span id="item5"></span>
                            <span id="item6"></span>
                            <span id="item7"></span>
                            <span id="item8"></span>
                        </div>
                    </div>
                    <div style="text-align:center;">
                    <h2 id="ContentPlaceHolder1_lbFee">Tốn <span>2999 Xu / 1</span> lần quay</h2>
                        </div>
                </td>
                <td style="vertical-align: top;">
                    <h2>Nhân vật: <span id="ContentPlaceHolder1_tbSelectChar">Topsy-Powell</span></h2>
                    
                            <table style="width: 100%">
                                <thead>
                                    <tr>
                                        <td class="ls_td" style="width: 150px">Thời gian</td>
                                        <td class="ls_td">Ghi chú</td>
                                    </tr>
                                </thead>
                                <tbody id="dataBodyLog">
                        
                            </tbody>
                    </table>
                        
                </td>
            </tr>
        </table>
    </div>
    <script type="text/javascript">
        var isProcessing = false;
        $(document).ready(function (e) {
            $("#btQuay").on("click", function (e) {
                jAlert("Chức năng chưa mở!");
                return;
                e.preventDefault();
                if (isProcessing) return false;
                var obj = {};
                var jsonData = JSON.stringify(obj);
                $.ajax({
                    type: "POST",
                    url: window.location.pathname + "/DoProcess",
                    data: jsonData,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: function (data) {
                        getVongQuayData = JSON.parse(data.d);
                        if (getVongQuayData.ErrCode == 0) {
                            $("#curKimCuong").text(getVongQuayData.Money1);
                            if (getVongQuayData && (getVongQuayData.Idx >= 0) && (getVongQuayData.ErrCode >= 0)) {
                                rotation(getVongQuayData.Idx, getVongQuayData.Message);
                            }
                            else
                                jAlert("System is ereor 1000, please contact tech support!");
                        } else {
                            jAlert(getVongQuayData.Message);
                            isProcessing = false;
                        }
                    },
                    error: function (request, status, error) {
                        document.write(request.responseText);
                        isProcessing = false;
                        jAlert("Wwoops something went wrong !");
                    }
                });
            }
            );
        });
    </script>

                </div>
            </div>
        </div>
    </form>
</body>
</html>
