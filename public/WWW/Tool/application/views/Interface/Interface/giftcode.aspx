﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="giftcode.aspx.cs" Inherits="Users.Interface.giftcode" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
      <meta http-equiv="cache-control" content="max-age=0" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <link rel="stylesheet" href="css/style.css" />
    <script src="js/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="js/jAlert.js"></script>
    <script src="js/jAlert-functions.js"></script>
    <link href="css/jAlert.css" rel="stylesheet" />
    <title></title>
</head>
<body>
    <form id="form1" runat="server">
      <div class="wrapper">
            <div class="main pr cf">
                <div class="tool_bar">
                    <a href="#" onclick="document.location = 'js-oc:kunlunClose:null';return false" class="close"></a>
                    <a href="./" class="home"></a>
                    <div class="title">Máy chủ:<asp:Label ID="txtServerName" runat="server" Text="Server Name"></asp:Label></div>
                </div>
                <div class="top_bar"> Chào, <asp:Label ID="txtUserName" runat="server" Text="Label"></asp:Label>! Bạn đang có: <span id="curKimCuong" style="font-weight:bold"><span id="lbKimCuong"><asp:Label ID="txtMoney" runat="server" Text="Label"></asp:Label></span></span> Xu</div>
                <div class="box-data">
                    
    <div class="box-news">
        <h1>NHẬP GIFTCODE</h1>
        <div>
            <h2>Quà sẻ được chuyển vào hộp thư nhân vật: <asp:Label ID="txtRoleName" runat="server" Text="Label"></asp:Label></h2>
			<h2>GiftCode S01: ytrplus01, klfplus01</h2>

        </div>

        <input type="text" class="login_input" value="" id="txtCode" placeholder="Nhập mã Giftcode vào" />
        <div>

        <a class="read-more" href="javascript:void(0)" title="Xem thêm" id="btGetGiftcode"><span>Nhận thưởng</span></a>
        </div>
    </div>
    <script type="text/javascript">
        var isProcessing = false;
        function doGiveCode(Code) {
            if (isProcessing)
                return;
            isProcessing = true;
            var obj = {};
            obj.Code = Code;
            var jsonData = JSON.stringify(obj);
            $.ajax({
                type: "POST",
                url: window.location.pathname + "/GiveCode",
                data: jsonData,
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (msg) {
                    var strMsg = "";
                    isProcessing = false;
                    switch (msg.d) {
                        case 0:
                            jAlertNotice("Nhân giftcode thành công");
                            return;
                        case 1:
                            jAlert("Mã code không tồn tại");
                            return;
                        case 2:
                            jAlert("Code đã hết số lần sử dụng");
                            return;
                        case 3:
                            jAlert("Gửi mail thất bại");
                            return;
                        case 5:
                            jAlert("Mỗi loại code chỉ dùng được 1 lần");
                            return;
                        case 6:
                            jAlert("Code không áp dụng ở Server này");
                            return;
                        case 7:
                            jAlert("Code không áp dụng cho nhân vật của bạn");
                            return;
                        case 8:
                            jAlert("Hệ thống bận, xin thử lại sau 5 giây");
                            return;
                        case 9:
                            jAlert("Code chỉ dùng 1 lần cho 1 tài khoản và 1 nhân vật");
                            return;
                        case 11:
                            jAlert("Code chỉ dùng cho 1 tài khoản");
                            return;
                        default:
                            jAlert("Không rõ lỗi");
                            break;
                    }
                    
                    return;

                },
                error: function (request, status, error) {
                    isProcessing = false;
                    document.write(request.responseText);
                }
            });
        }

        $(document).ready(function (e) {
            $("#btGetGiftcode").click(function () {
                Code = $("#txtCode").val();                
                doGiveCode(Code);
            });
        });

    </script>

                </div>
            </div>
        </div>
    </form>
</body>
</html>
