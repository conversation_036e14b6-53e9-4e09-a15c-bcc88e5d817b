﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="account.aspx.cs" Inherits="Users.Interface.account" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
     <meta http-equiv="cache-control" content="max-age=0" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <link rel="stylesheet" href="css/style.css" />
    <script src="js/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="js/jAlert.js"></script>
    <script src="js/jAlert-functions.js"></script>
    <link href="css/jAlert.css" rel="stylesheet" />
    <title></title>
</head>
<body>
    <form id="form1" runat="server">
        <div class="wrapper">
            <div class="main pr cf">
               <div class="tool_bar">
                    <a href="#" onclick="document.location = 'js-oc:kunlunClose:null';return false" class="close"></a>
                    <a href="./" class="home"></a>
                    <div class="title">Máy chủ:<asp:Label ID="txtServerName" runat="server" Text="Server Name"></asp:Label></div>
                </div>
                <div class="top_bar"> Chào, <asp:Label ID="txtUserName" runat="server" Text="Label"></asp:Label>!</div>
                <div class="box-data">
                    
    <div class="box-news">
        <ul class="lst-news">
            <li class="active"><a href="javascript:void(0)" title="Tất cả">Tài khoản</a> </li>
            <li><a href="javascript:void(0)" title="Tin tức">Đổi mật khẩu</a></li>
        </ul>

        <div class="data-news">
            <h1>THÔNG TIN TÀI KHOẢN</h1>
            <% if (Users.Interface.account.IsFirstTimeUpdateInfo)
                {%>
                   <h2> Bạn chưa cập nhật thông tin tài khoản, xin cập nhật
                        <br />
                       <span style="color:red">Ghi chú: </span> việc cập nhật chỉ thực hiện được 1 lần duy nhất vì thế bạn phải kiểm tra kỹ trước khi thực hiện</h2>
                    <input name="txtPhone" type="text" id="ContentPlaceHolder1_txtPhone" class="login_input" placeholder="Điện thoại: ex. ***********" />
                    <input name="txtEmail" type="text" id="ContentPlaceHolder1_txtEmail" class="login_input" placeholder="Email: ex. <EMAIL>" />
                    <a class="read-more" href="javascript:vold(0);" id="btUpdateInfo"><span>Cập nhật</span></a>
            <% }
    else
    {%>
          <h2>  Thông tin tài khoản, liên hệ trung tâm hỗ trợ để thay đổi thông tin.
                        <br /></h2> 
                    <input disabled="disabled" name="txtPhone" type="text" id="ContentPlaceHolder2_txtPhone" class="login_input" value="<%=Users.Interface.account.CurrentMail %>" />
                    <input disabled="disabled" name="txtEmail" type="text" id="ContentPlaceHolder2_txtEmail" class="login_input" value="<%=Users.Interface.account.CurrentPhone %>"/>
                   <!-- <a class="read-more" href="javascript:vold(0);" id="btChangeInfo"><span>Đổi số điện thoại</span></a> -->
            <%} %>
                    <script>
                        $(document).ready(function () {
                            $("#btUpdateInfo").click(function () {
                                var Phone = $("#ContentPlaceHolder1_txtPhone").val();
                                var Email = $("#ContentPlaceHolder1_txtEmail").val();
                                if ((Phone == "") || (Email) == "") {
                                    jAlert("Xin nhập đầy đủ Email và số điện thoại");
                                    return;
                                }
                                if ($.isNumeric(Phone) <= 0) {                                  
                                    alert('Điện thoại sai cấu trúc!');
                                    return;
                                }
                                var obj = {};
                                obj.Phone = Phone;
                                obj.Email = Email;
                                var jsonData = JSON.stringify(obj);
                                $.ajax({
                                    type: "POST",
                                    url: window.location.pathname + "/FirstTimeUpdateInfo",
                                    data: jsonData,
                                    contentType: "application/json; charset=utf-8",
                                    dataType: "json",
                                    success: function (msg) {
                                        var strMsg = "";
                                        switch (msg.d) {
                                            case 0:
                                                location.reload(false);
                                                return;
                                            case 1:
                                                strMsg = "Email sai cấu trúc";
                                                break;
                                            case 2:
                                                strMsg = "Điện thoại sai cấu trúc";
                                                break;
                                            case 3:
                                                strMsg = "Chưa đăng nhập, xin thoát ra vào lại";
                                                break;
                                            case 4:
                                                strMsg = "Email hoặc Điện thoại không được để trống";
                                                break;
                                            case 5:
                                                strMsg = "Số điện thoại đã đăng ký";
                                                break;
                                            case 6:
                                                strMsg = "Email đã tồn tại";
                                                break;
                                            default:
                                                break;
                                        }
                                        jAlert(strMsg);
                                        return;
                                    },
                                    error: function (msg) {
                                        jAlert("Err" + msg);
                                    }
                                });
                            });
                        });
                    </script>
                
        </div>

        <div class="data-news">
            <h1>ĐỔI MẬT KHẨU</h1>
            <input type="password" class="login_input" placeholder="Mật khẩu cũ" id="txtOldPass" />
            <input type="password" class="login_input" placeholder="Nhập mật khẩu mới" id="txtNewPass" />
            <input type="password" class="login_input" placeholder="Nhập lại mật khẩu mới" id="txtReNewPass" />
            <a class="read-more" href="javascript:vold(0);" id="btChangePass"><span>Xác Nhận</span></a>
        </div>
    </div>

    <script>
        $(document).ready(function () {
            $('a.bt-menu').click(function () {
                $(this).toggleClass('active');
                $('.menu-top').toggleClass('active');

            });
            var itemtab = $('.lst-news li');
            var data_news = $('.data-news');
            data_news.hide();
            data_news.first().show();
            itemtab.click(function () {
                itemtab.removeClass('active');
                $(this).addClass('active');
                var it_active = $(this).index();
                data_news.hide();
                data_news.eq(it_active).show();
            });

            $('#btChangePass').click(function () {
                var OldPass = $("#txtOldPass").val();
                var NewPass = $("#txtNewPass").val();
                var ReNewPass = $("#txtReNewPass").val();
                if ((OldPass == "") || (NewPass) == "") {
                    jAlert("Xin nhập đầy đủ mật khẩu cũ và mật khẩu mới");
                    return;
                }
                if (ReNewPass != NewPass) {
                    jAlert("Mật khẩu mới và nhập lại mật khẩu mới không giống nhau.");
                    return;
                }
                var obj = {};
                obj.OldPass = OldPass;
                obj.NewPass = NewPass;
                var jsonData = JSON.stringify(obj);
                $.ajax({
                    type: "POST",
                    url: window.location.pathname + "/ChangePassword",
                    data: jsonData,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: function (msg) {
                        var strMsg = "";
                        switch (msg.d) {
                            case 0:
                                strMsg = "Thay đổi mật khẩu thành công";
                                break;
                            case 1:
                                strMsg = "Mật khẩu cũ không khớp";
                                break;
                            case 2:
                                strMsg = "Cập nhật mật khẩu thất bại";
                                break;
                            case 3:
                                strMsg = "Mật khẩu từ 6 tới 20 ký tự";
                                break;
                            case 4:
                                strMsg = "Bạn chưa cập nhật thông tin không thể đổi mật khẩu";
                                break;
                            case 5:
                                strMsg = "Chưa đăng nhập, xin thoát ra vào lại";
                                break;
                            default:
                                strMsg = "Lỗi chưa xác định";
                                break;
                        }
                        jAlert(strMsg);
                        return;

                    },
                    error: function (msg) {
                        jAlert("Err" + msg);
                    }
                });
            });
        });

    </script>


                </div>
            </div>
        </div>
    </form>
</body>
</html>
