﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="undeletechar.aspx.cs" Inherits="Users.Interface.undeletechar" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
       <meta http-equiv="cache-control" content="max-age=0" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <link rel="stylesheet" href="css/style.css" />
    <script src="js/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="js/jAlert.js"></script>
    <script src="js/jAlert-functions.js"></script>
    <link href="css/jAlert.css" rel="stylesheet" />
    <title></title>
</head>
<body>
    <form id="form1" runat="server">
        <div class="wrapper">
            <div class="main pr cf">
                <div class="tool_bar">
                    <a href="#" onclick="document.location = 'js-oc:kunlunClose:null';return false" class="close"></a>
                    <a href="./" class="home"></a>
                    <div class="title">Máy chủ:<asp:Label ID="txtServerName" runat="server" Text="Server Name"></asp:Label></div>
                </div>
                <div class="top_bar"> Chào, <asp:Label ID="txtUserName" runat="server" Text="Label"></asp:Label>! Bạn đang có: <span id="curKimCuong" style="font-weight:bold"><span id="lbKimCuong"><asp:Label ID="txtMoney" runat="server" Text="Label"></asp:Label></span></span> Xu</div>
                <div class="box-data">
                    
    <div class="box-news">
        <h1>PHỤC HỒI NHÂN VẬT</h1>
        <h2>Khôi phục tài khoản tiêu hao <strong></strong> Xu<br>
            <span>Sau khi thực hiện phục hồi nhân vật yêu cầu người chơi không truy cấp máy chủ khôi phục trong 3 giờ kế tiếp</span></h2>

        
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td width="30%" class="ph_td">Nhân vật</td>
                        <td width="20%" class="ph_td">Chủng tộc</td>
                        <td width="10%" class="ph_td">Level</td>
                        <td width="20%" class="ph_td">Chuyển sinh</td>
                        <td width="20%" class="ph_td">Tình trạng</td>
                    </tr>
            
                </table>                   
            
    </div>


    <script type="text/javascript">
        function doUndeleteCharacter(RoleID) {
            var obj = {};
            obj.RoleId = RoleID;
            var jsonData = JSON.stringify(obj);
            $.ajax({
                type: "POST",
                url: window.location.pathname + "/UnDelRole",
                data: jsonData,
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (msg) {
                    var strMsg = "";
                    jAlert(msg.d);
                    var Ret = JSON.parse(msg.d);
                    switch (Ret.Code) {
                        case 0:
                            jAlert("Phục hồi nhân vật thành công");
                            location.reload(false);
                            return;
                        case 1:
                            jAlert("Xin đăng nhập lại");
                            break;
                        case 2:
                            jAlert("Không đủ xu trong tài khoản");
                            break;
                        case 4:
                            jAlert("Nhân vật chưa bị xóa");
                            break;
                        case 5:
                            jAlert("Không thấy nhân vật");
                            break;
                        case 6:
                            jAlert("Cập nhật lỗi");
                            break;
                        case 7:
                            jAlert("Xin thử lại sau 3 giây");
                            break;
                        default:
                            strMsg = 'Cập nhật thất bại';
                            break;
                    }
                    Messenger().post({ message: strMsg, type: 'error', showCloseButton: true });
                    return;

                },
                error: function (msg) {
                    jAlert("Err" + msg);
                }
            });
        }

        $(document).ready(function (e) {
            $(".btn").click(function () {
                RoleId = $(this).attr("data-rid");
                doUndeleteCharacter(RoleId);
            });
        });

    </script>

                </div>
            </div>
        </div>
    </form>
</body>
</html>
