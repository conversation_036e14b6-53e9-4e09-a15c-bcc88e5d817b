﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="gmtool.aspx.cs" Inherits="Users.Interface.gmtool" %>


<!DOCTYPE html>
<!-- 
Interface/cashout.aspx/CashOut
{"NumCash":"10000"}
{"d":"{\"Code\":3,\"Money1\":0}"}  
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="cache-control" content="max-age=0" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />

    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/3.3.0/css/bootstrap.min.css">
    <script src="//maxcdn.bootstrapcdn.com/bootstrap/3.3.0/js/bootstrap.min.js"></script>
    
    

    <link rel="stylesheet" href="css/style.css" />
    <script src="js/jquery-1.8.3.min.js" type="text/javascript"></script>
    <link href="css/jAlert.css" rel="stylesheet" />
    <script src="js/jAlert.js"></script>
    <script src="js/jAlert-functions.js"></script>

    <title></title>
    <link rel="stylesheet" type="text/css" href="DataTables/datatables.css">
    <script type="text/javascript" charset="utf8" src="DataTables/datatables.js"></script>


</head>
<body>

    <form id="form1" runat="server">
        <div class="wrapper">
            <div class="main pr cf">
                <div class="tool_bar">
                    <a href="#" onclick="document.location = 'js-oc:kunlunClose:null';return false" class="close"></a>
                    <a href="./" class="home"></a>
                    <div class="title">
                        GM Tool - Máy chủ:
                        <asp:Label ID="txtServerName" runat="server" Text="Server Name"></asp:Label>
                    </div>
                </div>
                <div class="top_bar">
                    Chào,
                    <asp:Label ID="txtUserName" runat="server" Text="Label"></asp:Label>! Bạn đang có: <span id="curKimCuong" style="font-weight: bold"><span id="lbKimCuong">
                        <asp:Label ID="txtMoney" runat="server" Text="Label"></asp:Label></span></span> Xu
                </div>
            </div>
        </div>
    </form>



    <form class="form-inline" id="itemGoodlist">

        <div class="row">
            <div class="form-group">
                <input type="text" class="form-control input-xs smallinput" value="Item: " disabled>
            </div>
            <div class="form-group">
                <input type="text" class="form-control input-xs smallinput" id="inputGoodId1" name="inputGoodId[]" placeholder="Id" required onkeypress="return isNumberKey(event)">
            </div>
            <div class="form-group">
                <input type="text" class="form-control input-xs smallinput" id="inputGoodQuantity1" name="inputGoodQuantity[]" placeholder="Quantity" required onkeypress="return isNumberKey(event)">
            </div>
            <div class="form-group">
                <input type="checkbox" id="checkGoodBinding1" name="checkGoodBinding[]">
            </div>
            <div class="form-group">
                <input type="text" class="form-control input-xs smallinput" id="inputGoodForgeLevel1" name="inputGoodForgeLevel[]" placeholder="20">
            </div>
            <div class="form-group">
                <input type="checkbox" id="checkGoodLucky1" name="checkGoodLucky[]" checked>
            </div>
            <div class="form-group">
                <input type="text" class="form-control input-xs smallinput" id="inputGoodExcellenceInfo1" name="inputGoodExcellenceInfo[]" placeholder="235">
            </div>
            <div class="form-group">
                <input type="text" class="form-control input-xs smallinput" id="inputGoodAppendPropLevel1" name="inputGoodAppendPropLevel[]" placeholder="80">
            </div>
            <div class="form-group">
                <!-- <button class="btn btn-danger btn-xs" type="button" onclick="remove_item_fields(1);">Remove</button> -->
                <button class="btn btn-xs" type="button">Remove</button>
            </div>

        </div>

        <div class="row">
            <div class="form-group">
                <button type="submit" class="btn btn-primary btn-xs">Send mail</button>
            </div>
        </div>
    </form>





    <table id="goodslist" class="display" style="width: 100%">
        <thead>
            <tr>
                <th>Id</th>
                <th>Title</th>
                <th>Add</th>
            </tr>
        </thead>
    </table>


    <style>
        table.dataTable tbody th, table.dataTable tbody td {
            padding: 4px 5px;
        }

        .dataTables_wrapper {
            position: relative;
            clear: both;
            max-width: 600px;
            zoom: 1;
        }

        #events {
            margin-bottom: 1em;
            padding: 1em;
            background-color: #f6f6f6;
            border: 1px solid #999;
            border-radius: 3px;
            height: 100px;
            overflow: auto;
        }

        .form-inline .row {
            margin: 5px;
        }

        .dataTables_wrapper .dataTables_filter {
            float: left;
            text-align: left;
        }

        .form-inline .form-group .smallinput {
            width: 80px;
        }

        .input-xs {
            height: 22px;
            font-size: 12px;
            line-height: 1.5;
            border-radius: 3px;
        }
    </style>

    <script type="text/javascript">

        $('#itemGoodlist').on('submit', function (e) {

            var rowCount = $('#itemGoodlist .row').length;

            var goodIdArray = new Array();
            $('input[name^="inputGoodId"]').each(function () {
                goodIdArray.push($(this).val());
            });
            var goodQuantityarray = new Array();
            $('input[name^="inputGoodQuantity"]').each(function () {
                goodQuantityarray.push($(this).val());
            });
            var goodBindingArray = new Array();
            $('input[name^="checkGoodBinding"]').each(function () {
                goodBindingArray.push(0 + $(this).is(':checked'));
            });
            var goodForgeLevelArray = new Array();
            $('input[name^="inputGoodForgeLevel"]').each(function () {
                goodForgeLevelArray.push($(this).val());
            });
            var goodLuckyArray = new Array();
            $('input[name^="checkGoodLucky"]').each(function () {
                goodLuckyArray.push(0 + $(this).is(':checked'));
            });
            var goodExcellenceInfoArray = new Array();
            $('input[name^="inputGoodExcellenceInfo"]').each(function () {
                goodExcellenceInfoArray.push($(this).val());
            });
            var goodAppendPropLevelArray = new Array();
            $('input[name^="inputGoodAppendPropLevel"]').each(function () {
                goodAppendPropLevelArray.push($(this).val());
            });

            //Cach khac
            //var values = $('input[name^="inputGoodId"]').map(function () { return $(this).val(); }).get();

            var arrGiftcodeinfo = new Array();
            var i;
            for (i = 0; i < numitem - 1; i++) {
                var giftcodeinfo = {};
                //init
                giftcodeinfo.appendproplev = 0;
                giftcodeinfo.binding = 0;
                giftcodeinfo.excellenceinfo = 0;
                giftcodeinfo.forge_level = 0;
                giftcodeinfo.gcount = 0;
                giftcodeinfo.gift_id = 0;
                giftcodeinfo.goodsid = 0;
                giftcodeinfo.id = 0;
                giftcodeinfo.lucky = 0;

                //parse
                giftcodeinfo.appendproplev = parseInt(goodAppendPropLevelArray[i]) || 0;
                giftcodeinfo.binding = parseInt(goodBindingArray[i]) || 0;
                giftcodeinfo.excellenceinfo = parseInt(goodExcellenceInfoArray[i]) || 0;
                giftcodeinfo.forge_level = parseInt(goodForgeLevelArray[i]) || 0;
                giftcodeinfo.gcount = parseInt(goodQuantityarray[i]) || 0;
                giftcodeinfo.gift_id = -1;
                giftcodeinfo.goodsid = parseInt(goodIdArray[i]) || 0;
                giftcodeinfo.id = -1;
                giftcodeinfo.lucky = parseInt(goodLuckyArray[i]) || 0;

                //alert(giftcodeinfo.goodsid + " " + giftcodeinfo.gcount + " " + giftcodeinfo.binding + " " + giftcodeinfo.forge_level + " " + giftcodeinfo.lucky);

                arrGiftcodeinfo.push(giftcodeinfo);
            }


            // if the validator does not prevent form submit
            if (!e.isDefaultPrevented()) {
                var url = window.location.pathname + "/sendmail";

                // POST values in the background the the script URL
                $.ajax({
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    dataType: 'json',
                    url: url,
                    data: "{giftcodeinfo:" + JSON.stringify(arrGiftcodeinfo) + "}",
                    success: function (msg) {
                        // msg = JSON object that controller sendmail returns
                        var message;
                        var Ret = JSON.parse(msg.d);
                        if (Ret.code == 0) {
                            jAlertNotice(Ret.msg);
                        }
                        else {
                            jAlert(Ret.msg);
                        }
                        return;
                        
                    }
                });
                return false;
            }
        })

        function isNumberKey(evt) {
            var charCode = (evt.which) ? evt.which : evt.keyCode;
            if (charCode != 46 && charCode > 31
                && (charCode < 48 || charCode > 57))
                return false;
            return true;
        }

        String.prototype.format = function (args) {
            var str = this;
            return str.replace(String.prototype.format.regex, function (item) {
                var intVal = parseInt(item.substring(1, item.length - 1));
                var replace;
                if (intVal >= 0) {
                    replace = args[intVal];
                } else if (intVal === -1) {
                    replace = "{";
                } else if (intVal === -2) {
                    replace = "}";
                } else {
                    replace = "";
                }
                return replace;
            });
        };
        String.prototype.format.regex = new RegExp("{-?[0-9]+}", "g");

        var numitem = 1;
        var rowid = 1;
        function add_item_fields() {

            if (numitem == 1) {
                $('#inputGoodId1').val(rowData[0]["Id"]);
                $('#inputGoodQuantity1').val(1);
                $('#checkGoodBinding1').prop('checked', false);
                $('#inputGoodForgeLevel1').val(20);
                $('#checkGoodLucky1').prop('checked', true);
                $('#inputGoodExcellenceInfo1').val(235);
                $('#inputGoodAppendPropLev1').val(80);
                numitem++;
                rowid++;;
            }
            else {
                if (numitem <= 5) {

                    var objTo = document.getElementById('itemGoodlist')
                    var divtest = document.createElement("div");
                    divtest.setAttribute("class", "row removeclass" + rowid);
                    //\u00A0 = "non breaking space"
                    //var labeItem = "Item\u00A0{0}: ";
                    //labeItem = labeItem.format(numitem.toString());
                    var labeItem = "Item:";
                    divtest.innerHTML = '<div class="form-group"> <input type="text" class="form-control input-xs smallinput" value=' + labeItem + ' disabled> </div> <div class="form-group"> <input type="text" class="form-control input-xs smallinput" id= ' + "inputGoodId" + numitem + ' value = ' + rowData[0]["Id"] + ' name = "inputGoodId[]"   placeholder = "Id" > </div > <div class="form-group"> <input type="text" class="form-control input-xs smallinput" id= ' + "inputGoodQuantity" + numitem + ' name = "inputGoodQuantity[]" value = "1" placeholder="Quantity"> </div> <div class="form-group"> <input type="checkbox" id= ' + "checkGoodBinding" + numitem + ' + name = "checkGoodBinding[]"> </div> <div class="form-group"> <input type="text" class="form-control input-xs smallinput" id=' + "inputGoodForgeLevel" + numitem + ' + name = "inputGoodForgeLevel[]" placeholder="20"> </div> <div class="form-group"> <input type="checkbox" id=' + "checkGoodLucky" + numitem + ' + name = "checkGoodLucky[]" checked> </div> <div class="form-group"> <input type="text" class="form-control input-xs smallinput" id=' + "inputGoodExcellenceInfo" + numitem + ' + name = "inputGoodExcellenceInfo[]" placeholder="235"> </div> <div class="form-group"> <input type="text" class="form-control input-xs smallinput" id=' + "inputGoodAppendPropLevel" + numitem + ' + name = "inputGoodAppendPropLevel[]" placeholder="80"> </div> <div class="form-group"> <button class="btn btn-danger btn-xs" type="button" onclick="remove_item_fields(' + rowid + ');">Remove</button> </div>';
                    objTo.prepend(divtest);

                    rowid++;
                    numitem++;

                } else {
                    alert("Maximum 5 items!");
                }
            }


        }
        function remove_item_fields(rid) {
            if (numitem > 0) {
                if (rid == 1) {
                    $('#inputGoodId1').val(null);
                    $('#inputGoodQuantity1').val(null);
                    $('#checkGoodBinding1').prop('checked', false);
                    $('#inputGoodForgeLevel1').val(null);
                    $('#checkGoodLucky1').prop('checked', true);
                    $('#inputGoodExcellenceInfo1').val(null);
                    $('#inputGoodAppendPropLev1').val(null);
                    numitem--;
                    if (numitem == 0)
                        numitem++;

                } else {
                    $('.removeclass' + rid).remove();
                    numitem--;
                }

            }

        }


        var table = $('#goodslist').DataTable({
            "ajax": window.location.pathname + "/.." + "/JsonGoods.txt",
            "lengthChange": false,
            "pageLength": 5,
            "bInfo": false,
            "select": true,
            "columns": [
                { "data": "Id" },
                { "data": "Title" },
                {
                    "data": "undefined", // can be null or undefined
                    "defaultContent": '<button class="btn btn-success btn-xs" type="button">Add</button>'
                }

            ]
        });
        var events = $('#events');
        var rowData;
        table
            .on('select', function (e, dt, type, indexes) {
                rowData = table.rows(indexes).data().toArray();
                add_item_fields();
            })
    </script>
</body>
</html>
