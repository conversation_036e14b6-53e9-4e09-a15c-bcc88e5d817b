﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="webshop.aspx.cs" Inherits="Users.Interface.Webshop" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="cache-control" content="max-age=0" /><meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" /><meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <link rel="stylesheet" href="css/style.css" />
    <link href="css/form.css" rel="stylesheet" />    
    <link href="css/jAlert.css" rel="stylesheet" />
    <link href="css/table.css" rel="stylesheet" />
    <link href="css/pagination.css" rel="stylesheet" />
    <link href="js/jquery.mobile-1.4.5.min.css" rel="stylesheet" />
    <script src="js/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="js/jquery.mobile-1.4.5.min.js"></script>
    <script src="js/jAlert.js"></script>
    <script src="js/jAlert-functions.js"></script>
    <title></title>
</head>
<body>
    <form id="form1" runat="server">
        <div class="wrapper">
            <div class="main pr cf">
                <div class="tool_bar">
                    <a href="#" onclick="document.location = 'js-oc:kunlunClose:null';return false" class="close"></a>
                    <a href="./" class="home"></a>
                    <div class="title">Máy chủ:<asp:Label ID="txtServerName" runat="server" Text="Server Name"></asp:Label></div>
                </div>   
                <div class="top_bar">
                    Chào,
                    <asp:Label ID="txtUserName" runat="server" Text="Label"></asp:Label>! Bạn đang có:
                        <asp:Label ID="txtMoney" runat="server" Text="Label"></asp:Label>
                    xu
                     . Vật phẫm sẽ chuyễn vào hộp thư nhân vật:
                    <asp:Label ID="txtRoleName" runat="server" Text="Label"></asp:Label>
                </div>
                <div class="box-data" style="margin-top:5px">
                    <div data-role="navbar" style="margin-left:-3px">
                        <div class="ui-grid-a center">
                            <div class="ui-block-a">
                                <ul>
                                    <li><a href="webshop.aspx?category=1" class="<%=Users.Interface.Webshop.IsActive(1,"ui-btn-active")%>">Vũ khí</a></li>
                                    <li><a href="webshop.aspx?category=2" class="<%=Users.Interface.Webshop.IsActive(2,"ui-btn-active")%>">Trang bị thân</a></li>
                                    <li><a href="webshop.aspx?category=3" class="<%=Users.Interface.Webshop.IsActive(3,"ui-btn-active")%>">Trang sức</a></li>
                                </ul>
                            </div>
                            <div class="ui-block-b">
                                <ul>
                                    <li><a href="webshop.aspx?category=4" class="<%=Users.Interface.Webshop.IsActive(4,"ui-btn-active")%>">Thời trang</a></li>
                                    <li><a href="webshop.aspx?category=5" class="<%=Users.Interface.Webshop.IsActive(5,"ui-btn-active")%>">Pet</a></li>
                                    <li><a href="webshop.aspx?category=6" class="<%=Users.Interface.Webshop.IsActive(6,"ui-btn-active")%>">Đạo cụ</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div id="pricing-table" class="clear" style="margin-top:5px">
                        <% foreach(var cart in Users.Interface.Webshop.carts)
                            {
                                %>

                        <div class="plan">
                            <h4><%=cart.goodname %>
                                <img alt="items" src="<%=Users.Models.Resource.GetGoodsImages(cart.iconcode)%>" />
                            </h4>
                            <ul class="form-style-1">
                                <li class="price">Giá: <b id="price-<%=cart.id %>-text"><%=cart.price %></b>xu</li>
                                <li class="ui-field-contain">
                                    <label for="gcount-<%=cart.id %>">S.lượng:</label>
                                    <input type="text" data-cid="<%=cart.id %>" id="gcount-<%=cart.id %>" value="<%=cart.gcount %>" />
                                    <input type="hidden" value="<%=cart.price %>" id="price-<%=cart.id %>" />
                                </li>
                            </ul>
                            <span class="buynow" data-cid="<%=cart.id %>">Mua Ngay</span>
                            <ul>
                                <li>Trạng thái: <b><%=Users.Models.Resource.BindText(cart.binding)%></b></li>
                                <li>Cấp tr.bị: <b><%=cart.equip_level %></b></li>
                                <li>Cường hóa: <b><%=cart.forge_level %></b></li>
                                <li>Gia công: <b><%=cart.appendproplev %></b></li>
                                <li>Hoàn mỹ: <b><%=cart.excellence_title %></b></li>
                                <li>May mắn: <b><%=Users.Models.Resource.LuckText(cart.lucky) %></b></li>
                            </ul>
                        </div>
                        <% }
                            if(Users.Interface.Webshop.carts.Count == 0)
                            {                           
                            %>

                        <span> Hiện tại hệ thống chưa bán vật phẩm này</span>
                        <%
                             } %>
                    </div>
                    <div class="pagination-container wow zoomIn mar-b-1x" data-wow-duration="0.5s">
                        <!-- is-active -->
                        <ul class="pagination">
                            <li class="pagination-item--wide first"><a class="pagination-link--wide first" href="webshop.aspx?category=<%=Users.Interface.Webshop.m_category %>&page=1">Trang đầu</a> </li>
                            <% for (var i = 1; i <= Users.Interface.Webshop.TotalPages; i++)
                                { %>                                  
                                    <li class="pagination-item"><a class="pagination-link" href="webshop.aspx?category=<%=Users.Interface.Webshop.m_category %>&page=<%=i %>"><%=i %></a> </li>
                            
                            <% } %>
                            <li class="pagination-item--wide last"><a class="pagination-link--wide last" href="webshop.aspx?category=<%=Users.Interface.Webshop.m_category %>&page=<%=Users.Interface.Webshop.TotalPages %>">Trang cuối</a> </li>
                        </ul>

                    </div>
                </div>
            </div>
        </div>
          <script type="text/javascript">
         $(document).ready(function (e) {
             //alert('Vui lòng nhập vào 1 số dương!');
             var isProcessing = false;
             $(".buynow").click(function () {

                 var cartid = $(this).attr('data-cid');
                 var gcount = $('#gcount-' + cartid).val();
                 if (isProcessing)
                     return;

                 isProcessing = true;
                 var obj = {};
                 obj.cartid = cartid;
                 obj.gcount = gcount;
                 var jsonData = JSON.stringify(obj);

                 $.ajax({
                     type: "POST",
                     url: window.location.pathname + "/BuyItemCart",
                     data: jsonData,
                     contentType: "application/json; charset=utf-8",
                     dataType: "json",
                     success: function (msg) {
                         var Ret = JSON.parse(msg.d);
                         isProcessing = false;
                         switch (Ret.status) {
                             case 0:
                                 $("#txtMoney").text(Ret.money);
                                 jAlertNotice(Ret.message);
                                 return;
                             default:
                                 jAlert(Ret.message);
                                 break;
                         }
                         return;
                     },
                     error: function (request, status, error) {
                         document.write(request.responseText);
                     }
                 });
             });

             $(".field-divided").on('change', function () {
                 
                 var cid = $(this).attr('data-cid');
                 var gcount = $('#gcount-' + cid).val();
                 var currentPrice = $('#price-' + cid).val();
                 if ($.isNumeric(gcount) > 0) {
                     $('#price-' + cid + '-text').html(gcount * currentPrice);
                 }
                 else {
                     alert('Vui lòng nhập vào 1 số dương!');
                 }
             });
         });
    </script>
    </form>   
</body>

</html>
