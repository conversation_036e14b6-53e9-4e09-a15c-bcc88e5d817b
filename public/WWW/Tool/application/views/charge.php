<div class="content">
<!-- Animated -->
    <!-- Widgets  -->
<div class="animated fadeIn">
    <div class="row">
        <div class="col-lg-12 col-md-12">
                <div class="card">
                    <div class="card-header">
                        <strong>Nạp <PERSON></strong>
                    </div>
                    <div class="card-body card-block">
                        <?php
                        $success = $this->session->flashdata('success');
                        if($success !== null && $success !== "")
                        {
                        ?>
                        <div class="alert alert-success" role="success">
                          <?php echo $success; ?>
                        </div>
                        <?php } ?>
                        <?php
                        $error = $this->session->flashdata('error');
                        if($error !== null && $error !== "")
                        {
                        ?>
                        <div class="alert alert-danger" role="alert">
                          <?php echo $error; ?>
                        </div>
                        <?php } ?>
                        <form action="<?php echo base_url(); ?>charge_process" method="post" enctype="multipart/form-data" class="form-horizontal">
                            <div class="row form-group">
                                <div class="col col-md-3"><label for="text-input" class=" form-control-label">Tài Khoản</label></div>
                                <div class="col-12 col-md-9">
                                    <input type="text" name="username" autocomplete="off" id="text-input" name="text-input" class="form-control"><div id="suggesstion-box" style="margin-left: 20px; margin-top: 10px; font-size: 18px;"></div></div>
                            </div>
                            <div class="row form-group">
                                <div class="col col-md-3"><label for="select" class=" form-control-label">Số Lượng Kim Cương</label></div>
                                <div class="col-12 col-md-9">
                                    <input type="number" name="quantity" class="form-control">
                                </div>
                            </div>
                            <div class="row form-group">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fa fa-dot-circle-o"></i> Nạp
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Lich su giao dich -->

    <div class="row">
        <div class="col-lg-12 col-md-12">
            <div class="card">
                <div class="card-header">
                    <strong class="card-title">Lịch Sử Giao Dịch</strong>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead class="thead-dark">
                            <tr>
                              <th scope="col">#</th>
                              <th scope="col">Nội Dung</th>
                              <th scope="col">Thời Gian</th>
                          </tr>
                      </thead>
                      <tbody>
                        <?php
                        $i = 1;
                        foreach ($transactions as $transaction)
                        {
                            if($transaction['type'] == 5)
                            {
                            ?>
                            <tr>
                                <th scope="row"><?php echo $i; ?></th>
                                <td>Bạn đã nạp <?php echo $transaction['amount']; ?> vào tài khoản <?php echo $transaction['quantri_username']; ?></td>
                                <td><?php echo $transaction['created_time']; ?></td>
                            </tr>
                            <?php
                            }
                            $i++;
                        }
                        ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

</div>