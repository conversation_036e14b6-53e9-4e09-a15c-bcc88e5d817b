<div class="content">
<!-- Animated -->
    <!-- Widgets  -->
<div class="animated fadeIn">
    <div class="row">
        <div class="col-lg-12 col-md-12">
                <div class="card">
                    <div class="card-header">
                        <strong>Thống <PERSON>ản <PERSON></strong>
                    </div>
                    <div class="card-body card-block">
                        <?php
                        $success = $this->session->flashdata('success');
                        if($success !== null && $success !== "")
                        {
                        ?>
                        <div class="alert alert-success" role="success">
                          <?php echo $success; ?>
                        </div>
                        <?php } ?>
                        <?php
                        $error = $this->session->flashdata('error');
                        if($error !== null && $error !== "")
                        {
                        ?>
                        <div class="alert alert-danger" role="alert">
                          <?php echo $error; ?>
                        </div>
                        <?php } ?>
                        <form action="<?php echo base_url(); ?>report_process" method="post" enctype="multipart/form-data" class="form-horizontal">
                            <div class="row form-group">
                                <div class="col col-md-3"><label for="text-input" class=" form-control-label">Tài Khoản</label></div>
                                <div class="col-12 col-md-9">
                                    <input type="text" name="username" autocomplete="off" id="text-input" name="text-input" class="form-control"><div id="suggesstion-box" style="margin-left: 20px; margin-top: 10px; font-size: 18px;"></div></div>
                            </div>
                            <div class="row form-group">
                                <div class="col col-md-3"><label for="select" class=" form-control-label">Từ Ngày</label></div>
                                <div class="col-12 col-md-9">
                                    <input type="text" name="from_date" class="form-control"/>
                                </div>
                            </div>
                            <div class="row form-group">
                                <div class="col col-md-3"><label for="select" class=" form-control-label">Đến Ngày</label></div>
                                <div class="col-12 col-md-9">
                                    <input type="text" name="to_date" class="form-control">
                                </div>
                            </div>
                            <div class="row form-group">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fa fa-dot-circle-o"></i> Xem
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Lich su giao dich -->

    <div class="row">
        <div class="col-lg-12 col-md-12">
            <div class="card">
                <div class="card-header">
                    <strong class="card-title">Thống kê sản lượng
                        <?php
                        if($today == 1)
                        {
                            echo " ngày hôm nay";
                        }
                        else
                        {
                            echo " từ ".$from_date." đến ".$to_date;
                        }
                        ?></strong>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead class="thead-dark">
                            <tr>
                              <th scope="col">#</th>
                              <th scope="col">Tài Khoản</th>
                              <th scope="col">Số Kim Cương</th>
                              <th scope="col">Số Tiền</th>
                          </tr>
                      </thead>
                      <tbody>
                        <?php
                        $i = 1;
                        foreach ($reports as $report)
                        {
                        ?>
                            <tr>
                                <th scope="row"><?php echo $i; ?></th>
                                <td><?php echo $report['username']; ?></td>
                                <td><?php echo number_format($report['kimcuong'], 0, '', ','); ?></td>
                                <td><?php echo number_format($report['sotien'], 0, '', ','); ?> đồng</td>
                            </tr>
                        <?php
                        $i++;
                        }
                        ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

</div>
<script>
$(function() {
  $('input[name="from_date"]').daterangepicker({
    locale: {
        format: 'YYYY/MM/DD'
    },
    singleDatePicker: true,
    showDropdowns: true,
    minYear: 1901,
    maxYear: parseInt(moment().format('YYYY'),10)
  });
  $('input[name="to_date"]').daterangepicker({
    locale: {
        format: 'YYYY/MM/DD'
    },
    singleDatePicker: true,
    showDropdowns: true,
    minYear: 1901,
    maxYear: parseInt(moment().format('YYYY'),10)
  });
});
</script>