<div class="content">
<!-- Animated -->
    <!-- Widgets  -->
<div class="animated fadeIn">
    <div class="row">
        <div class="col-lg-12 col-md-12">
                <div class="card">
                    <div class="card-header">
                        <strong>Thưởng Rolan</strong>
                    </div>
                    <div class="card-body card-block">
                        <?php
                        $success = $this->session->flashdata('success');
                        if($success !== null && $success !== "")
                        {
                        ?>
                        <div class="alert alert-success" role="success">
                          <?php echo $success; ?>
                        </div>
                        <?php } ?>
                        <?php
                        $error = $this->session->flashdata('error');
                        if($error !== null && $error !== "")
                        {
                        ?>
                        <div class="alert alert-danger" role="alert">
                          <?php echo $error; ?>
                        </div>
                        <?php } ?>
                        <form action="<?php echo base_url(); ?>toprolan_process" method="post" enctype="multipart/form-data" class="form-horizontal">
                            <div class="row form-group">
                            <div class="col col-md-3"><label for="text-input" class=" form-control-label">Server</label></div>
                            <div class="col-12 col-md-9">
                                    <select name="server" id="select" class="form-control">
                                        <option value="0">Chọn Server</option>
                                        <?php foreach ($serverList as $server): ?>
                                        <option value="<?php echo $server['Id']; ?>"><?php echo $server['ServerName']; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="row form-group">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fa fa-dot-circle-o"></i> Gửi
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>