<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class History extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'account';	
	
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_history';
	
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'uid', 'rid', 'zoneid', 'type', 'balance', 'content'
    ];
	
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'content' => 'array',
    ];
	
	/**
     * Get the username record associated with the login.
     */
    public function acc()
    {
        return $this->hasOne('App\Account', 'id', 'uid');
	}		
}
