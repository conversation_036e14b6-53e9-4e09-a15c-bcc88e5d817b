<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Lottery extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'account';	
	
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_lottery';
		
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'uid', 'type', 'code', 'amount', 'status', 'reward', 'balance'
    ];
	
    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */	
    protected $casts = [
		'code' => 'array',
    ];	
}
