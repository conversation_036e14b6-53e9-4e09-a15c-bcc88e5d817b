<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class InputLog extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_inputlog';
		
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'amount', 'u', 'rid', 'order_no', 'cporder_no', 'time', 'sign', 'inputtime', 'result', 'zoneid', 'itemid', 'chargetime',
    ];
	
    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'inputtime', 'chargetime',
    ];
	
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;		
}
