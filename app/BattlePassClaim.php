<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BattlePassClaim extends Model
{
    use HasFactory;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'account';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'battle_pass_claims';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'season_id',
        'level',
        'reward_type',
        'reward_id',
        'reward_amount',
        'is_premium'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'is_premium' => 'boolean',
        'claimed_at' => 'datetime'
    ];

    /**
     * Get the user that owns this claim.
     */
    public function user()
    {
        return $this->belongsTo(Account::class, 'user_id', 'UserID');
    }

    /**
     * Get the season for this claim.
     */
    public function season()
    {
        return $this->belongsTo(BattlePassSeason::class, 'season_id');
    }

    /**
     * Get reward display name.
     */
    public function getDisplayName()
    {
        switch ($this->reward_type) {
            case 'coin':
                return number_format($this->reward_amount) . ' Coins';
            case 'item':
                return $this->getItemName() . ' x' . $this->reward_amount;
            case 'exp':
                return number_format($this->reward_amount) . ' EXP';
            default:
                return 'Unknown Reward';
        }
    }

    /**
     * Get item name by ID.
     */
    private function getItemName()
    {
        $items = [
            1001 => 'Health Potion',
            1002 => 'Mana Potion',
            1003 => 'Strength Scroll',
            1004 => 'Defense Scroll',
            1005 => 'Speed Scroll',
            2001 => 'Premium Health Potion',
            2002 => 'Premium Mana Potion',
            2003 => 'Premium Strength Scroll',
            2004 => 'Premium Defense Scroll',
            2005 => 'Premium Speed Scroll',
            2010 => 'Legendary Weapon',
            2020 => 'Ultimate Armor'
        ];

        return $items[$this->reward_id] ?? 'Item #' . $this->reward_id;
    }
}
