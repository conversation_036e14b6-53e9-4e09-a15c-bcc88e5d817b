<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class WC_Pool_Log extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'account';	
	
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_wc_pool_log';
	protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id_pool', 'userId','amount', 'pool', 'status','amount_send',
    ];
	
    
}
