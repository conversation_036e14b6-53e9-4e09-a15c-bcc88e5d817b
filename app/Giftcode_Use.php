<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Giftcode_Use extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'account';	
	
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_gift_code_use';
		
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['content'];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'id'
    ];
	
    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */	
   
}
