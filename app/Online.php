<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Online extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_onlinenum';
	
    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'rectime'
    ];	
	
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
}
