<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Giftcode extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'account';	
	
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_giftcode';
		
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'type','accounts', 'multiple', 'code', 'items', 'content', 'limit', 'period', 'zoneid'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'id'
    ];
	
    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */	
    protected $casts = [
		'code' => 'array',
        'items' => 'array',
    ];
}
