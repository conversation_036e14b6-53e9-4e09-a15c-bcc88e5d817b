<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BattlePassReward extends Model
{
    use HasFactory;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'account';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'battle_pass_rewards';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'season_id',
        'level',
        'reward_type',
        'reward_id',
        'reward_amount',
        'is_premium'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'is_premium' => 'boolean'
    ];

    /**
     * Get the season that owns this reward.
     */
    public function season()
    {
        return $this->belongsTo(BattlePassSeason::class, 'season_id');
    }

    /**
     * Get reward display name.
     */
    public function getDisplayName()
    {
        switch ($this->reward_type) {
            case 'coin':
                return number_format($this->reward_amount) . ' Coins';
            case 'item':
                return $this->getItemName() . ' x' . $this->reward_amount;
            case 'exp':
                return number_format($this->reward_amount) . ' EXP';
            default:
                return 'Unknown Reward';
        }
    }

    /**
     * Get item name by ID (you can customize this based on your item system).
     */
    private function getItemName()
    {
        $items = [
            1001 => 'Health Potion',
            1002 => 'Mana Potion',
            1003 => 'Strength Scroll',
            1004 => 'Defense Scroll',
            1005 => 'Speed Scroll',
            2001 => 'Premium Health Potion',
            2002 => 'Premium Mana Potion',
            2003 => 'Premium Strength Scroll',
            2004 => 'Premium Defense Scroll',
            2005 => 'Premium Speed Scroll',
            2010 => 'Legendary Weapon',
            2020 => 'Ultimate Armor'
        ];

        return $items[$this->reward_id] ?? 'Item #' . $this->reward_id;
    }

    /**
     * Get reward icon/image.
     */
    public function getIcon()
    {
        switch ($this->reward_type) {
            case 'coin':
                return 'fa-coins';
            case 'item':
                return 'fa-gift';
            case 'exp':
                return 'fa-star';
            default:
                return 'fa-question';
        }
    }

    /**
     * Check if reward is premium.
     */
    public function isPremium()
    {
        return $this->is_premium;
    }
}
