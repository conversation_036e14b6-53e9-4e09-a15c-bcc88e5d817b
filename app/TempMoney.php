<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class TempMoney extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_tempmoney';
		
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'cc', 'uid', 'rid', 'addmoney', 'itemid', 'chargetime'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'chargetime'
    ];
	
    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'chargetime',
    ];		
	
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;		
}
