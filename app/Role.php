<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_roles';

    protected $primaryKey = 'rid';

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'regtime',
        'lasttime',
        'deltime',
        'logofftime'
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * Get the username record associated with the role.
     */
    public function acc()
    {
        return $this->hasOne('App\Account', 'userid', 'userid');
    }

    /**
     * Get the balance record associated with the role.
     */
    public function balance()
    {
        return $this->hasOne('App\Money', 'userid', 'userid');
    }

    /**
     * Get the server record associated with the role.
     */
    public function server()
    {
        return $this->hasOne('App\Server', 'Id', 'zoneid');
    }
}
