<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class t_ranks_list extends Model
{
    //
     /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'account';	
	
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_ranks_lists';
		
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
		'name',
		'limit',
		'zoneid',
        'lifecount',
        'lifecount_limit'
    ];
    protected $primaryKey = 'id';

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        
    ];

}
