<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Recharge extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'account';	
	
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_recharge';
		
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'uid', 'gateway', 'merchant_id', 'type', 'serial', 'code', 'ip', 'zoneid', 'status', 'amount', 'transaction_code'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'serial', 'code', 'ip'
    ];
	
	/**
     * Get the username record associated with the role.
     */
    public function acc()
    {
        return $this->hasOne('App\Account', 'id', 'uid');
	}	
	
	public function user() {
    	return $this->belongsTo(Account::class, 'uid', 'UserID');
    }
}
