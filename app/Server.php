<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Server extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'account';	
	
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'zt_serverdata';
		
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'Id', 'ServerName', 'ServerURL', 'DatabaseName', 'ServerPort', 'Status', 'OnlineNum', 'StartTime', 'MaintainStarTime', 'MaintainTerminalTime', 'MaintainTxt',
    ];
	
	protected $primaryKey = 'Id';
	
    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'StartTime', 'MaintainStarTime', 'MaintainTerminalTime'
    ];	

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
}
