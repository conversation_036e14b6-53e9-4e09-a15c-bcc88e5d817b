<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserBattlePass extends Model
{
    use HasFactory;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'account';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_battle_pass';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'season_id',
        'current_level',
        'current_exp',
        'is_premium',
        'purchased_at'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'is_premium' => 'boolean',
        'purchased_at' => 'datetime'
    ];

    /**
     * Get the user that owns this battle pass.
     */
    public function user()
    {
        return $this->belongsTo(Account::class, 'user_id', 'UserID');
    }

    /**
     * Get the season for this battle pass.
     */
    public function season()
    {
        return $this->belongsTo(BattlePassSeason::class, 'season_id');
    }

    /**
     * Get claimed rewards for this user and season.
     */
    public function claimedRewards()
    {
        return $this->hasMany(BattlePassClaim::class, 'user_id', 'user_id')
                    ->where('season_id', $this->season_id);
    }

    /**
     * Add experience points.
     */
    public function addExp($exp)
    {
        $this->current_exp += $exp;
        
        // Check for level up (assuming 1000 exp per level)
        $expPerLevel = 1000;
        $newLevel = floor($this->current_exp / $expPerLevel) + 1;
        
        if ($newLevel > $this->current_level && $newLevel <= $this->season->max_level) {
            $this->current_level = $newLevel;
        }
        
        $this->save();
        
        return $this;
    }

    /**
     * Purchase premium battle pass.
     */
    public function purchasePremium()
    {
        if (!$this->is_premium) {
            $this->is_premium = true;
            $this->purchased_at = now();
            $this->save();
        }
        
        return $this;
    }

    /**
     * Get available rewards to claim.
     */
    public function getAvailableRewards()
    {
        $claimedRewardIds = $this->claimedRewards()->pluck('level')->toArray();
        
        $query = $this->season->rewards()
                     ->where('level', '<=', $this->current_level)
                     ->whereNotIn('level', $claimedRewardIds);
        
        if (!$this->is_premium) {
            $query->where('is_premium', false);
        }
        
        return $query->get();
    }

    /**
     * Claim reward.
     */
    public function claimReward($rewardId)
    {
        $reward = BattlePassReward::find($rewardId);
        
        if (!$reward || $reward->season_id != $this->season_id) {
            return false;
        }
        
        if ($reward->level > $this->current_level) {
            return false;
        }
        
        if ($reward->is_premium && !$this->is_premium) {
            return false;
        }
        
        // Check if already claimed
        $alreadyClaimed = BattlePassClaim::where([
            'user_id' => $this->user_id,
            'season_id' => $this->season_id,
            'level' => $reward->level,
            'is_premium' => $reward->is_premium
        ])->exists();
        
        if ($alreadyClaimed) {
            return false;
        }
        
        // Create claim record
        BattlePassClaim::create([
            'user_id' => $this->user_id,
            'season_id' => $this->season_id,
            'level' => $reward->level,
            'reward_type' => $reward->reward_type,
            'reward_id' => $reward->reward_id,
            'reward_amount' => $reward->reward_amount,
            'is_premium' => $reward->is_premium
        ]);
        
        // Give reward to user
        $this->giveRewardToUser($reward);
        
        return true;
    }

    /**
     * Give reward to user (implement based on your game system).
     */
    private function giveRewardToUser($reward)
    {
        switch ($reward->reward_type) {
            case 'coin':
                $user = $this->user;
                $user->Money += $reward->reward_amount;
                $user->save();
                break;
            case 'item':
                // Implement item giving logic here
                // This would typically involve adding items to user inventory
                break;
            case 'exp':
                // Implement experience giving logic here
                break;
        }
    }

    /**
     * Get progress percentage.
     */
    public function getProgressPercentage()
    {
        $expPerLevel = 1000;
        $currentLevelExp = $this->current_exp % $expPerLevel;
        return ($currentLevelExp / $expPerLevel) * 100;
    }

    /**
     * Get experience needed for next level.
     */
    public function getExpForNextLevel()
    {
        $expPerLevel = 1000;
        $currentLevelExp = $this->current_exp % $expPerLevel;
        return $expPerLevel - $currentLevelExp;
    }
}
