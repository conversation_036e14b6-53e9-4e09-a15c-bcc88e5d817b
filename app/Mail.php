<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Mail extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_mail';
	
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'senderrid', 'senderrname', 'sendtime', 'receiverrid', 'reveiverrname', 'subject', 'content'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'senderrid', 'senderrname'
    ];

	protected $primaryKey = 'mailid';
	
    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'sendtime',
    ];	
	
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;	
}
