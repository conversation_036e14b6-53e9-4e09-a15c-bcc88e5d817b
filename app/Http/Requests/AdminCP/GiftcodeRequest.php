<?php

namespace App\Http\Requests\AdminCP;

use Illuminate\Foundation\Http\FormRequest;

class GiftcodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
    	return [
			'type' => 'required|numeric',
			'code' => 'nullable|alpha_num|between:4,20',
        	'items' => 'nullable',
			'content' => 'required',
			'limit' => 'numeric|max:999',
			'period' => 'numeric|max:365',
			'zoneid' => 'numeric',
    	];
    }
}
