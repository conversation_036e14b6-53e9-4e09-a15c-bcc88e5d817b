<?php

namespace App\Http\Requests\AdminCP;

use Illuminate\Foundation\Http\FormRequest;

class RewardCustomRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
    	return [
			'id' => 'nullable|numeric',
            'zoneid'=>'required',
			'name' => 'required',	
			'date_start' => 'nullable|date_format:Y-m-d H:i:s',
			'date_end' => 'nullable|date_format:Y-m-d H:i:s',
			'txt' => 'nullable'
    	];
    }
}
