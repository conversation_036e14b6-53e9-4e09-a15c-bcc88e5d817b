<?php

namespace App\Http\Requests\AdminCP;

use Illuminate\Foundation\Http\FormRequest;

class AccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
    	return [
			'coin' => 'nullable|numeric',
			'reason' => 'required_with:coin|max:255',
			'password' => 'nullable|alpha_num|between:4,20',
        	'phone' => 'nullable|unique:account.zt_account|digits_between:10,12',
        	'email' => 'nullable|email|unique:account.zt_account|max:255',
			'code_secret' => 'nullable|digits_between:4,20',
    	];
    }
}
