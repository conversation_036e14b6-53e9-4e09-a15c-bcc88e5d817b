<?php

namespace App\Http\Requests\AdminCP;

use Illuminate\Foundation\Http\FormRequest;

class ServerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
    	return [
			'id' => 'nullable|numeric',
			'name' => 'required|alpha_dash|between:1,20',
        	'url' => 'required',
			'database' => 'required',
			'port' => 'required|numeric',
			'status' => 'required|numeric',
			'online' => 'required|numeric|max:999',
			'startime' => 'nullable|date_format:Y-m-d H:i:s',
			'terminaltime' => 'nullable|date_format:Y-m-d H:i:s',
			'txt' => 'nullable'
    	];
    }
}
