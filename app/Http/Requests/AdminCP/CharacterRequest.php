<?php

namespace App\Http\Requests\AdminCP;

use Illuminate\Foundation\Http\FormRequest;

class CharacterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
    	return [
			'zoneid' => 'required',
			'reason' => 'required_with:ban,unban|max:255',		
        	'code' => 'required_with:add_item',
			'content' => 'required_with:code',
    	];
    }
}
