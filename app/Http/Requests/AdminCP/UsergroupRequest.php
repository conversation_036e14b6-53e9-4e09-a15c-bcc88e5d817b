<?php

namespace App\Http\Requests\AdminCP;

use Illuminate\Foundation\Http\FormRequest;

class UsergroupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
    	return [
			'username' => 'required|alpha_num|exists:account.zt_account|between:4,20',
			'group' => 'required|in:0,1,2,3,9',
    	];
    }
}
