<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ExchangeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
    	return [
			'amount' => 'required|in:1000,2000,5000,10000,20000,50000,100000,200000,500000,1000000,2000000,5000000,10000000',
			'ex_rid' => 'nullable|integer',
    	];
    }
}
