<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
    	return [
			'old_password' => 'nullable|alpha_num|between:4,20',
			'secret' => 'nullable|digits_between:4,20',
        	'password' => 'required|alpha_num|between:4,20|confirmed',
    	];
    }
}
