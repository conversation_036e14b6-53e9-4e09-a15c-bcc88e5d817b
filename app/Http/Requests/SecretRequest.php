<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SecretRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
    	return [
        	'secret' => 'required|digits_between:4,20|confirmed',
    	];
    }
}
