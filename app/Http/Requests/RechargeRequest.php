<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RechargeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
    	return [
			'type' => 'required|numeric|between:1,20',
			'amount' => 'required|numeric|between:10000,5000000',
        	'serial' => 'required|alpha_num|between:8,20',
			'code' => 'required|alpha_num|between:8,20',
    	];
    }
}
