<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
    	return [
        	'phone' => 'required|unique:account.zt_account|digits_between:10,12',
        	'email' => 'required|email|unique:account.zt_account|max:255',
			'secret' => 'nullable|digits_between:4,20',
    	];
    }
}
