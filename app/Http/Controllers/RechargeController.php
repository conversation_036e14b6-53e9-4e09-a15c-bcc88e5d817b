<?php

namespace App\Http\Controllers\UserCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Http\Requests\RechargeRequest;

use App\Recharge;
use App\Helper;
use Config;
use Auth;

use Carbon\Carbon;
use App\NL_CheckOutV3 as NLATM;
use Paymentwall_Config;
use Paymentwall_Widget;
use MP;

use App\Services\PayPalService as PayPalSvc;


class RechargeController extends Controller
{	
	private $paypalSvc;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->paypalSvc = new PayPalSvc();
    }

    public function automobi_post(Request $request){

    	$dt = Carbon::now();

    		$ip = $request->ip();
    		
    		$id = Config('mobiAutoPay.id');
    		$vippay = Config::get('recharge.vippay');
			$merchant_id = $vippay[$id]['merchant_id'];
			$api_user = $vippay[$id]['user'];
			$api_password = $vippay[$id]['password'];
    	$transaction_id = rand(1000,9999).$request->code.time();

    	$check_card = Helper::vippay_api_new(
    		$request->serial,
    		$request->code,
    		$request->amount,
    		$transaction_id,
    		$request->type
    	);

    	$account = Auth::user();

    	if($check_card->code==0){

    		
    		
			$balance = Config('mobiAutoPay.discount')*$check_card->info_card;
			$bonus = 0;
			$promotion_date = Config::get('mu.recharge.date');
			$promotion = Config('mobiAutoPay.promotion');
			$bonus_cf = Config('mobiAutoPay.bonus');

			if($promotion > 0 && $dt >= $promotion_date[0] && $dt <= $promotion_date[1]){
				$bonus = $check_card->info_card * $promotion;
			}
			if(count($bonus_cf)){
				if(isset($bonus_cf[$check_card->info_card])){
					$bonus =$bonus+$bonus_cf[$check_card->info_card];
				}	
			}

    		
    		$recharge = Recharge::create([
						'uid' => $account->id,
						'gateway' => 2,
						'merchant' => $merchant_id,
						'type' => $request->type,
						'serial' => $request->serial,
						'code' => $request->code,
						'zoneid' => $account->zoneid,
						'ip' => $ip,
						'status' => 1,
						'amount' =>$balance,
						'transaction_id'=>$transaction_id
					]);

    		$account->balance = $account->balance + $balance+$bonus;
			$account->save();
			Helper::mobiCarDaily($account->id,$check_card->info_card,$request->code); 
			return redirect()->route('recharge.show', [$request->code])->with('status', __('usercp.message_recharge_success'));

    	}elseif($check_card->code==17 || $check_card->code==1 ){

    		$recharge = Recharge::create([
						'uid' => $account->id,
						'gateway' => 2,
						'merchant' => $merchant_id,
						'type' => $request->type,
						'serial' => $request->serial,
						'code' => $request->code,
						'zoneid' => $account->zoneid,
						'ip' => $ip,
						'status' => 0,
						'amount' =>$check_card->info_card,
						'transaction_id'=>$transaction_id
					]);
    		return redirect()->route('recharge.show', [$request->code])->with('status', __('usercp.message_recharge_pending'));
    	}

    	else{


    		return redirect()->back()->with('message',$check_card->msg)->with('status',2);
    	}
    }	
    public function automobi(){
    	
    	return view('usercp.automobi.index');
    }


    /**
     *
     * PayPal
     *
    */
     public function PayPal_index(Request $request){

     	if($request->amount==-1){
    		return redirect()->back()
                   	->withInput($request->only('amount'))
                  	->withErrors(['amount' => __('usercp.vtcpay.notAmount')]);
    	}
    	
     	$account = Auth::user();
     	

     	$data = [
            [
                'name' => $account->username ,'pay '.$request->amount,
                'quantity' => 1,
                'price' => $request->amount,
                'sku' => 'sku'.$account->username.'|'.$request->amount
            ],
        ];
        $transactionDescription = "Tobaco";
        $paypalCheckoutUrl = $this->paypalSvc
        ->setReturnUrl(route('paypal_succses'))
        ->setCancelUrl(route('paypal_error'))
        ->setItem($data)
        ->setUserIdPay($account->username.'-'.$account->id)
        ->createPayment($transactionDescription);
        if ($paypalCheckoutUrl) {
            return redirect($paypalCheckoutUrl);
        } else {
            dd(['Error']);
        }

     	//return view('usercp.paypal');
     }

    public function PayPal_succses(Request $request){
    	

    	$ip = $request->ip();
    	$account = Auth::user();
    	$paymentStatus = $this->paypalSvc->getPaymentStatus($account->username.'-'.$account->id);
    	$id = $paymentStatus->id;

    	$checked = Recharge::where('code',$id)->first();
    	if($checked){
    		return redirect()->route('recharge.index')->with('message',__('usercp.paypal.failed2'))->with('status',2);
    	}

    	if($paymentStatus->state=='approved'){

        	$transaction = $paymentStatus->transactions[0];
        	$amount = $transaction->amount;
        	$total = $amount->total;
        	$currency=$amount->currency;
        	$recharge = Recharge::create([
						'uid' => $account->id,
						'gateway' => 15,
						'merchant' =>00000,
						'type' => 13,
						'serial' => 'xxxxx',
						'code' =>$id,
						'zoneid' => $account->zoneid,
						'ip' => $ip,
						'status' => 1,
						'amount' =>$total,
					]);
        	$rate  = Config('recharge.paypal.rate');
        	$balance =$total*$rate;
			$account->balance = $account->balance + $balance;
			$account->save();
			return redirect()->route('recharge.index')->with('message',__('usercp.paypal.success',[
				'amount '=>$total,
				'Currency'=>$currency,
				'value'=>$balance ,
				'name'=>Config('mu.currency.name'),

			]))->with('status',1);
    	}
     
    }
    public function PayPal_error(){
    	return redirect()->route('recharge.index')->with('message',__('usercp.paypal.failed'))->with('status',2);
    }
		
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
		$gateway = Config::get('recharge.gateway');	
		
		$paymentwall = NULL;
		$mercadopago = NULL;
		
		if($gateway == 5) {
					
			$auth = Auth::user();
			
			$gateway_info = Helper::getGatewayInfo($gateway);
			
			$id = Helper::getRandomMerchant($gateway_info);
			
			$paymentwall = Config::get('recharge.paymentwall');
			
			$public_key = $paymentwall[$id]['public_key'];
			$private_key = $paymentwall[$id]['private_key'];			
			
			Paymentwall_Config::getInstance()->set([
				'api_type' => Paymentwall_Config::API_VC,
				'public_key' => $public_key,
				'private_key' => $private_key
			]);
			
			$widget = new Paymentwall_Widget(
				$auth->userid, // id of the end-user who's making the payment
				'p1_1',      // widget code, e.g. p1; can be picked inside of your merchant account
				[],     // array of products - leave blank for Virtual Currency API
				[
					'email' => $auth->email,
					'merchant_id' => $id,
					'zoneid' => $auth->zoneid,
					'country_code' => 'VN'
				] // additional parameters
			);
			
			$paymentwall = $widget->getHtmlCode();

			
			
		}
		
		if($gateway == 11) {
					
			$auth = Auth::user();
			
			$gateway_info = Helper::getGatewayInfo($gateway);
			
			$id = Helper::getRandomMerchant($gateway_info);
			
			$mercadopago = Config::get('recharge.mercadopago');
			
			$client_id = $mercadopago[$id]['client_id'];
			$client_secret = $mercadopago[$id]['client_secret'];			
			
			$mp = new MP ($client_id, $client_secret);
			
			$preference_data = [
				"items" => [
					[
						"title" => "Test",
						"quantity" => 1,
						"currency_id" => "USD",
						"unit_price" => 10.4
					]
				]
			];

			$mercadopago = $mp->create_preference($preference_data);			
			
		}		
		
		$exchange = Config::get('mu.recharge.exchange');
		$promotion = Config::get('mu.recharge.promotion');
		$promotion_date = Config::get('mu.recharge.date');
		
		if($gateway == 4 || $gateway == 5) {
			$rate = $exchange;
		} else {
			$rate = 10000 * $exchange;
		}
		
		$dt = Carbon::now();
		
		$promotion_time = false;
		if($promotion > 0 && $dt >= $promotion_date[0] && $dt <= $promotion_date[1]) {
			$promotion_time = true;
		}		
		
		// Get History payment
		$account = Auth::user();
		$Payment_history = Recharge::where('uid', $account->id)->orderBy('id', 'desc')->paginate(10);
        return view('usercp.recharge', ['paymentwall' => $paymentwall,
										'mercadopago' => $mercadopago,
										'rate' => $rate,
										'promotion_time' => $promotion_time,
										'promotion_from' => Helper::getFormatDate($promotion_date[0]),
										'promotion_to' => Helper::getFormatDate($promotion_date[1]),
										'payment_history' => $Payment_history,
										]);
    }
    public function AtmPay(){

    	return view('usercp.vtcpay');
    }
    public function atmPayOk(Request $request){

    	$dt = Carbon::now();
    	$ip = $request->ip();
    	$nganluong = Config::get('recharge.nganluong');
		$merchant_id = $nganluong[1]['merchant_id'];
		$api_user = $nganluong[1]['user'];
		$api_password = $nganluong[1]['password'];
		define('URL_API','https://www.nganluong.vn/checkout.api.nganluong.post.php'); // Đường dẫn gọi api
		define('RECEIVER',$api_user); // Email tài khoản ngân lượng
		define('MERCHANT_ID', $merchant_id); // Mã merchant kết nối
		define('MERCHANT_PASS', $api_password); // Mật khẩu kết nôi

		$nlcheckout= new NLATM(MERCHANT_ID,MERCHANT_PASS,RECEIVER,URL_API);
		$nl_result = $nlcheckout->GetTransactionDetail($request->query('token'));

		if($nl_result){
			$nl_errorcode           = (string)$nl_result->error_code;
			$nl_transaction_status  = (string)$nl_result->transaction_status;
			if($nl_errorcode == '00') {
				if($nl_transaction_status == '00') {

					$nganluong = Config::get('recharge.nganluong');
					$atm_tyle= $nganluong[1]['atm'];

					$check_exist = Recharge::where('code',$nl_result->order_code)->count();
					//where('type',12)
					//->where('code',$nl_result->order_code)
					//->where('status', '<', 2)
					if($check_exist > 0 ) {
					 	return redirect()->route('atmpay')->with('message',__('usercp.atm.message_card_exist'))->with('status',2);
						}

					$bonus = 0;
					$promotion_date = Config::get('mu.recharge.date');
					$promotion = Config('vtcpay.promotion');

					if($promotion > 0 && $dt >= $promotion_date[0] && $dt <= $promotion_date[1]){
						$bonus = $nl_result->total_amount  * $promotion;
					}

					$amount =$nl_result->total_amount*$atm_tyle;
					$account = Auth::user();
					$account->balance = $account->balance + $amount + $bonus;
					$account->save();

					Helper::mobiCarDaily($account->id,$nl_result->total_amount,$nl_result->order_code); 
					


					$log_rate = Config('vtcpay.log_rate');

					$recharge = Recharge::create([
						'uid' => $account->id,
						'gateway' => 6,
						'merchant' => $merchant_id,
						'type' => 12,
						'serial' => $nl_result->transaction_id,
						'code' => $nl_result->order_code,
						'zoneid' => $account->zoneid,
						'ip' => $ip,
						'status' => 1,
						'amount' => $nl_result->total_amount*$log_rate,
					]);
				return redirect()->route('atmpay')->with('message',__('usercp.atm.message_success',['amount'=>number_format((int)$nl_result->total_amount).' vnđ','vcoin'=> number_format((int)$amount).' '.config('mu.currency.name')]))->with('status',1);	
			}
		}else{
			
			return redirect()->route('atmpay')->with('message',$nlcheckout->GetErrorMessage($nl_errorcode) )->with('status',2);
		}

		}


    }
    public function cancelPay(Request $request){

    	return redirect()->route('atmpay')->with('message',__('usercp.atm.message_cancel'))->with('status',2);
    }
    public function checkAtmPay(Request $request){

    	if($request->amount==-1){
    		return redirect()->back()
                   	->withInput($request->only('amount'))
                  	->withErrors(['amount' => __('usercp.vtcpay.notAmount')]);
    	}
    	elseif(!isset($request->option_payment)){

    		return redirect()->back()
                   	->withInput($request->only('option_payment'))
                  	->withErrors(['option_payment' => __('usercp.atm.type')]);
                  	
    	}elseif(in_array($request->option_payment,['ATM_ONLINE','IB_ONLINE','VISA','VISA'])) {

    		if(!isset($request->bankcode)){
    			return redirect()->back()->with('message',__('usercp.atm.bank'))->with('status',2);
    		}

    	}
    	

    		$param =  $request->all();
			$account = Auth::user();
			$nganluong = Config::get('recharge.nganluong');
			$merchant_id = $nganluong[1]['merchant_id'];
			$api_user = $nganluong[1]['user'];
			$api_password = $nganluong[1]['password'];

			define('URL_API','https://www.nganluong.vn/checkout.api.nganluong.post.php'); // Đường dẫn gọi api
			define('RECEIVER',$api_user); // Email tài khoản ngân lượng
			define('MERCHANT_ID', $merchant_id); // Mã merchant kết nối
			define('MERCHANT_PASS', $api_password); // Mật khẩu kết nôi

			$nlcheckout= new NLATM(MERCHANT_ID,MERCHANT_PASS,RECEIVER,URL_API);
			$total_amount=$request->amount;
			$array_items[0]= array('item_name1' => 'Product name',
					 'item_quantity1' => 1,
					 'item_amount1' => $total_amount,
					 'item_url1' => 'http://nganluong.vn/'); 
			$array_items=array();				 
	 		$payment_method =$request->option_payment;
	 		$bank_code = $request->bankcode;
	 		$order_code ="macode_".time()."_".$account->userid;
	
	 		$payment_type ='';
	 		$discount_amount =0;
	 		$order_description='';
	 		$tax_amount=0;
	 		$fee_shipping=0;
	 		$return_url = route('atmPayOk');
	 		$cancel_url =urlencode( route('cancelPay').'?orderid='.$order_code) ;
	 		$buyer_fullname =$account->username;
	 		$buyer_email =$account->email?$account->email:'<EMAIL>';
	 		$buyer_mobile =$account->phone?$account->phone:'**********';
	 		$buyer_address ='';
	 
	
	
	if($payment_method !='' && $buyer_email !="" && $buyer_mobile !="" && $buyer_fullname !="" && filter_var( $buyer_email, FILTER_VALIDATE_EMAIL )  ){

		if($payment_method =="VISA"){
			$nl_result= $nlcheckout->VisaCheckout($order_code,$total_amount,$payment_type,$order_description,$tax_amount,
											  $fee_shipping,$discount_amount,$return_url,$cancel_url,$buyer_fullname,$buyer_email,$buyer_mobile, 
									          $buyer_address,$array_items,$bank_code);
											  
		}elseif($payment_method =="NL"){
			$nl_result= $nlcheckout->NLCheckout($order_code,$total_amount,$payment_type,$order_description,$tax_amount,
												$fee_shipping,$discount_amount,$return_url,$cancel_url,$buyer_fullname,$buyer_email,$buyer_mobile, 
												$buyer_address,$array_items);
												
		}elseif($payment_method =="ATM_ONLINE" && $bank_code !='' ){
			$nl_result= $nlcheckout->BankCheckout($order_code,$total_amount,$bank_code,$payment_type,$order_description,$tax_amount,
												  $fee_shipping,$discount_amount,$return_url,$cancel_url,$buyer_fullname,$buyer_email,$buyer_mobile, 
												  $buyer_address,$array_items) ;
		}
		elseif($payment_method =="NH_OFFLINE"){
				$nl_result= $nlcheckout->officeBankCheckout($order_code, $total_amount, $bank_code, $payment_type, $order_description, $tax_amount, $fee_shipping, $discount_amount, $return_url, $cancel_url, $buyer_fullname, $buyer_email, $buyer_mobile, $buyer_address, $array_items);
			}
		elseif($payment_method =="ATM_OFFLINE"){
				$nl_result= $nlcheckout->BankOfflineCheckout($order_code, $total_amount, $bank_code, $payment_type, $order_description, $tax_amount, $fee_shipping, $discount_amount, $return_url, $cancel_url, $buyer_fullname, $buyer_email, $buyer_mobile, $buyer_address, $array_items);
				
			}
		elseif($payment_method =="IB_ONLINE"){
				$nl_result= $nlcheckout->IBCheckout($order_code, $total_amount, $bank_code, $payment_type, $order_description, $tax_amount, $fee_shipping, $discount_amount, $return_url, $cancel_url, $buyer_fullname, $buyer_email, $buyer_mobile, $buyer_address, $array_items);
			}
		elseif ($payment_method == "CREDIT_CARD_PREPAID") {

			$nl_result = $nlcheckout->PrepaidVisaCheckout($order_code, $total_amount, $payment_type, $order_description, $tax_amount, $fee_shipping, $discount_amount, $return_url, $cancel_url, $buyer_fullname, $buyer_email, $buyer_mobile, $buyer_address, $array_items, $bank_code);
		}
		//var_dump($nl_result); die;
		if ($nl_result->error_code =='00'){		
			return redirect($nl_result->checkout_url);
		}else{
			return redirect()->back()->with('message',$nl_result->error_message)->with('status',2);
		}
			
		}
    }
    public function atm_info_page(){

    	return view('usercp.atmlinfo');
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(RechargeRequest $request)
    {	
		
		$type = $request->type;
		$serial = $request->serial;
		$code = $request->code;
        $ip = $request->ip();
		
		
		$dt = Carbon::now();
		
		$account = Auth::user();
		
		$check_exist = Recharge::where('type', $type)->where('code', $code)->where('status', '<', 2)->exists();
		
		if($check_exist !== FALSE) {
			return redirect()->back()
                    	->withInput($request->only('code'))
                    	->withErrors(['code' => __('usercp.message_card_exist')]);
		} else {

			$gateway = Config::get('recharge.gateway');

			$mode = Config::get('recharge.mode');				
		
			$multiple = Config::get('recharge.multiple');	
		
			if($multiple != 0) {
				
				foreach ($multiple as $time => $multi) {
					
					if ($time > $dt->hour) {
						$gateway = array_rand($multi);
						
						$gateway = $multi[$gateway];
						
						break;
					}
				}
				
			}
			
			$gateway_info = Helper::getGatewayInfo($gateway);
			
			$id = Helper::getRandomMerchant($gateway_info);
			
			if($id === 0) {
				return redirect()->back()
                    	->withInput($request->only('code'))
                    	->withErrors(['code' => __('usercp.message_recharge_maintenance')]);				
			}
			
			$merchant_id = $gateway_info[$id]['merchant_id'];
			
			if($mode == 1) {
			
				if($gateway == 1) {
					$info = Helper::baokim_api($type, $serial, $code, $id);
					
					$status = $info->getStatusCode();
					$data = $info->getBody();
					$data = json_decode($data);
					
					$amount = isset($data->amount) ? $data->amount : 0;
					$message = isset($data->errorMessage) ? $data->errorMessage : '';
				}
				elseif($gateway == 2) {
					$info = Helper::vippay_api($type, $serial, $code, $id);
					
					$status = $info->code;
					$amount = isset($info->info_card) ? $info->info_card : 0;
					$message = isset($info->msg) ? $info->msg : '';			
				}
				elseif($gateway == 3) {
					$info = Helper::hanoipay_api($type, $serial, $code, $id);
					
					$status = $info->Status;
					$amount = isset($info->Amount) ? $info->Amount : 0;
					$message = isset($info->Description) ? $info->Description : '';			
				}
				elseif($gateway == 4) {
					$info = Helper::tmpay_api($code, $id);
					if(strpos($info, 'SUCCEED') !== FALSE) {
						return redirect()->route('recharge.show', [$code])->with('status', __('usercp.message_recharge_wait'));
					} else {
						$status = 2;
					}
				}
				elseif($gateway == 6) {
					$info = Helper::nganluong_api($type, $serial, $code, $id);
					
					$result = explode("|", $info);
					
					$status = isset($result[0]) ? $result[0] : '';
					
					$amount = isset($result[10]) ? $result[10] : 0;
					$message = isset($result[0]) ? Helper::GetErrorMessage($result[0]) : '';
				}
				elseif($gateway == 7) {
					$info = Helper::maxpay_api($type, $serial, $code, $id);
					
					$status = $info->code;
					$amount = isset($info->card_amount) ? intval($info->card_amount) : 0;
					$message = isset($info->response_message) ? $info->response_message : '';
				}
				elseif($gateway == 8) {
					$info = Helper::bapvip_api($type, $serial, $code, $id, $account->username, $ip);
					
					$status = $info->code;
					$amount = isset($info->data->Amount) ? intval($info->data->Amount) : 0;
					$message = isset($info->msg) ? $info->msg : '';
				}
				elseif($gateway == 10) {
					$info = Helper::hpay_api($type, $serial, $code, $id);
					
					$status = $info->code;
					$amount = isset($info->info_card) ? intval($info->info_card) : 0;
					$message = isset($info->msg) ? $info->msg : '';
				}
				elseif($gateway == 13) {
					$info = Helper::key24h_api($type, $serial, $code, $id);
					
					$status = $info->status;
					$amount = isset($info->amount) ? intval($info->amount) : 0;
					$message = isset($info->message) ? $info->message : '';
				}				
				
			
				if(($gateway == 1 && $status == 200) || ($gateway == 2 && $status == 0 && $amount >= 10000) || ($gateway == 3 && $status == 1) || ($gateway == 6 && $status == '00') || ($gateway == 7 && $status == 1) || ($gateway == 8 && $status == 0 && $amount > 0) || ($gateway == 10 && $status == 0 && $amount > 0) || ($gateway == 13 && $status == '0')) {
			
					$recharge = Recharge::create([
						'uid' => $account->id,
						'gateway' => $gateway,
						'merchant' => $merchant_id,
						'type' => $type,
						'serial' => $serial,
						'code' => $code,
						'zoneid' => $account->zoneid,
						'ip' => $ip,
						'status' => 1,
						'amount' => $amount,
					]);

					$exchange = Config::get('mu.recharge.exchange');
					$promotion = Config::get('mu.recharge.promotion');
					$promotion_date = Config::get('mu.recharge.date');
					
					$promotion_gate = Config::get('recharge.bonus.gate');
				
					$balance = $amount * $exchange;
					
					$bonus = 0;
					if($promotion > 0 && $dt >= $promotion_date[0] && $dt <= $promotion_date[1]) {
						$bonus = $balance * $promotion / 100;
					}
					$bonus_gate = 0;
					if($type == 4) {
						$bonus_gate = $balance * $promotion_gate / 100;
					}
				
					$account->balance = $account->balance + $balance + $bonus + $bonus_gate;
					$account->save();	
				
					return redirect()->route('recharge.show', [$code])->with('status', __('usercp.message_recharge_success'));
				
				} else {
				
					return redirect()->back()
							->withInput($request->only('code'))
							->withErrors(['code' => $message]);
				}
			} else {
				$amount_pending = $request->amount; // thêm mệnh giá khi chế độ duyệt tay
				Recharge::create([
					'uid' => $account->id,
					'gateway' => $gateway,
					'merchant' => $merchant_id,
					'type' => $type,
					'serial' => $serial,
					'code' => $code,
					'zoneid' => $account->zoneid,
					'ip' => $ip,
					'status' => 0,
					'amount' => $amount_pending, // default 0
				]);
					
				return redirect()->route('recharge.show', [$code])->with('status', __('usercp.message_recharge_pending'));
			}
		} 
		
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
		$account = Auth::user();
		
        $recharge = Recharge::where('uid', $account->id)
							->where('code', $id)
							->first();
		
		return view('usercp.recharge.show', ['recharge' => $recharge]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
