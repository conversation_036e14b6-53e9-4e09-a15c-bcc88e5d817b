<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Server;
use DB;
use Config;

class OnlineController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('admin');
    }	
	
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
		$keyword = NULL;
		$zoneid= NULL;
		
		
		$platform_name = Config::get('mu.platform.name');
		$servers = Server::orderBy('Id', 'asc')->get();
		if(!$request->has('zoneid')){
			$zoneid = $servers->first()->Id;
		} else {
			$zoneid = $request->input('zoneid');
		}
		
		if($request->has('keyword') && !empty($request->input('keyword'))) {
			
			$keyword = $request->input('keyword');	
		
			
			$online = DB::connection($zoneid)
						->table('t_login')
						->join('t_roles', 't_login.rid', '=', 't_roles.rid')
						->select('t_login.*', 't_roles.rname', 't_roles.zoneid')
						->where('t_roles.rname', 'like', '%' . $keyword .'%')
						->where('t_login.userid', 'like', $platform_name .'%')
						->where('t_roles.zoneid', '=', $zoneid)
						->orderBy('t_login.dayid', 'desc')
						->paginate(20);
								
		} else {
			// $server = Server::orderBy('Id', 'desc')->first();
		
			// $online = DB::connection($server->Id)
						// ->table('t_login')
						// ->join('t_roles', 't_login.rid', '=', 't_roles.rid')
						// ->select(
							// 't_login.*'
							// ,
							// 't_roles.rname',
							// 't_roles.zoneid'
						// )
						// ->where('t_login.userid', 'like', $platform_name .'%')
						// ->orderBy('t_login.dayid', 'desc')
						// ->paginate(20);
			
			$online = DB::connection($zoneid)
						->table('t_login')
						->join('t_roles', 't_login.rid', '=', 't_roles.rid')
						->select('t_login.*', 't_roles.rname', 't_roles.zoneid')
						->where('t_login.userid', 'like', $platform_name .'%')
						->where('t_roles.zoneid', '=', $zoneid)
						->orderBy('t_login.dayid', 'desc')
						->paginate(20);
		}
		
        return view('admincp.online', ['online' => $online, 'keyword' => $keyword, 'servers' => $servers, 'zoneid' => $zoneid]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
