<?php

namespace App\Http\Controllers\AdminCP;

use App\t_rewardCuston;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Server;
use App\Http\Requests\AdminCP\RewardCustomRequest;

use Config;

class TRewardCustonController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function __construct()
    {
        $this->middleware('admin.super');
    }
    public function index()
    {
        //
        $rw = t_rewardCuston::orderBy('created_at','DESC')->paginate(10);
        return view('admincp.rewardcuston',['rw_custom'=>$rw]);
    }


    public function createReward($id){
        //reward_custon
        $rwc = t_rewardCuston::where('id',$id)->first();
        $note='';
        $reward_with_server =  config('rewardsC.reward_'.$rwc->zondid.'_'.$rwc->id);
        if(!$reward_with_server){
            $note ='Không tìm thấy file cấu hình phần thưởng.'; 
            $reward_with_server =  config('rewardsC.reward_def');
        }
        return view('admincp.rewardcuston.createReward',['reward_with_server'=>$reward_with_server,'reward'=>$rwc,'note'=>$note]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
        $servers = Server::orderBy('Id', 'desc')->get();
        
        return view('admincp.rewardcuston.create',[
            'servers'=>$servers,
            //'types'=> $list_type_reward,
        ]);
    }


    public function create_post(Request $request){

        //dd($request->all());

        if(!isset($request->name) || empty($request->name) ){
            return redirect()->back()->withErrors(['name'=>'12'])->with('status',2);
        }
        // if(empty($request->zoneid) ){
        //     return redirect()->back()->withErrors(['zoneid'=>'12'])->with('status',2);
        // }
        if(!isset($request->date_start)  || empty($request->date_start) ){
            return redirect()->back()->withErrors(['date_start'=>'12'])->with('status',2);
        }
        if(!isset($request->date_end) || empty($request->date_end) ){
            return redirect()->back()->withErrors(['date_end'=>'12'])->with('status',2);
        }
        

        $create_rw =  t_rewardCuston::create([
            'name'=>$request->name,
            'zondid'=>$request->zoneid,
            'DayForm'=>$request->date_start,
            'DayTo'=>$request->date_end,
        ]);
        if( $create_rw){
            if($request->zoneid==0){
                $serverName= 'Dùng cho tất cả các máy chủ!';
            }else{
                $server= Server::where('Id',$request->zoneid)->first();
                $serverName= $server->ServerName;
            }
            
            return redirect()->route('reward_custon')->with('message',__('admincp.reward.custon.created.success',[
           'name'=>$request->name,
           'zoneid'=> $serverName,
            'formday'=>$request->date_start,
            'today'=>$request->date_end    
        ]))->with('status',1);

        }else{
            return redirect()->back()->with('message',__('admincp.reward.custon.created.failed'))->with('status',2);
        }
    }
    // public function edit($id){


    // }
    public function delete($id){

        $rw = t_rewardCuston::where('id','=',$id)->first();
        $delete_rwc = t_rewardCuston::where('id','=',$id)->delete();

        if( $delete_rwc){
            return redirect()->route('reward_custon')->with('message',__('admincp.reward.custon.delete.success',['name'=> $rw->name]))->with('status',1);
        }else{
            return redirect()->route('reward_custon')->with('message',__('admincp.reward.custon.delete.failed') )->with('status',2);
        }
        

    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\t_rewardCuston  $t_rewardCuston
     * @return \Illuminate\Http\Response
     */
    public function show(t_rewardCuston $t_rewardCuston)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\t_rewardCuston  $t_rewardCuston
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
        $servers = Server::orderBy('Id', 'desc')->get();
        $rewardcuston = t_rewardCuston::where('id',$id)->first();
        return view('admincp.rewardcuston.edit',[
            'servers'=>$servers,
            'reward_custon'=>$rewardcuston
        ]);
    }
    public function edit_post(RewardCustomRequest $request){

        $id = $request->id;
        $rewardcuston = t_rewardCuston::where('id', $id)->first();
        if(!$rewardcuston){
            return redirect()->route('reward_custon')->with('message',__('admincp.reward.custon.edit.failed') )->with('status',2);
        }

        $rewardcuston->name= $request->name;
        $rewardcuston->zondid= $request->zoneid;
        $rewardcuston->DayForm= $request->date_start;
        $rewardcuston->DayTo= $request->date_end;
        $rewardcuston->created_at = $rewardcuston->created_at;
        
        $rewardcuston->save();

        return redirect()->route('reward_custon')->with('message',__('admincp.reward.custon.edit.success',['name'=>$request->name]) )->with('status',1);
        

       

    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\t_rewardCuston  $t_rewardCuston
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, t_rewardCuston $t_rewardCuston)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\t_rewardCuston  $t_rewardCuston
     * @return \Illuminate\Http\Response
     */
    public function destroy(t_rewardCuston $t_rewardCuston)
    {
        //
        
    }
}
