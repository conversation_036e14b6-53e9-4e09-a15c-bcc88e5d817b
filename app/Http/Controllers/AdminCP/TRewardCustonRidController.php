<?php

namespace App\Http\Controllers\AdminCP;

use App\t_rewardCuston_rid;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\t_rewardCuston;
use App\Account;
use App\Helper;
use DB;
use Auth;
use Hash;
use Config;


use App\Server;



class TRewardCustonRidController extends Controller
{   
     public function __construct()
    {
        $this->middleware('admin.super');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */


    public function getRidByaccount(Request $request){
        
        
    }

    public function api_find_account(Request $request){

        if($request->type=='accounts'){
            $accounts =Account::select('*')->where('username', 'like', '%' .$request->keyword .'%')->orderBy('username')->get();
        //echo json_encode(['data'=>$accounts]);
        if( count($accounts) > 0  ){
          ?>
        <div style="margin-top: 10px; " class="form-group " >
         <select    class="form-control" name="uid" id="username">
            <option value="">Chọn tài khoản</option>
             <?php 
        foreach ( $accounts as $key => $value) {
            ?>
            <option   value="<?php echo $value->userid ?>"><?php echo $value->username ?></option>
            <?php
            }
             ?>
         </select>

            <script>
                jQuery("#username").on('change',function($){
                    jQuery.ajax({
                        url:'<?php echo route('api_get_user'); ?>',
                        dataType:'html',
                        method:'get',
                        data:{

                            uid:jQuery(this).val(),
                            rewardId:'<?php echo $request->rw_id; ?>',
                            zoneid:jQuery('#zoneidselected').val(),

                        },
                        success:function(data){
                            console.log(data)
                            jQuery("#rids-box").html(data);
                        }
                    })
                    
                })
            </script>
         </div>
        <?php  
    }else{
        echo 'Không tìm thấy tài khoảng nào.';
    }

    }else{

            //zondid

            //$reward = t_rewardCuston::where('id',$request->rewardId)->first();
            $zoneid =$request->zoneid;
            $getRoles = DB::connection($zoneid)->table('t_roles')->where('userid',$request->uid)->get();

            if($getRoles){

                ?>
                    <div style="margin-top: 10px; " class="form-group " >
                        <label for="rid"><?php echo __('admincp.reward.custon.rid.name') ?> </label>
                        <select    class="form-control" name="rid" id="rid_ajax">
                            <option value="">Chọn nhân vật</option>
                                <?php 
                                    foreach ( $getRoles as $key => $value) {?>
                                     <option   value="<?php echo $value->rid ?>"><?php echo $value->rname ?></option>
                                <?php
                                }
                                ?>
                        </select>
                    </div>
                <?php
            }
            
        }
    }
    public function  delete($id){

        $remove= t_rewardCuston_rid::where('id','=',$id)->delete();
        return redirect()->route('reward_custon_list_rid')->with('message','Xóa thành công');
    }
    public function list_rid(Request $request){

        $servers = Server::orderBy('Id', 'desc')->get();
        $reward_custons  = t_rewardCuston::orderBy('id', 'desc')->get();
        $list_rid = t_rewardCuston_rid::join('t_reward_custons','t_reward_custon_rids.rwid','=','t_reward_custons.id')
        ->select('t_reward_custons.*','t_reward_custon_rids.*');

        if(isset($request->reward_custon_id)){
            $list_rid=$list_rid->where('t_reward_custons.id','=',$request->reward_custon_id); 
        }
        if(isset($request->account)){
            $account = Account::where('username',trim($request->account))->first();
            $list_rid=$list_rid->where('t_reward_custon_rids.uid','=',$account?$account->id:''); 
        }
        if(isset($request->zoneid)){
            $list_rid=$list_rid->where('t_reward_custons.zondid','=',$request->zoneid); 
        }

        $list_rid = $list_rid->orderBy('t_reward_custons.created_at', 'desc')->paginate(20);
        return view('admincp.rewardcuston.list_rid',['rids'=> $list_rid,'servers'=>$servers,'reward_custons'=> $reward_custons]);

    }
    public function create($id)
    {
        //
        $list_type_reward = Config('rewardsC.reward_config');
        $servers = Server::orderBy('Id', 'desc')->get();

        $rq_custon =t_rewardCuston::where('id',$id)->first();
        return view('admincp.rewardcuston.create_rid',['servers'=>$servers,'rw_name'=>$rq_custon->name,'rw_id'=>$id,'types_reward'=>$list_type_reward,'zoneid'=>$rq_custon->zondid]);
    }
    public function create_post(Request $request,$id){

        $rq_custon =t_rewardCuston::where('id',$id)->first();

        $zoneId =  $request->zoneid;
        

        if(!isset($request->uid) || empty($request->uid) ){
            return redirect()->back()->withErrors(['uid'=>'không tìm thấy thông tin tai khoản'])->with('status',2);
        }
       

         if(!isset($request->top) || empty($request->top) ){
            return redirect()->back()->withErrors(['top'=>'Lỗi chưa nhập top'])->with('status',2);
        }

        
       

        if(!isset($request->type_rid) || empty($request->type_rid) ){
            return redirect()->back()->withErrors(['type_rid'=>'Chưa chọn phần thưởng'])->with('status',2);
        }
        

        $account =  Account::where('userid',trim($request->uid))->first();
       
        $character = DB::connection($zoneId)->table('t_roles')->select('rname')->where('rid','=',trim($request->rid))->first(); 
        
        

        if(!$character){
            return redirect()->back()->with('message','Không tìm thấy thông tin nhân vật này!')->with('status',2);
        }


        $add_rid_rq = t_rewardCuston_rid::create([
            'rid'=>$request->rid,
            'uid'=>$account->id,
            'top'=>$request->top,
            'type_rw'=>$request->type_rid,
            'zondid'=> $zoneId,         
            'rwid'=>$id
           
        ]);
        if($add_rid_rq){
            return redirect()->route('reward_custon')->with('message',__('admincp.reward.custon.rid.created.success',[
           'account'=>$account->username,
           'rid'=> $request->rid,
            'name'=> $rq_custon->name,
            'character'=>$character->rname
         
        ]))->with('status',1);

        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\t_rewardCuston_rid  $t_rewardCuston_rid
     * @return \Illuminate\Http\Response
     */
    public function show(t_rewardCuston_rid $t_rewardCuston_rid)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\t_rewardCuston_rid  $t_rewardCuston_rid
     * @return \Illuminate\Http\Response
     */
    public function edit(t_rewardCuston_rid $t_rewardCuston_rid)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\t_rewardCuston_rid  $t_rewardCuston_rid
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, t_rewardCuston_rid $t_rewardCuston_rid)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\t_rewardCuston_rid  $t_rewardCuston_rid
     * @return \Illuminate\Http\Response
     */
    public function destroy(t_rewardCuston_rid $t_rewardCuston_rid)
    {
        //
    }
}
