<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Payment;

class PaymentController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('admin');
    }	
	
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
		$keyword = NULL;
		
		if($request->has('keyword')) {
			$keyword = $request->input('keyword');	

			$payments = Payment::join('zt_serverdata', 't_payment.zoneid', '=', 'zt_serverdata.Id')
								->join('zt_account', 't_payment.uid', '=', 'zt_account.id')
								->select('t_payment.*', 'zt_serverdata.ServerName as server')
								->orderBy('t_payment.id', 'desc')
								->where('zt_account.username', 'like', '%' . $keyword . '%')
								->get();			
		} else {
			$payments = Payment::join('zt_serverdata', 't_payment.zoneid', '=', 'zt_serverdata.Id')
								->select('t_payment.*', 'zt_serverdata.ServerName as server')
								->orderBy('t_payment.id', 'desc')->paginate(20);
		}
								
		return view('admincp.payment', ['payments' => $payments, 'keyword' => $keyword]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
