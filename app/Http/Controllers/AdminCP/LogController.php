<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\History;
use App\Giftcode_Log;
use App\Log;
use App\Reward;
use App\Webshop;
use App\Webshop_Log;
use App\Rank_Log;
use App\Helper;
use Config;
use Carbon\Carbon;
use App\t_luckywinwheel;
use DB;

use App\Server;


class LogController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('admin');
    }
		
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
		$keyword = NULL;
		$type = NULL;
		$items = NULL;
        $zoneid= NULL;

        $servers = Server::orderBy('Id','des')->get();

		if($request->has('keyword')) {
			
			$keyword = $request->input('keyword');
			
			if($request->has('type')) {
				
				$type = $request->input('type');
				
				if($type == 1) {
					$logs = Log::join('zt_account', 'zt_account.UserID', '=', 't_log.uid')
							->select('t_log.*', 'zt_account.UserName')
							->orderBy('t_log.id', 'desc')->where('zt_account.UserName', 'like', '%' . $keyword .'%')->paginate(20);
				} elseif($type == 2) {

					$logs = Giftcode_Log::join('zt_account', 'zt_account.UserID', '=', 't_giftcode_log.uid')
							->select('t_giftcode_log.*', 'zt_account.UserName');

                    if($request->has('zoneid')){
                        $zoneid = $request->input('zoneid');
                        $logs=$logs->where('t_giftcode_log.zoneid',$zoneid);
                    }

					$logs=$logs->orderBy('t_giftcode_log.id', 'desc')->where('zt_account.UserName', 'like', '%' . $keyword .'%')->paginate(20);				
				} elseif($type == 3) {
                    
					$logs = Reward::join('zt_account', 'zt_account.UserID', '=', 't_reward.uid')
							->select('t_reward.*', 'zt_account.UserName');

                    if($request->has('zoneid')){
                        $zoneid = $request->input('zoneid');
                        $logs=$logs->where('t_reward.zoneid',$zoneid);
                    }


					$logs=$logs->where('zt_account.UserName', 'like', '%' . $keyword .'%')->orderBy('t_reward.id', 'desc')->paginate(20);	

				} elseif($type == 4) {
                     $logs =Webshop_Log::join('zt_account', 'zt_account.UserID', '=', 't_webshop_log.uid')->join('t_webshop','t_webshop.item','=','t_webshop_log.item')
                             ->select('t_webshop_log.*','t_webshop.price','t_webshop.name', 'zt_account.UserName')
                             ->where('zt_account.UserName', 'like', '%' . $keyword .'%');

                    if($request->has('zoneid')){
                        $zoneid = $request->input('zoneid');
                        $logs=$logs->where('t_webshop_log.zoneid',$zoneid);
                    }

                    $logs=$logs->orderBy('t_webshop_log.updated_at', 'desc')->paginate(20);               
                } 
                elseif($type == 5) {

                    $logs = Rank_Log::join('zt_account', 'zt_account.UserID', '=', 't_rank_log.uid')
                            ->select('t_rank_log.*', 'zt_account.UserName')
                            ->orderBy('t_rank_log.id', 'desc');

                    if($request->has('zoneid')){

                        $zoneid = $request->input('zoneid');
                        $logs=$logs->where('t_rank_log.zoneid',$zoneid);
                        
                    }


                    
                    $logs=$logs->where('zt_account.UserName', 'like', '%' . $keyword .'%')->paginate(20);  


                }  				
			} else {
				$logs = History::join('zt_account', 'zt_account.UserID', '=', 't_history.uid')
							->select('t_history.*', 'zt_account.UserName')
							->orderBy('t_history.id', 'desc')->where('zt_account.UserName', 'like', '%' . $keyword .'%')->paginate(20);
			}
		} else {
		
			if($request->has('type')) {
				
				$type = $request->input('type');

				if($type == 1) {
					
					$logs = Log::join('zt_account', 'zt_account.UserID', '=', 't_log.uid')
							->select('t_log.*', 'zt_account.UserName')
							->orderBy('t_log.id', 'desc')->paginate(20);
					
				} elseif($type == 2) {
					

                    if($request->has('zoneid')){
                        $zoneid = $request->input('zoneid');
						$logs = DB::connection($zoneid)->table('z_giftcoderecord')->join('t_roles', 'z_giftcoderecord.userid', '=', 't_roles.userid')->distinct()->select('z_giftcoderecord.id as id', 'z_giftcoderecord.userid', 'z_giftcoderecord.userid as uid', DB::raw('COALESCE(zoneid, 1) as zoneid'), 'z_giftcoderecord.code as giftcode', 'z_giftcoderecord.created_at');
						
						$uids = $logs->get()->pluck('userid');
						$uids->transform(function($item, $key) {
							
							return substr($item, 2);
						});
						$accounts = \App\Account::whereIn('UserID', $uids)->get()->pluck('UserName', 'UserID');
						$logs = $logs->paginate(20);
						$logs->transform(function($item, $key)use($accounts) {
							$item->username = $accounts[substr($item->userid, 2)];
							return $item;
						});
                    } else {
						
					}

					$logs=$logs->sortBy(['id' => 'desc']);	

				} elseif($type == 3) {
					$logs = Reward::join('zt_account', 'zt_account.UserID', '=', 't_reward.uid')
							->select('t_reward.*', 'zt_account.UserName');

                     if($request->has('zoneid')){
                        $zoneid = $request->input('zoneid');
                        $logs=$logs->where('t_reward.zoneid',$zoneid);
                    }

                    $logs=$logs->orderBy('t_reward.id', 'desc')->paginate(20);
						
				}
                elseif($type == 4) {

                    $logs =Webshop_Log::join('zt_account', 'zt_account.UserID', '=', 't_webshop_log.uid')->join('t_webshop','t_webshop.item','=','t_webshop_log.item')
                        ->select('t_webshop_log.*','t_webshop.name','t_webshop.price', 'zt_account.UserName');

                    if($request->has('zoneid')){
                        $zoneid = $request->input('zoneid');
                        $logs=$logs->where('t_webshop_log.zoneid',$zoneid);
                    }

                     $logs= $logs->orderBy('t_webshop_log.updated_at', 'DESC')->paginate(20);   

                    //dd($logs)  ;       
                }
                elseif($type == 5) {

                    $logs = Rank_Log::join('zt_account', 'zt_account.UserID', '=', 't_rank_log.uid')
                            ->select('t_rank_log.*', 'zt_account.UserName');

                    if($request->has('zoneid')){

                        $zoneid = $request->input('zoneid');
                        $logs=$logs->where('t_rank_log.zoneid',$zoneid);

                    }
                                

                     $logs=$logs->orderBy('t_rank_log.id', 'desc')->paginate(20);   

                }		
			} else {		
				$logs = History::join('zt_account', 'zt_account.UserID', '=', 't_history.uid')
							->select('t_history.*', 'zt_account.UserName as username')
							->orderBy('t_history.id', 'desc')->paginate(20);
			}
		}
		
        return view('admincp.log', ['logs' => $logs,
         'keyword' => $keyword, 
         'type' => $type,
         'zoneid'=>$zoneid,
         'servers'=>$servers
     ]);
    }


    public function winwheelLog(Request $request){

        $today = Carbon::now();
        $coinTotal = [
            'today'=>t_luckywinwheel::whereraw("date(created_at) = CURDATE()")->sum('coin'),
            'yesterday'=>t_luckywinwheel::whereDate('created_at','=',$today->subDay()->toDateString())
                    ->sum('coin'),
            'week'=>t_luckywinwheel::whereDate('created_at','>=',$today->subWeek()->toDateString())
                    ->sum('coin'),
            'month'=>t_luckywinwheel::whereDate('created_at','>=',$today->subWeek()->toDateString())
                    ->sum('coin'),
        ];

        $chipTotal = [
            'today'=>t_luckywinwheel::whereraw('date(created_at) = CURDATE()')
                    ->sum('chip'),
            'yesterday'=>t_luckywinwheel::whereDate('created_at','=',$today->subDay()->toDateString())
                    ->sum('chip'),
            'week'=>t_luckywinwheel::whereDate('created_at','>=',$today->subWeek()->toDateString())
                    ->sum('chip'),
            'month'=>t_luckywinwheel::whereDate('created_at','>=',$today->subWeek()->toDateString())
                    ->sum('chip'),
        ];


        $chipBuyTotal = [
            'today'=>t_luckywinwheel::whereraw("date(created_at) = CURDATE()")
                    ->sum('buyChip'),
            'yesterday'=>t_luckywinwheel::whereDate('created_at','=',$today->subDay()->toDateString())
                    ->sum('buyChip'),
            'week'=>t_luckywinwheel::whereDate('created_at','>=',$today->subWeek()->toDateString())
                    ->sum('buyChip'),
            'month'=>t_luckywinwheel::whereDate('created_at','>=',$today->subWeek()->toDateString())
                    ->sum('buyChip'),
        ];

        $coinBuyTotal = [
            'today'=>t_luckywinwheel::whereraw("date(created_at) = CURDATE()")
                    ->sum('buyCoin'),
            'yesterday'=>t_luckywinwheel::whereDate('created_at','=',$today->subDay()->toDateString())
                    ->sum('buyCoin'),
            'week'=>t_luckywinwheel::whereDate('created_at','>=',$today->subWeek()->toDateString())
                    ->sum('buyCoin'),
            'month'=>t_luckywinwheel::whereDate('created_at','>=',$today->subWeek()->toDateString())
                    ->sum('buyCoin'),
        ];

        
        $totalCoin = [
            'today' => $coinTotal['today']- $coinBuyTotal['today']   ,
            'yesterday' =>$coinTotal['yesterday']- $coinBuyTotal['yesterday'],
            'week' =>   $coinTotal['week']-$coinBuyTotal['week'],
            'month' =>  $coinTotal['month']- $coinBuyTotal['month']
        ];

         $totalChip = [
            'today' =>   $chipTotal['today']- $chipBuyTotal['today'],
            'yesterday' =>  $chipBuyTotal['yesterday']-$chipTotal['yesterday'] ,
            'week' =>  $chipBuyTotal['week']-$chipTotal['week'],
            'month' =>   $chipBuyTotal['month']-$chipTotal['month'],
        ];



        // $logs = t_luckywinwheel::join('zt_account', 'zt_account.UserID', '=', 't_luckywinwheel.uid')
        // ->select('t_luckywinwheel.*', 'zt_account.UserName as username');

        $logs = t_luckywinwheel::join('zt_account', DB::raw("CONCAT('ZT', zt_account.UserID) COLLATE utf8_unicode_ci"), '=', 't_luckywinwheel.uid')
        ->select('t_luckywinwheel.*', 'zt_account.UserName as username');
        
        if($request->keyword){
           $logs=$logs->where('zt_account.UserName', 'like', '%' . $request->keyword .'%');
        }
        if($request->rewardId){
            $logs= $logs->where('rewardId',$request->rewardId);
        }
        $logs = $logs->orderBy('t_luckywinwheel.created_at','DESC')
        ->paginate(20);
        $Rewards= Config('luckywinwheel.items');
        return view('admincp.winwheel.log',[
            'logs'=>$logs,
            'rewards'=>$Rewards,
            'rewardId'=>$request->rewardId,
            'keyword'=>$request->keyword,
            'goods_parse' => Helper::getGoods(),
            'totalCoin'=>$totalCoin,
            'totalChip'=>$totalChip,
            'coinTotal'=>$coinTotal,
            'chipTotal'=>$chipTotal,
            'chipBuyTotal'=>$chipBuyTotal,
            'coinBuyTotal'=>$coinBuyTotal

    ]);
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
