<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Http\Requests\AdminCP\WebshopRequest;

use App\Webshop;
use App\FileLoader;
use Illuminate\Filesystem\Filesystem;
use Carbon\Carbon;

class WebshopController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('admin.super');
    }	
	
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $webshop = Webshop::orderBy('Id', 'desc');
        if($request->catId){
           $webshop->where('idCat',$request->catId) ;
        }
		$webshop = $webshop->paginate(10);

        return view('admincp.webshop', ['webshop' => $webshop,'catId'=>$request->catId]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
		
		$webshop = Webshop::orderBy('Id', 'desc')->first();
		
		if($webshop !== NULL) {
			$webshopid = $webshop->id + 1;
		} else {
			$webshopid = 1;
		}
		
        return view('admincp.webshop.create', ['webshopid' => $webshopid]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(WebshopRequest $request)
    {
		Webshop::create([
			'id' => $request->id,
			'name' => $request->name,
			'item' => $request->item,
			'items' => explode(PHP_EOL,$request->items),
            'idCat'=>$request->cat_id,
			'price' => $request->price
		]);
		
		$status = "Thêm vật phẩm Webshop thành công!";
		return redirect()->route('webshop.index')->with('status', $status);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
		$webshop = Webshop::where('id', $id)->first();
		
        return view('admincp.webshop.edit', ['webshop' => $webshop]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(WebshopRequest $request, $id)
    {
        $webshop = Webshop::where('id', $id)->first();
		$webshop->name = $request->name;
		$webshop->item = $request->item;
        $webshop->idCat =$request->cat_id;
		$webshop->items = explode(PHP_EOL, $request->items);
		$webshop->price = $request->price;	
		$webshop->save();
		$status = "Lưu vật phẩm Webshop thành công!";
		return redirect()->route('webshop.index')->with('status', $status);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        Webshop::where('id', $id)->delete();
		$status = "Xóa vật phẩm Webshop thành công!";
		return redirect()->route('webshop.index')->with('status', $status);
    }
}
