<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\BattlePassSeason;
use App\BattlePassReward;
use App\UserBattlePass;
use App\BattlePassClaim;
use App\Account;
use Auth;
use DB;

class BattlePassController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display a listing of battle pass seasons.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $seasons = BattlePassSeason::orderBy('created_at', 'desc')->paginate(10);
        return view('admincp.battlepass.index', compact('seasons'));
    }

    /**
     * Show the form for creating a new season.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admincp.battlepass.create');
    }

    /**
     * Store a newly created season.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'max_level' => 'required|integer|min:1|max:200',
            'price' => 'required|integer|min:0'
        ]);

        BattlePassSeason::create($request->all());

        return redirect()->route('battle_pass.index')
                        ->with('success', 'Battle Pass season created successfully.');
    }

    /**
     * Display the specified season.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $season = BattlePassSeason::with('rewards')->findOrFail($id);
        $userCount = UserBattlePass::where('season_id', $id)->count();
        $premiumCount = UserBattlePass::where('season_id', $id)->where('is_premium', true)->count();
        
        return view('admincp.battlepass.show', compact('season', 'userCount', 'premiumCount'));
    }

    /**
     * Show the form for editing the specified season.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $season = BattlePassSeason::findOrFail($id);
        return view('admincp.battlepass.edit', compact('season'));
    }

    /**
     * Update the specified season.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'max_level' => 'required|integer|min:1|max:200',
            'price' => 'required|integer|min:0',
            'status' => 'boolean'
        ]);

        $season = BattlePassSeason::findOrFail($id);
        $season->update($request->all());

        return redirect()->route('battle_pass.index')
                        ->with('success', 'Battle Pass season updated successfully.');
    }

    /**
     * Remove the specified season.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $season = BattlePassSeason::findOrFail($id);
        $season->delete();

        return redirect()->route('battle_pass.index')
                        ->with('success', 'Battle Pass season deleted successfully.');
    }

    /**
     * Manage rewards for a season.
     *
     * @param  int  $seasonId
     * @return \Illuminate\Http\Response
     */
    public function rewards($seasonId)
    {
        $season = BattlePassSeason::findOrFail($seasonId);
        $rewards = BattlePassReward::where('season_id', $seasonId)
                                  ->orderBy('level')
                                  ->orderBy('is_premium')
                                  ->get();
        
        return view('admincp.battlepass.rewards', compact('season', 'rewards'));
    }

    /**
     * Add reward to season.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $seasonId
     * @return \Illuminate\Http\Response
     */
    public function addReward(Request $request, $seasonId)
    {
        $request->validate([
            'level' => 'required|integer|min:1',
            'reward_type' => 'required|in:coin,item,exp',
            'reward_id' => 'required|integer',
            'reward_amount' => 'required|integer|min:1',
            'is_premium' => 'boolean'
        ]);

        BattlePassReward::create([
            'season_id' => $seasonId,
            'level' => $request->level,
            'reward_type' => $request->reward_type,
            'reward_id' => $request->reward_id,
            'reward_amount' => $request->reward_amount,
            'is_premium' => $request->has('is_premium')
        ]);

        return redirect()->route('battle_pass.rewards', $seasonId)
                        ->with('success', 'Reward added successfully.');
    }

    /**
     * Delete reward.
     *
     * @param  int  $rewardId
     * @return \Illuminate\Http\Response
     */
    public function deleteReward($rewardId)
    {
        $reward = BattlePassReward::findOrFail($rewardId);
        $seasonId = $reward->season_id;
        $reward->delete();

        return redirect()->route('battle_pass.rewards', $seasonId)
                        ->with('success', 'Reward deleted successfully.');
    }

    /**
     * View user progress.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function userProgress(Request $request)
    {
        $query = UserBattlePass::with(['user', 'season']);
        
        if ($request->has('season_id') && $request->season_id) {
            $query->where('season_id', $request->season_id);
        }
        
        if ($request->has('username') && $request->username) {
            $query->whereHas('user', function($q) use ($request) {
                $q->where('UserName', 'like', '%' . $request->username . '%');
            });
        }
        
        $userProgress = $query->orderBy('current_level', 'desc')->paginate(20);
        $seasons = BattlePassSeason::orderBy('created_at', 'desc')->get();
        
        return view('admincp.battlepass.user_progress', compact('userProgress', 'seasons'));
    }

    /**
     * Purchase premium for user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function purchasePremium(Request $request)
    {
        $request->validate([
            'user_id' => 'required|string',
            'season_id' => 'required|integer'
        ]);

        $userBattlePass = UserBattlePass::where([
            'user_id' => $request->user_id,
            'season_id' => $request->season_id
        ])->first();

        if ($userBattlePass) {
            $userBattlePass->purchasePremium();
            return response()->json(['success' => true, 'message' => 'Premium purchased successfully']);
        }

        return response()->json(['success' => false, 'message' => 'User battle pass not found']);
    }

    /**
     * Add experience to user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function addExp(Request $request)
    {
        $request->validate([
            'user_id' => 'required|string',
            'season_id' => 'required|integer',
            'exp' => 'required|integer|min:1'
        ]);

        $userBattlePass = UserBattlePass::where([
            'user_id' => $request->user_id,
            'season_id' => $request->season_id
        ])->first();

        if ($userBattlePass) {
            $userBattlePass->addExp($request->exp);
            return response()->json(['success' => true, 'message' => 'Experience added successfully']);
        }

        return response()->json(['success' => false, 'message' => 'User battle pass not found']);
    }
}
