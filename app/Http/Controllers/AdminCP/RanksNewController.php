<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\t_ranks_list;
use App\Helper;
use App\Server;
use Config;
use DB;

class RanksNewController extends Controller
{
    //

    function __construct()
    {
    	 $this->middleware('admin');
    }

    public function index(){
    	
    	$list_rank_rewards = t_ranks_list::get();
    	return view('admincp.ranks.index',['ranks'=>$list_rank_rewards]);
    }

    public function createNew(){
    	$servers = Server::orderby('id')->get();

    	return view('admincp.ranks.create',['server'=>$servers]);
    }

    public function updaterank($id){
    	$servers = Server::orderby('id')->get();
    	$rank = t_ranks_list::where('id',$id)->first();
    	return view('admincp.ranks.update',['server'=>$servers,'rank'=>$rank]);
    }


    public function updaterank_post(Request $request,$id){

    	$servers = Server::orderby('id')->get();
    	$rank = t_ranks_list::where('id',$id)->first();

    	if(!$request->zoneid){
    		return redirect()->back()->withErrors(['zoneid'=>'Vui lòng chọn máy chủ.']);
    	}
        
    	$rank->name=$request->name;
		$rank->limit=$request->limit;
		$rank->zoneid=$request->zoneid;
		$rank->lifecount=$request->lifecount;
        $rank->lifecount_limit=$request->lifecount_limit;

		$rank->save();
    	return redirect()->route('ranks_das')->with('message',"cập nhập rank $request->name thành công!")->with('status',1);
    }

    public function delete_rank($id){

    	$rank = t_ranks_list::where('id',$id)->first();
    	$delete_rank = t_ranks_list::destroy($id);

    	if($delete_rank){
    		return redirect()->route('ranks_das')->with('message',"Xóa rank $rank->name thành công!")->with('status',1);
    	}else{
    		return redirect()->back()->with('message','có lỗi')->with('status',2);
    	}

    }
    public function createNew_post(Request $request){

    	if(!$request->zoneid){
    		return redirect()->back()->withErrors(['zoneid'=>'Vui lòng chọn máy chủ.']);

    	}

    	$insert = t_ranks_list::create(array(
		'name'=>$request->name,
		//'rank'=>$request->rank,
		'limit'=>$request->limit,
		'zoneid'=>$request->zoneid,
        'lifecount'=>$request->lifecount,
        'lifecount_limit'=>$request->lifecount_limit
    	));


    	if($insert){
    		return redirect()->route('ranks_das')->with('message',"Tạo rank $request->name thành công")->with('status',1);
    	}else{
    		return redirect()->back()->with('message','Tạo rank lỗi')->with('status',2);
    	}

    }

}
