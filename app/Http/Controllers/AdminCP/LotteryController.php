<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Lottery;
use Carbon\Carbon;

class LotteryController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('admin');
    }	
	
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
		
		$amount = [
			'today' => Lottery::whereDate('created_at', '=', Carbon::now()->toDateString())->sum('amount'),
			'yesterday' => Lottery::whereDate('created_at', '=', Carbon::now()->subDay()->toDateString())->sum('amount'),
			'this_week' => Lottery::whereDate('created_at', '>=', Carbon::now()->subWeek()->toDateString())->sum('amount'),
			'this_month' => Lottery::whereDate('created_at', '>=', Carbon::now()->subMonth()->toDateString())->sum('amount'),
		];
		
		$balance = [
			'today' => Lottery::where('status', 1)->whereDate('created_at', '=', Carbon::now()->toDateString())->sum('balance'),
			'yesterday' => Lottery::where('status', 1)->whereDate('created_at', '=', Carbon::now()->subDay()->toDateString())->sum('balance'),
			'this_week' => Lottery::where('status', 1)->whereDate('created_at', '>=', Carbon::now()->subWeek()->toDateString())->sum('balance'),
			'this_month' => Lottery::where('status', 1)->whereDate('created_at', '>=', Carbon::now()->subMonth()->toDateString())->sum('balance'),
		];

		$total = [
			'today' => $amount['today'] - $balance['today'],
			'yesterday' => $amount['yesterday'] - $balance['yesterday'],
			'this_week' => $amount['this_week'] - $balance['this_week'],
			'this_month' => $amount['this_month'] - $balance['this_month'],
		];			
		
		$lotteries = Lottery::orderBy('id', 'desc')->paginate(20);
		
		return view('admincp.lottery', ['lotteries' => $lotteries, 'amount' => $amount, 'balance' => $balance, 'total' => $total]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
