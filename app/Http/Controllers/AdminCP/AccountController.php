<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Http\Requests\AdminCP\AccountRequest;

use App\Account;
use App\Server;
use App\Log;

use App\Helper;
use DB;
use Auth;
use Hash;
use Config;

class AccountController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('admin');
    }
		
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
		$keyword = NULL;
		$filter = NULL;
		
		if($request->has('filter')) {
			
			$filter = [
				'group' => $request->input('group')
			];
			
			$account = [];
			
			if($request->has('group')) {
				$account['groupid'] = $filter['group'];
				
				$accounts = Account::orderBy('Money', 'desc')->where($account)->paginate(20);
			}

			
		} elseif($request->has('keyword')) {
			
			$keyword = $request->input('keyword');
			
			$accounts = Account::orderBy('Money', 'desc')->where('groupid', '<>', 1)->where('UserName', 'like', '%' . $keyword .'%')->orWhere('phone', 'like', '%' . $keyword .'%')->orWhere('Email', 'like', '%' . $keyword .'%')->orWhere('UserID', 'like', '%' . $keyword .'%')->get();
		} else {
			$accounts = Account::orderBy('Money', 'desc')->where('groupid', '<>', 1)->paginate(20);
		}
		
        return view('admincp.account', ['accounts' => $accounts, 'keyword' => $keyword, 'filter' => $filter]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
		$account = Account::where('UserName', $id)->where('groupid', '<>', 1)->first();
		
		$servers = Server::orderBy('Id', 'desc')->get();
		
		$characters = [];
		foreach ($servers as $server) {
			$characters[] = DB::connection($server->Id)->table('t_roles')->select('rid', 'rname', 'level', 'zoneid', 'isdel')->where('userid', Config::get('mu.prefix_userid') . $account->UserID)->orderBy('level', 'desc')->get();
		}
		
		$character = collect($characters);
		
		$role = $character->flatten(1);
		
		$roles = $role->values()->all();
		
        return view('admincp.account.show', ['account' => ($account), 'roles' => $roles]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(AccountRequest $request, $id)
    {
		
		$user = Auth::user();	
		
        $account = Account::where('UserID', $id)->where('groupid', '<>', 1)->first();
		
		if($request->has('change_pasword')) {
			
			$content = [
				'userid' => $account->UserID,
				'password' => $account->Password
			];
			
			$account->Password = md5($request->password);
			
			// $account->hash = Hash::make(md5($request->password));
			
			$status = __('admincp.message_password_success');	
			
			$type = 3;
		}
		elseif($request->has('change_phone')) {
			
			$content = [
				'userid' => $account->UserID,
				'phone' => $account->phone
			];
			
			$account->phone = $request->phone;
			
			$status = __('admincp.message_phone_success');	
			
			$type = 4;
		}
		elseif($request->has('change_email')) {
			
			$content = [
				'userid' => $account->UserID,
				'email' => $account->Email
			];
			
			$account->Email = $request->email;
			
			$status = __('admincp.message_email_success');	
			
			$type = 5;
		}
		elseif($request->has('change_secret')) {
			
			$content = [
				'userid' => $account->UserID
			];
			
			$account->code_secret = Hash::make($request->code_secret);
			
			$status = __('admincp.message_secret_success');	
			
			$type = 10;
		}				
		elseif($request->has('add')) {
			if($user->groupid == 2) {
				$user->Money = $user->Money - $request->coin;
				
				if($user->Money < 0) {
					
					$status = __('admincp.message_coin_add_failed', ['username' => $user->UserName, 'currency' => Config::get('mu.currency.name')]);
					
					return redirect()->back()
                   	->withInput($request->only('coin'))
                  	->withErrors(['coin' => $status]);
					
				}
				
				$user->save();
			}
			
			$account->Money = $account->Money + $request->coin;
			
			$status = __('admincp.message_coin_add', ['money' => Helper::numberFormat($request->coin), 'currency' => Config::get('mu.currency.name')]);
			
			$type = 1;
			
			$content = [
				'userid' => $account->UserID,
				'coin' => $request->coin,
				'reason' => $request->reason
			];			
		}
		elseif($request->has('sub')) {
			$account->Money = $account->Money - $request->coin;
			
			$status = __('admincp.message_coin_sub', ['money' => Helper::numberFormat($request->coin), 'currency' => Config::get('mu.currency.name')]);
			
			$type = 2;
			
			$content = [
				'userid' => $account->UserID,
				'coin' => $request->coin,
				'reason' => $request->reason
			];			
		}
		elseif($request->has('ban')) {
			
			$content = [
				'userid' => $account->UserID,
				'reason' => $request->cause
			];
			
			$account->groupid = 9;
			
			$status = __('admincp.message_acc_ban', ['acc' => $account->UserName]);	
			
			$type = 12;
		}
		elseif($request->has('unban')) {
			
			$content = [
				'userid' => $account->UserID,
				'reason' => $request->cause
			];
			
			$account->groupid = 0;
			
			$status = __('admincp.message_acc_unban', ['acc' => $account->UserName]);	
			
			$type = 13;
		}		
		
		$account->save();
		
		Log::create([
			'uid' => $user->UserID,
			'type' => $type,
			'content' => $content
		]);		
		
		return redirect()->back()->with('status', $status);		
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
