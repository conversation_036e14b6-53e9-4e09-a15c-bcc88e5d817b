<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Recharge;
use App\Helper;

use DB;
use Config;

use Carbon\Carbon;

class RevenueController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('admin');
    }	
	
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
		$recharges = Recharge::select(DB::raw('SUM(amount) as total'), DB::raw('MONTH(created_at) as month'), DB::raw('YEAR(created_at) as year'))->where('status', 1)->groupBy('month')->groupBy('year')->get();
		
		$revenue_name = [];
		$revenue_amount = [];
		
		foreach($recharges as $recharge) {		
		
			$revenue_name[] = __('admincp.month') . ' ' . $recharge->month . '/' . $recharge->year;
			$revenue_amount[] = $recharge->total;	
			
		}

		$discounts = Recharge::select('type', 'gateway', 'merchant', DB::raw('SUM(amount) as total'), DB::raw('MONTH(created_at) as month'))
							->where('status', 1)
							->groupBy('month', 'gateway', 'type', 'merchant')
							->get();
		
		$monthly_discounts = collect($discounts)->groupBy('month');
		
		$discount_amount = [];
		//dd($monthly_discounts);
		foreach($monthly_discounts as $monthly_discount) {
			
			$discount_amount[] = Helper::getTotalDiscount($monthly_discount);
		}
		
		$revenues = NULL;
		
		if($request->has('date_start') && $request->has('date_end')) {
			
			$date = [
				'start' => $request->input('date_start'),
				'end' => $request->input('date_end')
			];

			if ($date['start'] != $date['end']) {
				$rechanges = Recharge::select('type', 'gateway', 'merchant', DB::raw('SUM(amount) as total'))
								 ->where('status', 1)
								 ->whereBetween('created_at', [$date['start'] . ' 00:00:00', $date['end'] . ' 23:59:59'])
								 ->groupBy('gateway', 'type', 'merchant')
								 ->get();				 	 
			} else {
				$rechanges = Recharge::select('type', 'gateway', 'merchant', DB::raw('SUM(amount) as total'))
								 ->where('status', 1)
								 ->whereDate('created_at', $date['end'])
								 ->groupBy('gateway', 'type', 'merchant')
								 ->get(); 
			}
			
			$revenues = collect($rechanges)->groupBy('merchant');
			
		} else {
			$date = [
				'start' => Carbon::now()->toDateString(),
				'end' => Carbon::now()->toDateString()
			];
		}
		
		$revenue_name = json_encode($revenue_name);
		$revenue_amount = json_encode($revenue_amount, JSON_NUMERIC_CHECK);
		$discount_amount = json_encode($discount_amount, JSON_NUMERIC_CHECK);
		
        return view('admincp.revenue', ['revenue_name' => $revenue_name,
										'revenue_amount' => $revenue_amount,
										'discount_amount' => $discount_amount,
										'revenues' => $revenues,
										'date' => $date]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
