<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\WC_Pool;
use Carbon\Carbon;
use App\WC_Pool_Log;
use App\Account;

class WordCupController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('admin');
    }	
	
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

        $today = Carbon::now();
        $coinTotal = [
            'today'=>WC_Pool_Log::whereraw("date(created_at) = CURDATE()")->sum('amount'),
            'yesterday'=>WC_Pool_Log::whereDate('created_at','=',$today->subDay()->toDateString())
                    ->sum('amount'),
            'week'=>WC_Pool_Log::whereDate('created_at','>=',$today->subWeek()->toDateString())
                    ->sum('amount'),
            'month'=>WC_Pool_Log::whereDate('created_at','>=',$today->subWeek()->toDateString())
                    ->sum('amount'),
        ];

        $coinsendTotal = [
            'today'=>WC_Pool_Log::whereraw("date(created_at) = CURDATE()")->sum('amount_send'),
            'yesterday'=>WC_Pool_Log::whereDate('created_at','=',$today->subDay()->toDateString())
                    ->sum('amount_send'),
            'week'=>WC_Pool_Log::whereDate('created_at','>=',$today->subWeek()->toDateString())
                    ->sum('amount_send'),
            'month'=>WC_Pool_Log::whereDate('created_at','>=',$today->subWeek()->toDateString())
                    ->sum('amount_send'),
        ];

        
        $totalCoin = [
            'today' => $coinsendTotal['today']- $coinTotal['today']   ,
            'yesterday' =>$coinsendTotal['yesterday']- $coinTotal['yesterday'],
            'week' =>   $coinsendTotal['week']-$coinTotal['week'],
            'month' =>  $coinsendTotal['month']- $coinTotal['month']
        ];



		$logs = WC_Pool::orderby('id','DESC')->get();


		return view('admincp.worldcup.index',['logs'=>$logs,'coinsendTotal'=>$coinsendTotal,'coinTotal'=>$coinTotal, 'totalCoin'=> $totalCoin]);
    }
    public function viewuserbypool($id){
        $getPool = WC_Pool::join('t_wc_pool_log',"t_wc_pool_log.id_pool","t_wc_pool.id")->join("zt_account","t_wc_pool_log.userId","zt_account.id")
        ->selectraw("t_wc_pool_log.*, zt_account.username, t_wc_pool.pool as pol")
        ->where('t_wc_pool.id',$id)
        //->groupby("zt_account.id")
        ->paginate(20);

        return view('admincp.worldcup.user',['logs'=>$getPool,'id'=>$id]);
    }

    public function createPool(){
        return view('admincp.worldcup.create');
    }

    public function deletePool($id){

        $getPool = WC_Pool::where('id',$id)->first();
        $delete = $getPool->delete();
         return redirect()->route('adminwc')->with('message','Xóa trận thành công!')->with('status',1);
    }

    public function editPool(Request $request, $id){

        $getPool = WC_Pool::where('id',$id)->first();
        if( $getPool){
            return view('admincp.worldcup.edit',['pool'=>$getPool,'id'=>$id]);
        }else{
            return redirect()->route('adminwc')->with('message','Không tìm thấy trận đấu.')->with('status',2);
        }
        

    }
    public function editPool_submit(Request $request){

        $getPool = WC_Pool::where('id',$request->id)->first();
         if( $getPool){


            $getPool->Team1=$request->team1;
            $getPool->Team2=$request->team2;
            $getPool->pool=$request->pool?$request->pool:'';
            $getPool->status=0;
            $getPool->dayend=$request->dayend;
            $getPool->amount=$request->amount;

            if($request->status==1){

                $logs_pool= WC_Pool_Log::where('id_pool',$request->id)->where('pool','=',$request->pool)->where('status',0)
                ->get();
                foreach ($logs_pool as $key => $value){
                    $user = Account::where('id',$value->userId)->first();
                    if($user){

                       $user->balance= $user->balance+$value->amount + ($value->amount*$request->amount);  


                       $user->save();

                       $up_log = WC_Pool_Log::where('id',$value->id)->first();
                       $up_log->amount_send=$value->amount + ($value->amount*$request->amount);
                       $up_log->status = 1;
                       $up_log->save();

                       return redirect()->route('adminwc')->with('message','Đã trao giả.')->with('status',1); 
                    }
                } 
                $getPool->status=1;
            }

            $getPool->save();

           return redirect()->route('adminwc')->with('message','Cập nhập thành công.')->with('status',1);
        }else{
            return redirect()->route('adminwc')->with('message','Không tìm thấy trận đấu.')->with('status',2);
        }

    }
    public function createpool_submit(Request $request){

        $opts = array(
            'Team1'=>$request->team1,
            'Team2'=>$request->team2,
            'pool'=>'',
            'status'=>0,
            'dayend'=>$request->dayend,
            'amount'=>$request->amount,
        );
        $create = WC_Pool::create($opts);

        
        if( $create){
            return redirect()->route('adminwc')->with('message','Tạo trận thành công')->with('status',1);
        }else{
            return redirect()->back()->with('message','Tạo trận lỗi')->with('status',2);
        }

    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
