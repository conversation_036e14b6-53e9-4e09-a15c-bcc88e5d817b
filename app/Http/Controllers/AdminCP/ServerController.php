<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Http\Requests\AdminCP\ServerRequest;

use App\Server;
use App\FileLoader;
use Illuminate\Filesystem\Filesystem;
use Carbon\Carbon;

class ServerController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('admin.super');
    }	
	
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
		$servers = Server::orderBy('Id', 'desc')->get();
		
        return view('admincp.server', ['servers' => $servers]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
		
		$server = Server::orderBy('Id', 'desc')->first();
		
		if($server !== NULL) {
			$serverid = $server->Id + 1;
		} else {
			$serverid = 1;
		}
		
        return view('admincp.server.create', ['serverid' => $serverid]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(ServerRequest $request)
    {
		Server::create([
			'Id' => $request->id,
			'ServerName' => $request->name,
			'ServerURL' => $request->url,
			'DatabaseName' => $request->database,
			'ServerPort' => $request->port,
			'Status' => $request->status,
			'OnlineNum' => $request->online,
			'StartTime' => Carbon::now()
		]);
		
		
		$servers = Server::orderBy('id', 'desc')->get();
		
		FileLoader::db_config($servers);		
		
		$status = __('admincp.message_server_created');
		
		return redirect()->route('server.index')->with('status', $status);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
		$server = Server::where('Id', $id)->first();
		
        return view('admincp.server.edit', ['server' => $server]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(ServerRequest $request, $id)
    {
        $server = Server::where('Id', $id)->first();
		$server->ServerName = $request->name;
		$server->ServerURL = $request->url;
		$server->DatabaseName = $request->database;
		$server->ServerPort = $request->port;
		$server->Status = $request->status;
		$server->OnlineNum = $request->online;
		$server->MaintainStarTime = $request->startime;
		$server->MaintainTerminalTime = $request->terminaltime;
		$server->MaintainTxt = $request->txt;						
		$server->save();
		
		$servers = Server::orderBy('id', 'desc')->get();
		
		FileLoader::db_config($servers);		
		
		$status = __('admincp.message_server_edited', ['id' => $server->Id]);
		
		return redirect()->route('server.index')->with('status', $status);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        Server::where('Id', $id)->delete();
		
		$servers = Server::orderBy('id', 'desc')->get();
		
		FileLoader::db_config($servers);		
		
		$status = __('admincp.message_server_deleted', ['id' => $id]);
		
		return redirect()->route('server.index')->with('status', $status);
    }
}
