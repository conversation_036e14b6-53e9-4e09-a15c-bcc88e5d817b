<?php

namespace App\Http\Controllers\AdminCP;

use App\t_rewardCuston_log;
use Illuminate\Http\Request;

class TRewardCustonLogController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\t_rewardCuston_log  $t_rewardCuston_log
     * @return \Illuminate\Http\Response
     */
    public function show(t_rewardCuston_log $t_rewardCuston_log)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\t_rewardCuston_log  $t_rewardCuston_log
     * @return \Illuminate\Http\Response
     */
    public function edit(t_rewardCuston_log $t_rewardCuston_log)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\t_rewardCuston_log  $t_rewardCuston_log
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, t_rewardCuston_log $t_rewardCuston_log)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\t_rewardCuston_log  $t_rewardCuston_log
     * @return \Illuminate\Http\Response
     */
    public function destroy(t_rewardCuston_log $t_rewardCuston_log)
    {
        //
    }
}
