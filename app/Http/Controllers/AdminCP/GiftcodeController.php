<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Http\Requests\AdminCP\GiftcodeRequest;

use App\Giftcode;
use App\Giftcode_Use;
use App\Helper;
use App\Log;
use DB;
use Auth;
use Response;
use Illuminate\Support\Str;

class GiftcodeController extends Controller
{
	/**
	 * Create a new controller instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		$this->middleware('admin');
	}

	/**
	 * Display a listing of the resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index()
	{

		$giftcode = Giftcode::orderBy('id', 'desc')->paginate(20);

		$goods_parse = Helper::getGoods();

		return view('admincp.giftcode', ['giftcode' => $giftcode, 'goods_parse' => $goods_parse]);
	}

	public function createGiftUseIndex()
	{
		$list = Giftcode_Use::get();
		return view('admincp.giftcode.giftuserIndex', ['gifts' => $list]);
	}
	public function createGiftUse()
	{
		return view('admincp.giftcode.giftuser', ['item' => null]);
	}

	public function createGiftUse_update($id)
	{
		$item = Giftcode_Use::where('id', $id)->first();
		return view('admincp.giftcode.giftuser', ['id' => $id, 'item' => $item]);
	}

	public function createGiftUse_delete($id)
	{
		$item = Giftcode_Use::destroy($id);
		return redirect()->route('createGiftUseIndex')->with('message', 'Xóa thành công!')->with('status', 1);
	}

	public function createGiftUse_post(Request $request)
	{
		if ($request->id) {

			$item = Giftcode_Use::where('id', $request->id)->first();
			$item->content = trim($request->content);
			$item->save();
			return redirect()->route('createGiftUseIndex')->with('message', 'Cập nhập thành công!')->with('status', 1);
		} else {

			Giftcode_Use::create([
				'content' => trim($request->content)
			]);
			return redirect()->route('createGiftUseIndex')->with('message', 'Thêm thành công!')->with('status', 1);
		}
	}

	/**
	 * Show the form for creating a new resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function create(Request $request)
	{
		$type = $request->input('type');

		return view('admincp.giftcode.create', ['type' => $type]);
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(GiftcodeRequest $request)
	{
		$multiple = $request->multiple;
		$limit = $request->limit;

		$code = [];

		if ($multiple == 1) {

			$limit = 1;

			$number = $request->number;

			if ($number < 2) {
				$number = 2;
			}

			for ($i = 0; $i < $number; $i++) {
				$str = Str::random(8);
				$code[] = Helper::str_upper($str);
			}
		} else {

			$multiple = 0;

			if ($request->has('code')) {
				$code[] = Helper::str_upper($request->code);
			} else {
				$str = Str::random(8);
				$code[] = Helper::str_upper($str);
			}
		}

		$items = explode(PHP_EOL, $request->items);

		foreach ($items as $item) {
			$code_item = explode(',', $item);

			if (count($code_item) != 7) {
				$status = __('admincp.message_code_error');

				return redirect()->back()
					->withInput($request->only('items'))
					->withErrors(['items' => $status]);
			}
		}

		Giftcode::create([
			'type' => $request->type,
			'multiple' => $multiple,
			'code' => $code,
			'items' => $items,
			'content' => $request->content,
			'limit' => $limit,
			'accounts' => trim($request->accounts),
			'period' => $request->period,
			'zoneid' => $request->zoneid,
		]);

		foreach ($code as $c) {
			$data = [
				'cc' => '',
				'code' => $c,
				'mail' => intval($request->type),
				'count' => 0,
				'maxcount' => intval($limit),
				'userid' => trim($request->accounts),
				'itemlist' => implode(',', $items),
			];
			DB::connection($request->zoneid)->table('z_giftcode')->insert($data);
		}


		Log::create([
			'uid' => Auth::user()->UserID,
			'type' => 11,
			'content' => [
				'giftcode' => $code,
				'zoneid' => $request->zoneid,
				'items' => $items,
				'message' => $request->content
			]
		]);

		$status = __('admincp.message_giftcode_created');

		return redirect()->route('gift_code.index')->with('status', $status);
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function show($id)
	{

		$giftcode = Giftcode::where('id', $id)->first();

		$content = collect($giftcode->code)->implode(PHP_EOL);

		$filename = $giftcode->content . '.txt';

		$headers = array(
			'Content-Type' => 'plain/txt',
			'Content-Disposition' => sprintf('attachment; filename="%s"', $filename),
			'Content-Length' => strlen($content),
		);

		return Response::make($content, 200, $headers);
	}

	/**
	 * Show the form for editing the specified resource.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function edit($id)
	{
		$giftcode = Giftcode::where('id', $id)->first();

		return view('admincp.giftcode.edit', ['giftcode' => $giftcode]);
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function update(GiftcodeRequest $request, $id)
	{
		$giftcode = Giftcode::where('id', $id)->first();
		$giftcode->type = $request->type;

		if (Auth::user()->groupid == 1) {
			$giftcode->items = explode(PHP_EOL, $request->items);
		}

		$giftcode->content = $request->content;
		// if($giftcode->multiple == 1) {
		// $giftcode->limit = 1;
		// } else {
		$giftcode->limit = $request->limit;
		// }
		$giftcode->period = $request->period;
		$giftcode->zoneid = $request->zoneid;
		$giftcode->accounts = trim($request->accounts);
		$giftcode->save();

		$data = [
			'mail' => intval($giftcode->type),
			'maxcount' => intval($request->limit),
			'userid' => trim($request->accounts),
		];
		if (Auth::user()->groupid == 1) {
			$data['itemlist'] = implode(',', $giftcode->items);
		}
		if ($giftcode->multiple == 1) {
			foreach ($giftcode->code as $c) {
				DB::connection($request->zoneid)->table('z_giftcode')->where('code', $c)->update($data);
			}
		} else {
			DB::connection($request->zoneid)->table('z_giftcode')->where('code', $giftcode->code)->update($data);
		}

		$status = __('admincp.message_giftcode_edited', ['id' => $id]);

		return redirect()->route('gift_code.index')->with('status', $status);
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function destroy($id)
	{
		if (Auth::user()->groupid == 1) {
			Giftcode::where('id', $id)->delete();

			$status = __('admincp.message_giftcode_deleted', ['id' => $id]);

			return redirect()->route('gift_code.index')->with('status', $status);
		}
	}
}
