<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Server;
use App\Account;
use App\Recharge;
use App\Log;

use Carbon\Carbon;

use Cache;
use DB;
use Config;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('admin');
    }
	
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
		$servers = Server::orderBy('Id', 'desc')->get();
		
		$account = [
			'today' => Account::whereDate('created_at', Carbon::now()->toDateString())->count(),
			'yesterday' => Account::whereDate('created_at', Carbon::yesterday()->toDateString())->count(),
			'month' => Account::whereMonth('created_at', Carbon::now()->month)->whereYear('created_at', Carbon::now()->year)->count(),
			'total' => Account::count()
		];
		
		
		$character = Cache::remember('role.total', 5, function () use($servers) {
			
			$platform_name = Config::get('mu.platform.name');
			
			$today = 0;
			$yesterday = 0;
			$month = 0;
			$total = 0;
			
			foreach($servers as $server) {
				$today += DB::connection($server->Id)->table('t_roles')->where('userid', 'like', $platform_name .'%')->whereDate('regtime', Carbon::now()->toDateString())->count();	
				$yesterday += DB::connection($server->Id)->table('t_roles')->where('userid', 'like', $platform_name .'%')->whereDate('regtime', Carbon::yesterday()->toDateString())->count();	
				$month += DB::connection($server->Id)->table('t_roles')->where('userid', 'like', $platform_name .'%')->whereMonth('regtime', Carbon::now()->month)->whereYear('regtime', Carbon::now()->year)->count();	
				$total += DB::connection($server->Id)->table('t_roles')->where('userid', 'like', $platform_name .'%')->count();
			}
		
			$character = [
				'today' => $today,
				'yesterday' => $yesterday,
				'month' => $month,
				'total' => $total
			];
		
			return $character;	
		});

		$recharge = [
			'today' => Recharge::where('status', 1)->whereDate('created_at', Carbon::now()->toDateString())->sum('amount'),
			'yesterday' => Recharge::where('status', 1)->whereDate('created_at', Carbon::yesterday()->toDateString())->sum('amount'),
			'month' => Recharge::where('status', 1)->whereMonth('created_at', Carbon::now()->month)->whereYear('created_at', Carbon::now()->year)->sum('amount'),
			'last_month' => Recharge::where('status', 1)->whereMonth('created_at', Carbon::now()->subMonth()->month)->whereYear('created_at', Carbon::now()->subMonth()->year)->sum('amount')
		];
		
		if($account['yesterday'] > 0) {
			$percent['account'] = ($account['today'] / $account['yesterday']) * 100;
		} else {
			$percent['account'] = 100;
		}
		
		if($character['yesterday'] > 0) {
			$percent['character'] = ($character['today'] / $character['yesterday']) * 100;
		} else {
			$percent['character'] = 100;
		}
		
		if($recharge['yesterday'] > 0) {
			$percent['recharge'] = ($recharge['today'] / $recharge['yesterday']) * 100;
		} else {
			$percent['recharge'] = 100;
		}
		
		$logs = Log::join('zt_account', 'zt_account.UserID', '=', 't_log.uid')
						->select('t_log.*', 'zt_account.UserName')
						->orderBy('t_log.id', 'desc')
						->take(5)
						->get();
		
        return view('admincp.dashboard', ['servers' => $servers,
										  'account' => $account,
										  'character' => $character,
										  'recharge' => $recharge,
										  'percent' => $percent,
										  'logs' => $logs]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
