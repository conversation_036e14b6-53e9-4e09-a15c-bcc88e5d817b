<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Http\Requests\AdminCP\CharacterRequest;

use App\Role;
use App\Account;
use App\Server;
use App\Helper;
use App\Log;
use App\GMMsg;

use Carbon\Carbon;
use Auth;
use DB;
use Config;

class CharacterController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('admin');
    }	
	
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
		$keyword = NULL;
		
		$platform_name = Config::get('mu.platform.name');
		
		if($request->has('keyword')) {
			
			$servers = Server::orderBy('Id', 'desc')->get();
			
			$keyword = $request->input('keyword');
			
			$characters = [];
			foreach($servers as $server) {
				$characters[] = DB::connection($server->Id)->table('t_roles')->select('t_roles.userid', 't_roles.rid', 't_roles.rname','t_roles.changelifecount', 't_roles.level', 't_roles.zoneid', 't_roles.logofftime', 't_roles.isdel')->where('t_roles.rname', 'like', '%' . $keyword .'%')->where('t_roles.userid', 'like', $platform_name .'%')->orderBy('t_roles.changelifecount', 'desc')->get();
			}
			
			$character = collect($characters);
		
			$role = $character->flatten(1);
		
			$roles = $role->values()->all();			
		} else {
			if ($request->has('server')) {
				$server = Server::where('Id', $request->input('server'))->first();
			} else {
				$server = Server::orderBy('Id', 'desc')->first();
			}
		
			$roles = DB::connection($server->Id)->table('t_roles')->select('userid', 'rid', 'rname', 'level','changelifecount', 'zoneid', 'logofftime', 'isdel')->where('userid', 'like', $platform_name .'%')->orderBy('changelifecount', 'desc')->paginate(20);
			$tmp_roles= unserialize(serialize($roles));
			$tmp_roles->transform(function ($item, $key) {
				$item->userid = substr($item->userid, 2);
				return $item;
			});
			

			$accounts = Account::whereIn('UserID', $tmp_roles->pluck('userid'))->get()->pluck('UserName','UserID')->toArray();
			// dd($accounts);
			
			$roles->transform(function ($item, $key)use($accounts) {
				// dd($item->userid);
				$item->rname = $item->rname . " (" .$accounts[substr($item->userid, 2)] . ")";
				return $item;
			});
		}
		
        return view('admincp.character', ['server' => $request->input('server'), 'roles' => $roles, 'keyword' => $keyword]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
		if ($request->has('server')) {
			$server = Server::where('Id', $request->input('server'))->first();
		} else {
			$server = Server::orderBy('Id', 'desc')->first();
		}
		
		$role = DB::connection($server->Id)->table('t_roles')->where('rid', $id)->first();
		
		$goods = DB::connection($server->Id)->table('t_goods')->where('rid', $id)->get();
		
		$goods_parse = Helper::getGoods();

        return view('admincp.character.show', ['role' => $role, 'goods' => $goods, 'goods_parse' => $goods_parse]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(CharacterRequest $request, $id)
    {
		$user = Auth::user();
		
		$server = Server::where('Id', $request->zoneid)->first();
					
		$role = DB::connection($server->Id)->table('t_roles')->where('rid', $id)->first();		
		
		if($request->has('ban')) {
			
			GMMsg::create(['msg' => '-ban ' . $role->rname . ' 1']);
		
			$content = [
				'rid' => $role->rid,
				'zoneid' => $role->zoneid,
				'reason' => $request->reason
			];		
		
			Log::create([
				'uid' => $user->UserID,
				'type' => 8,
				'content' => $content
			]);
			
			$status = __('admincp.message_char_ban', ['char' => $role->rname]);			
			
			return redirect()->back()->with('status', $status);
			
		} elseif($request->has('unban')) {
			
			GMMsg::create(['msg' => '-unban ' . $role->rname . ' 1']);
		
			$content = [
				'rid' => $role->rid,
				'zoneid' => $role->zoneid,
				'reason' => $request->reason
			];		
		
			Log::create([
				'uid' => $user->UserID,
				'type' => 9,
				'content' => $content
			]);
			
			$status = __('admincp.message_char_unban', ['char' => $role->rname]);			
			
			return redirect()->back()->with('status', $status);			
			
		} elseif($user->groupid == 1 && $request->has('add_item')) {
			
			$mailid = DB::connection($server->Id)->table('t_mail')->insertGetId([
				'senderrid' => 0,
				'senderrname' => 'GM',
				'sendtime' => Carbon::now(),
				'receiverrid' => $role->rid,
				'reveiverrname' => $role->rname,
				'subject' => __('admincp.mail_item_subject', ['content' => $request->content]),
				'content' => __('admincp.mail_item_content', ['char' => $role->rname, 'content' => $request->content])
			]);
			
			$code_item = explode(PHP_EOL, $request->code);

			$items = [];
			foreach($code_item as $item) {
			
				$goods = explode(',', $item);
			
				$items[] = [
					'mailid' => $mailid,
					'goodsid' => $goods[0],
					'gcount' => $goods[1],
					'binding' => $goods[2],
					'forge_level' => $goods[3],
					'appendproplev' => $goods[4],
					'lucky' => $goods[5],
					'excellenceinfo' => $goods[6],
    			];	
			}
		
			DB::connection($server->Id)->table('t_mailgoods')->insert($items);
			
			Log::create([
				'uid' => $user->UserID,
				'type' => 7,
				'content' => [
					'rid' => $role->rid,
					'zoneid' => $role->zoneid,
					'items' => $code_item,
					'message' => $request->content
				]
			]);
			
			$goods_parse = Helper::getGoods();
			
			$goods = Helper::getNameGoods($goods_parse, $code_item);
			
			$items = '';
			foreach($goods as $good) {
				$items .= '<li>'  . $good['name'] . '</li>';
			}
			
			$status = __('admincp.message_item_success', ['char' => $role->rname, 'items' => $items]);			
			
			return redirect()->back()->with('status', $status);	
					
		}
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
