<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Account;
use App\Recharge;
use App\Helper;
use Config;

use Carbon\Carbon;

class RechargeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('admin');
    }	
	
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
		$keyword = NULL;
		
		$filter = NULL;
		
		if($request->has('filter')) {
			
			$filter = [
				'type' => $request->input('type'),
				'amount' => $request->input('amount'),
				'status' => $request->input('status'),
				'server' => $request->input('server'),
				'date' => $request->input('date')
			];
			
			$recharge = [];
			
			if($request->has('type')) {
				$recharge['t_recharge.type'] = $filter['type'];
			}
			
			if($request->has('amount')) {
				$recharge['t_recharge.amount'] = $filter['amount'];
			}
			
			if($request->has('status')) {
				$recharge['t_recharge.status'] = $filter['status'];
			}
			
			if($request->has('server')) {
				$recharge['t_recharge.zoneid'] = $filter['server'];
			}
			
			if($request->has('date')) {
				
				switch($filter['date']) {
					case 1:
						$date_start = Carbon::today()->toDateString();
						$date_end = Carbon::tomorrow()->toDateString();
					break;
					case 2:
						$date_start = Carbon::yesterday()->toDateString();
						$date_end = Carbon::today()->toDateString();
					break;
					case 3:
						$date_start = Carbon::now()->startOfWeek()->toDateString();
						$date_end = Carbon::now()->addWeek()->startOfWeek()->toDateString();
					break;
					case 4:
						$date_start = Carbon::now()->subWeek()->startOfWeek()->toDateString();
						$date_end = Carbon::now()->startOfWeek()->toDateString();
					break;
					case 5:
						$date_start = Carbon::now()->startOfMonth()->toDateString();
						$date_end = Carbon::now()->addMonth()->startOfMonth()->toDateString();
					break;
					case 6:
						$date_start = Carbon::now()->subMonth()->startOfMonth()->toDateString();
						$date_end = Carbon::now()->startOfMonth()->toDateString();
					break;																							
				}
				
				if($request->has('type') || $request->has('amount') || $request->has('status') || $request->has('server')) {
					$recharges = Recharge::join('zt_serverdata', 't_recharge.zoneid', '=', 'zt_serverdata.Id')
							->select('t_recharge.*', 'zt_serverdata.ServerName as server')
							->whereDate('t_recharge.created_at', '>=', $date_start)
							->whereDate('t_recharge.created_at', '<', $date_end)
							->where($recharge)
							->orderBy('t_recharge.id', 'desc')->paginate(20);
				} else {
					$recharges = Recharge::join('zt_serverdata', 't_recharge.zoneid', '=', 'zt_serverdata.Id')
							->select('t_recharge.*', 'zt_serverdata.ServerName as server')
							->whereDate('t_recharge.created_at', '>=', $date_start)
							->whereDate('t_recharge.created_at', '<', $date_end)
							->orderBy('t_recharge.id', 'desc')->paginate(20);						
				}
			} else {
			
				$recharges = Recharge::join('zt_serverdata', 't_recharge.zoneid', '=', 'zt_serverdata.Id')
								->select('t_recharge.*', 'zt_serverdata.ServerName as server')
								->where($recharge)
								->orderBy('t_recharge.id', 'desc')->paginate(20);	
			}
		}
		elseif($request->has('keyword')) {
			$keyword = $request->input('keyword');
			
			$recharges = Recharge::join('zt_serverdata', 't_recharge.zoneid', '=', 'zt_serverdata.Id')
								->join('zt_account', 't_recharge.uid', '=', 'zt_account.id')
								->select('t_recharge.*', 'zt_serverdata.ServerName as server')
								->orderBy('t_recharge.id', 'desc')
								->where('zt_account.username', 'like', '%' . $keyword . '%')
								->orWhere('t_recharge.serial', 'like', '%' . $keyword . '%')
								->orWhere('t_recharge.code', 'like', '%' . $keyword . '%')
								->get();
		} else {
			$recharges = Recharge::join('zt_serverdata', 't_recharge.zoneid', '=', 'zt_serverdata.Id')
								->select('t_recharge.*', 'zt_serverdata.ServerName as server')
								->orderBy('t_recharge.id', 'desc')->paginate(20);			
		}
		
        return view('admincp.recharge', ['recharges' => $recharges, 'keyword' => $keyword, 'filter' => $filter]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id,Request $request)
    {	
    	$xx  = $request->query('no');
    	$no= isset($xx)?$xx:1;
		$recharge = Recharge::where('id', $id)->first();
        return view('admincp.recharge.edit', ['recharge' => $recharge,'no'=>$no]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
	

		$recharge = Recharge::where('id', $id)->first();
		$recharge->amount = $request->amount;
		$recharge->status = $request->status;
		$recharge->save();
		
		if($request->status == 1 && $request->updatev==1 ) {
			
			$dt = Carbon::now();
			
			$exchange = Config::get('mu.recharge.exchange');
			$promotion = Config::get('mu.recharge.promotion');
			$promotion_date = Config::get('mu.recharge.date');
					
			$promotion_gate = Config::get('recharge.bonus.gate');
				
			$balance = $request->amount * $exchange;
					
			$bonus = 0;
			if($promotion > 0 && $dt >= $promotion_date[0] && $dt <= $promotion_date[1]) {
				$bonus = $balance * $promotion / 100;
			}
			
			$bonus_gate = 0;
			if($recharge->type == 4) {
				$bonus_gate = $balance * $promotion_gate / 100;
			}
			$account = Account::where('UserID', $recharge->uid)->first();				
			$account->Money = $account->Money + $balance + $bonus + $bonus_gate;
			$account->save();

			// add dau tien trong ngay 
			
			Helper::mobiCarDaily($recharge->uid,$request->amount,$recharge->code);  


		}

		$message = __('usercp.message_update_info');
			
		return redirect()->route('recharge_info.index')->with('status', $message);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
