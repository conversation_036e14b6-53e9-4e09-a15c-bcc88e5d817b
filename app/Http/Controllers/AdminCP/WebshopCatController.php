<?php

namespace App\Http\Controllers\AdminCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\WebshopCatelog;
use Config;


class WebshopCatController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        $list_Cat = WebshopCatelog::get();
        return view('admincp.webshop.cat',['lists'=>$list_Cat ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
        return view('admincp.webshop.create_cat');
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
        if(empty($request->name)){
           return redirect()->route('webshop_Cat_create')->withError('name','Thêm tên cat')->with('status',2); 
        }
        if(empty($request->slug)){
           return redirect()->route('webshop_Cat_create')->withError('name','Thêm slug')->with('status',2); 
        }
        $create = WebshopCatelog::create([
            'name'=>$request->name,
            'slug'=>$request->slug,
        ]);
        
        return redirect()->route('webshop_Cat_index')->with('message',__('admincp.webshop.cat.add.success',['cat'=>$request->name]))->with('status',1);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
        $cat = WebshopCatelog::where('id',$id)->first();
        return view('admincp.webshop.cat_edit',['cat'=>$cat]);
    }

    public function edit_post(Request $request){
        $id = $request->id;
        $cat = WebshopCatelog::where('id', $id )->first();      
        if(!$cat){
            return redirect()->route('webshop_Cat_index')->with('message',__('admincp.webshop.cat.edit.failed',['cat'=> $request->name]))->with('status',2);
        }
        $cat->name = $request->name;
        $cat->slug = $request->slug;
        $cat->save();

        return redirect()->route('webshop_Cat_index')->with('message',__('admincp.webshop.cat.edit.success',['cat'=> $request->name]))->with('status',1);


    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
        $selected = WebshopCatelog::where('id',$id)->first();
        $delete = WebshopCatelog::where('id',$id)->delete(['id'=>$id]);
        if($delete){
            return redirect()->route('webshop_Cat_index')->with('message',__('admincp.webshop.cat.del.success',['cat'=>$selected->name]))->with('status',1);
        }else{
            return redirect()->route('webshop_Cat_index')->with('message',__('admincp.webshop.cat.del.failed',['cat'=>$selected->name]))->with('status',2);
        }
    }
}
