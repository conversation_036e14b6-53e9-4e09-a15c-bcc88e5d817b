<?php

namespace App\Http\Controllers\UserCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Underscore\Types\Arrays;
use App\Helper;
use App\Reward;
use App\Recharge;
use App\Payment;
use App\Server;
use Carbon\Carbon;

use Auth;
use DB;
use Config;

class RewardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
		
        $this->middleware('auth');
    }
	
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index( Request $request)
    {
		
		
		
		$method = Config::get('recharge.method');
		$goods_parse = Helper::getGoods();
		$account = Auth::user();
		$server = Server::where('Id', $account->Role)->first();

		$reward_with_server =  config('rewards.reward_'.$server->Id);
		if(!$reward_with_server){
			$reward_with_server =  config('rewards.reward_def');
		}
		
		
		$reward_date = $reward_with_server['date'];

		$list_reset_to_day = DB::connection($server->Id)->table('t_roles')->where('userid', $account->UserID)->orderBy('lasttime', 'desc')->first();
		
		if($server) {
			$role = DB::connection($server->Id)->table('t_roles')->where('userid', $account->UserID)->orderBy('lasttime', 'desc')->first();
		
			if($role) {
				$rname = $role->rname;
			} else {
				$rname = __('usercp.message_switch_char');
			}
		} else {
			$rname = __('usercp.message_switch_server');
		}
		


		$reward_from = Carbon::parse($reward_date[0]);
		$reward_to = Carbon::parse($reward_date[1]);
		
		$reward = Reward::where('type', 1)->where('uid', $account->UserID)->whereDate('created_at', '>=', $reward_from->toDateString())->whereDate('created_at', '<=', $reward_to->toDateString())->orderBy('id', 'desc')->first();

		$recharge = Recharge::where('uid', $account->UserID)->where('status', 1)
		->whereDate('created_at', '>=', $reward_from->toDateString())
		->whereDate('created_at', '<=', $reward_to->toDateString())->sum('amount');
				
		if($method == 3) {
			$payment = Payment::where('uid', $account->UserID)->where('status', 1)->whereDate('created_at', '>=', $reward_from->toDateString())->whereDate('created_at', '<=', $reward_to->toDateString())->sum('amount');
										
			$recharge = $recharge + $payment;
		}		
		

		// if($request->amount){
		// 	if( $recharge < $request->amount ){
		//  	return redirect()->route('reward.index')->with('message', __('usercp.message_reward_invaild'))->with('status', 2);
		//  }
		 
		// }
		
		$reward_from = Helper::getFormatDate($reward_date[0]);
		$reward_to = Helper::getFormatDate($reward_date[1]);
		
        return view('usercp.reward', [	'rname' => $rname,
										'goods_parse' => $goods_parse,
										'recharge' => $recharge,
										'reward' => $reward,
										'reward_from' => $reward_from,
										'reward_to' => $reward_to,
										'reward_with_server'=>$reward_with_server,
										'amount'=>$request->amount
										]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {	

    	if(!Helper::isReward()){
    		return redirect()->back()->with('message', __('usercp.reward.end'))->with('status', 2);
    	}
    	
    	$rid =$request->select_rid;
    	
		$account = Auth::user();
		$server = Server::where('Id', $account->Role)->first();

		$reward_with_server =  config('rewards.reward_'.$server->Id);

		if(!$reward_with_server){
			$reward_with_server =  config('rewards.reward_def');
		}
		$role = DB::connection($server->Id)->table('t_roles')->where('rid',$rid)->orderBy('lasttime', 'desc')->first();
			if(!$role){
				return redirect()->back()->with('message', __('usercp.message_unknown_char'))->with('status', 2);
			}

		
		$dt = Carbon::now();
		
		$awards = $reward_with_server['recharge'];
		$award = $awards[$id];
		$i = $id;


		//foreach($awards as $i => $award) {
			//if($id == $i) {
				
				foreach($award['custom'] as $v => $customs) {				
					if(count($request->input('item' . '_' . $v)) < $customs['number']) {
						return redirect()->back()->with('message', __('usercp.message_reward_min', ['number' => $customs['number'], 'item' => Helper::getCustomReward($v)]))->with('status', 2);
					} else if(count($request->input('item' . '_' . $v)) > $customs['number']) {
						return redirect()->back()->with('message', __('usercp.message_reward_max', ['number' => $customs['number'], 'item' => Helper::getCustomReward($v)]))->with('status', 2);
					}
				}

				$reward_date = $reward_with_server['date'];
			
				$method = Config::get('recharge.method');
		
				$reward_from = Carbon::parse($reward_date[0]);
				$reward_to = Carbon::parse($reward_date[1]);	


				
				$recharge = Recharge::where('uid', $account->UserID)->where('status', 1)->whereDate('created_at', '>=', $reward_from->toDateString())->whereDate('created_at', '<=', $reward_to->toDateString())->sum('amount');
				
				if($method == 3) {
					$payment = Payment::where('uid', $account->UserID)->where('status', 1)->whereDate('created_at', '>=', $reward_from->toDateString())->whereDate('created_at', '<=', $reward_to->toDateString())->sum('amount');
										
					$recharge = $recharge + $payment;
				}
					
				if($recharge >= $i) {
					$reward = Reward::where('type', 1)->where('amount', $id)->where('uid', $account->UserID)->whereDate('created_at', '>=', $reward_from->toDateString())->whereDate('created_at', '<=', $reward_to->toDateString())->exists();
				
					if($reward === FALSE) {
						
						$mailid = DB::connection($server->Id)->table('t_mail')->insertGetId([
							'senderrid' => 0,
							'senderrname' => 'GM',
							'sendtime' => $dt,
							'receiverrid' => $role->rid,
							'reveiverrname' => $role->rname,
							'subject' => __('usercp.mail_reward_subject'),
							'content' => __('usercp.mail_reward_content', ['char' => $role->rname, 'amount' => Helper::numberFormat($i)])
						]);
		
						$items = [];
						foreach($award['default'] as $item) {
			
							$goods = explode(',', $item);
			
							$items[] = [
								'mailid' => $mailid,
								'goodsid' => $goods[0],
								'gcount' => $goods[1],
								'binding' => $goods[2],
								'forge_level' => $goods[3],
								'appendproplev' => $goods[4],
								'lucky' => $goods[5],
								'excellenceinfo' => $goods[6],
							];	
						}		
		
						DB::connection($server->Id)->table('t_mailgoods')->insert($items);

						$custom_items = [];
						foreach($award['custom'] as $v => $customs) {				
							
							$item_reward = $request->input('item' . '_' . $v);
								
							foreach($customs['item'] as $k => $custom) {
								
								if(count($item_reward) > 0 && in_array($k, $item_reward) !== FALSE) {
								
									$goods = explode(',', $custom);
			
									$custom_items[] = [
										'mailid' => $mailid,
										'goodsid' => $goods[0],
										'gcount' => $goods[1],
										'binding' => $goods[2],
										'forge_level' => $goods[3],
										'appendproplev' => $goods[4],
										'lucky' => $goods[5],
										'excellenceinfo' => $goods[6],
									];
								}
							}
						}
							
						DB::connection($server->Id)->table('t_mailgoods')->insert($custom_items);
							
						Reward::create([
							'uid' => $account->UserID,
							'type' => 1,
							'amount' => $i,
							'rid'=>$rid,
							'zoneid'=>$account->Role,
							'item' => $custom_items,
						]);
							
						return redirect()->back()->with('message', __('usercp.message_reward_success'))->with('status', 1);
						
					} else {
						return redirect()->back()->with('message', __('usercp.message_reward_error'))->with('status', 2);
					}
					
				} else {
					return redirect()->back()->with('message', __('usercp.message_reward_invaild'))->with('status', 2);
				}
			//}
		//}
		
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
