<?php

namespace App\Http\Controllers\UserCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Server;
use App\Webshop;
use App\Webshop_Log;
use Auth;
use DB;

class WebshopController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }	
	
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

		$account = Auth::user();
		$server = Server::where('Id', $account->Role)->first();
        $webshop = Webshop::orderBy('id', 'desc');

		if($server) {
			$role = DB::connection($server->Id)->table('t_roles')->where('userid', $account->UserID)->orderBy('lasttime', 'desc')->first();
		
			if($role) {
				$rname = $role->rname;
			} else {
				$rname = __('usercp.message_switch_char');
			}
		} else {
			$rname = __('usercp.message_switch_server');
		}
        if($request->catId){
           $webshop->where('idCat',$request->catId) ;
        }

		$webshop = $webshop->paginate(12);
		
        return view('usercp.webshop', ['rname' => $rname, 'webshop' => $webshop ,'catId'=>$request->catId]);

    }
	public  function logs(){

            $account = Auth::user();

            $logs = Webshop_Log::join('zt_account', 'zt_account.UserID', '=', 't_webshop_log.uid')->join('t_webshop','t_webshop.item','=','t_webshop_log.item')
                            ->select('t_webshop_log.*','t_webshop.price','t_webshop.name', 'zt_account.UserName')
                            ->where('t_webshop_log.uid','=',$account->UserID)
                            ->orderBy('t_webshop_log.updated_at', 'desc')->paginate(10);
            return view('usercp.webshoplog',['logs'=>$logs]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(WebshopRequest $request)
    {
		//	
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $account = Auth::user();
		
		$server = Server::where('Id', $account->Role)->first();
		
		if($server) {
			$role = DB::connection($server->Id)->table('t_roles')->where('userid', $account->UserID)->orderBy('lasttime', 'desc')->first();
		
			if($role) {
				$rname = $role->rname;
			} else {
				$rname = __('usercp.message_switch_char');
			}
		} else {
			$rname = __('usercp.message_switch_server');
		}
		$webshop = Webshop::where('id',$id)->get();
		
        return view('usercp.webshop.show', ['rname' => $rname, 'webshop' => $webshop]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
