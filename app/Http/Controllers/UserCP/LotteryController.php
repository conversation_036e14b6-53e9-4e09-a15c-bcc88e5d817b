<?php

namespace App\Http\Controllers\UserCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Account;
use App\Lottery;
use App\Helper;

use Carbon\Carbon;
use Config;
use Auth;
use Cache;

class LotteryController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }	
	
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
		
		$auth = Auth::user();
		
		$update_time = Config::get('lottery.update_time');
		
		if(Carbon::now()->hour >= $update_time) {
			$xoso_data = Cache::rememberForever('lottery_data' . Carbon::now()->day . Carbon::now()->month, function() {
				
				$reward_de = Config::get('lottery.reward.de');
				$reward_lo = Config::get('lottery.reward.lo');
				$reward_xien2 = Config::get('lottery.reward.xien2');
				$reward_xien3 = Config::get('lottery.reward.xien3');
				$reward_xien4 = Config::get('lottery.reward.xien4');
				$reward_lotruot = Config::get('lottery.reward.lotruot');				
				
				$xoso = Helper::xoso_data();
				
				if($xoso['date'][0] == Carbon::now()->day && $xoso['date'][1] == Carbon::now()->month) {
				
					$lottery_arr = [];
					foreach($xoso['result'] as $i => $data_lotteries) {
						foreach ($data_lotteries as $data_lottery) {
							$lottery_arr[] = substr($data_lottery, -2);
						}
					}
		
					$lode = Lottery::where('status', 0)->whereDate('created_at', Carbon::now()->toDateString())->get();
		
					foreach($lode as $lottery) {

						$lottery_number = $lottery->code;
			
						$lottery_number_count = array_intersect($lottery_number, $lottery_arr);
						
						$lotter_lode = $lottery_number[0];
			
						// Check De
						if($lottery->type == 1 && $lotter_lode == $lottery_arr[0]) {
							$lottery_balance = $lottery->amount * $reward_de;
							$lottery_status = 1;
							$lottery_reward = 1;
						}
						// Check Lo
						elseif($lottery->type == 2 && in_array($lotter_lode, $lottery_arr)) {
							$lottery_counts = array_count_values($lottery_arr);
							$lottery_reward = $lottery_counts[$lotter_lode];
							$lottery_balance = $lottery->amount * $reward_lo * $lottery_reward;
							$lottery_status = 1;
						}				
						elseif ($lottery->type == 3 && count($lottery_number_count) == 2) {
							$lottery_balance = $lottery->amount * $reward_xien2;
							$lottery_status = 1;
							$lottery_reward = 1;				
						}
						elseif ($lottery->type == 4 && count($lottery_number_count) == 3) {
							$lottery_balance = $lottery->amount * $reward_xien3;
							$lottery_status = 1;
							$lottery_reward = 1;				
						}
						elseif ($lottery->type == 5 && count($lottery_number_count) == 4) {
							$lottery_balance = $lottery->amount * $reward_xien4;
							$lottery_status = 1;
							$lottery_reward = 1;				
						}
						elseif ($lottery->type == 6 && empty($lottery_number_count)) {
							$lottery_balance = $lottery->amount * $reward_lotruot;
							$lottery_status = 1;
							$lottery_reward = 1;				
						}
						else {
							$lottery_balance = 0;
							$lottery_status = 2;
							$lottery_reward = 0;				
						}

						Lottery::where('id', $lottery->id)
							->update([
								'balance' => $lottery_balance,
								'status' => $lottery_status,
								'reward' => $lottery_reward
							]);
							
						$account = Account::where('id', $lottery->uid)->first();
						$account->balance = $account->balance + $lottery_balance;
						$account->save();
					}
				}

				return $xoso;
			});
		} else {
			$xoso_data = Cache::rememberForever('lottery_data' . Carbon::now()->subDay()->day . Carbon::now()->month, function() {
				return Helper::xoso_data();
			});	
		}			

		$xoso = [
			'date' => $xoso_data['date'],
			'result' => $xoso_data['result']
		];
		
		$record_time = $update_time - 2;
		
		$lotteries = Lottery::where('uid', $auth->id)->whereDate('created_at', '>=', Carbon::now()->subDay()->toDateString())->orderBy('id', 'desc')->paginate(10);
		
		return view('usercp.lottery', ['xoso' => $xoso, 'record_time' => $record_time, 'lotteries' => $lotteries]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
