<?php

namespace App\Http\Controllers\UserCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use Auth;
use App\Server;
use App\Helper;


class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
		
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {   
		// dd("home");
		// dd(Auth::user()->Role);
        Helper::setRid();
		// dd(config('chip.open'));
		
        return view('usercp.main');
        
    }
    public function loginIos(){
        
        $Server = Server::orderBy('Id', 'asc')->get();
      
        $account = Auth::user();
        $Role = $account->Role; 
        return view('usercp.playIos',['server'=>$Server,'Role'=>$Role]);
    }
    public function loginIos_selectsv(Request $request){

        $idzone = $request->Role;
        if($request->Role==-1){
                return redirect()->back()
                     ->withErrors(['Role' => __('usercp.message_login_zoneid_failed')]);
        }
        $account = Auth::user();
        $account->Role = $idzone;
        $account->save();
        
        return redirect()->route('userDas');
    }
    public function logout(){
        try{
            Auth::logout();
            return redirect()->route('userDas');
        }catch(\Exception $e){
           echo $e; 
        }
        
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
