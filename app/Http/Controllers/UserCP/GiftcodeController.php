<?php

namespace App\Http\Controllers\UserCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Server;

use Auth;
use DB;
use App\Giftcode;
use App\Giftcode_Log;
use App\Giftcode_Use;
use Carbon\Carbon;


class GiftcodeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }	
	
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

        $dt =Carbon::now();
        //created_at 
		$account = Auth::user();
		
		$server = Server::where('Id', $account->zoneid)->first();

		$gifCodes = Giftcode_Use::first();
        
        


		if($server) {
			$role = DB::connection($server->Id)->table('t_roles')->where('userid', $account->userid)->orderBy('lasttime', 'desc')->first();
		
			if($role) {
				$rname = $role->rname;
			} else {
				$rname = __('usercp.message_switch_char');
			}
		} else {
			$rname = __('usercp.message_switch_server');
		}
		
        return view('usercp.giftcode', ['rname' => $rname,'gifts'=>$gifCodes?$gifCodes:null]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(GiftcodeRequest $request)
    {
		//	
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
