<?php

namespace App\Http\Controllers\UserCP;


use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\History;

use Auth;
use App\Server;
use DB;
use Carbon\Carbon;
use Validator;
use Config;
use Hash;
use App\Account;
use App\Helper;
use Illuminate\Support\Str;


/**
 *
 */
class AuthController extends Controller
{

	public function __construct()
	{
		$this->middleware('guest');
	}
	public function index()
	{
		try {
			$Server = Server::select()->get();
		} catch (\Exception $e) {
			// Fallback if database not available
			$Server = collect([]);
		}
		return view('usercp.login', ['server' => $Server]);
	}

	public function indexIos()
	{
		try {
			$Server = Server::select()->get();
		} catch (\Exception $e) {
			// Fallback if database not available
			$Server = collect([]);
		}
		return view('usercp.loginios', ['server' => $Server]);
	}

	public function register()
	{
		return view('usercp.register');
	}
	public function CreateAcc(Request $request)
	{

		$validator = Validator::make(
			$request->all(),
			[
				'username' => 'required|unique:account.zt_account,username|alpha_num|between:5,20',
				'password' => 'required|alpha_num|between:4,32',
				'email' => 'nullable|email|unique:account.zt_account|max:255',
			]
		);

		$ip = $request->ip();
		if ($validator->fails()) {

			$msg = $validator->errors()->first();
		} else {
			// try{

			$username = Helper::str_lower($request->username);
			$token = Str::random(8);
			$platform_name = Config::get('mu.platform.name');
			$userid = $token;
			$lang = Helper::getLangCode();
			$msg = __('usercp.message_register_success');

			$account = Account::create([
				//'fullName'=>$request->fullName,
				'UserName' => $username,
				// 'phone'=>$request->phone,
				'Password' => md5($request->password),
				'Email' => $request->email,
				'IPAddress' => $request->ip(),
				'Money' => 0,
				'created_at' => Carbon::now(),
				// 'UserID' => $userid,
				//'code_secret'=>Hash::make($request->codesecret),
				// 'hash' => Hash::make(md5($request->password)),
			]);

			if ($account) {
				Auth::login($account, true);
			}

			return redirect()->route('userloginios')->with('message', __('usercp.message_register_success'))->with('status', 1);
			// }catch(\Exception $e){
			// $msg = __('usercp.message_register_failed');
			// }
		}


		return redirect()->back()->withInput(['fullName' => $request->fullName])
			->with('message', $msg)->with('status', 2);
	}
	public function login(Request $request)
	{

		// if($request->zoneid==-1){
		//              return redirect()->back()
		//                	->withErrors(['zoneid' => __('usercp.message_login_zoneid_failed')]);
		//           }
		// $data=[
		// 'username'=>$request->username,
		// 'password'=>md5($request->password),
		// ];
		$data = [
			'UserName' => $request->username,
			'Password' => md5($request->password),
		];

		$user = Account::where($data)->first();
		if ($user) {
			Auth::login($user);
			$account = Auth::user();
			// $rid = $request->zoneid;
			// if(strlen($rid) > 3) {
			// 	$zoneid = substr($rid, -3);
			// 	$account->zoneid = (int)$zoneid;
			// } else {
			// 	$account->zoneid = $rid;
			// }
			$account->save();

			if ($request->has('device') && $request->device == 'ios') {
				return redirect()->route('userloginios');
			} else {
				// dd(Auth::check());
				return redirect()->route('userDas');
			}
		} else {
			return redirect()->back()
				->withInput($request->only('username'))
				->withErrors(['username' => __('usercp.message_login_failed')]);
		}
		/* if(Auth::login($isTrue)){
    			$account = Auth::user();
					// $rid = $request->zoneid;
					// if(strlen($rid) > 3) {
					// 	$zoneid = substr($rid, -3);
					// 	$account->zoneid = (int)$zoneid;
					// } else {
					// 	$account->zoneid = $rid;
					// }
					$account->save();

				if($request->has('device') && $request->device == 'ios') {
					return redirect()->route('userloginios');
				}else{
					return redirect()->route('userDas');
				}
    		}else{

                  	return redirect()->back()
                   	->withInput($request->only('username'))
                  	->withErrors(['username' => __('usercp.message_login_failed')]);

    		} */
	}

	public function forgot()
	{

		return view('usercp.forgot');
	}

	public function forgot_pro(Request $request)
	{

		$user_find  = Account::where('username', trim($request->username))
			->where('email', trim($request->email))
			->where('code_secret', Hash::make($request->codesecret))
			->where('phone', trim($request->phone))->first();
		if ($user_find) {

			$pass = Str::random(10);
			$user_find->hash = Hash::make(md5($pass));
			$user_find->password  = $pass;
			$user_find->save();
			return redirect()->back()->with('message', __('usercp.usercp.btn.forgot.success', ['pass' => $pass]))->with('status', 1);
		} else {
			return redirect()->back()->with('message', __('usercp.usercp.btn.forgot.failed'))->with('status', 2);
		}
	}
}
