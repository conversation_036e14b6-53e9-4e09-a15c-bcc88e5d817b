<?php

namespace App\Http\Controllers\UserCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Server;
use DB;
use App\Helper;
use Auth;
use Hash;

class AccountController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
		
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {   
        $account = Auth::user();
		// dd($account->Password);
		
		
		
        $getChartDel = DB::connection($account->Role)
        ->table('t_roles')
        ->where('userid',"ZT".$account->UserID)
        ->where('isdel',1)->get();
		
		// $hashedPassword = (Hash::make('123456'));
		// dd($hashedPassword);
		// dd(Hash::check('123456', $hashedPassword));

        return view('usercp.account',['chartdel'=>$getChartDel]);
    }
    public function chartreturn(Request $Request){

        $account = Auth::user();
        $balance = $account->Money;
        $rmount= Config('mu.chrreturn');;

        if($balance < $rmount ){
            return redirect()->back()->with('message','Không đủ coin để khổi phục nhân vật.')->with('status',2);
        }else{

            $account->Money = $account->Money-$rmount;
            $account->save();

            $Chartupadate = DB::connection($account->Role)
            ->table('t_roles')
            ->where('rid',$Request->charterreturn)->update(['isdel'=>0]);

            $getChartupadate= DB::connection($account->Role)
            ->table('t_roles')
            ->where('rid',$Request->charterreturn)->first();


            return redirect()->route('account')->with('message','Khôi phục nhân vật '.$getChartupadate->rname.', máy chủ: '.Helper::getServer($account->Role).' thành công.' )->with('status',1);
        }


    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
