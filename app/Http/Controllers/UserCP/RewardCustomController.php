<?php

namespace App\Http\Controllers\UserCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\History;
use App\Helper;
use Auth;

use App\Server;
use DB;
use Carbon\Carbon;
use App\Rank_Log;

use App\t_rewardCuston_rid;
use App\t_rewardCuston;
use App\t_rewardCuston_log;




class RewardCustomController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }	
		
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

   public function isReward($id_rw,$rid,$type_rw,$top,$zoneid){

        $account = Auth::user();
        $t_rewardCuston = t_rewardCuston::where('id','=',$id_rw)->first();
        $t_rewardCuston_log_count = t_rewardCuston_log::where('rw_id','=',$id_rw)
        ->where('uid','=',$account->UserID)
        ->where('rid','=',$rid)
        ->where('type_rw','=',$type_rw)
        ->where('zoneid','=',$zoneid)
        ->count();
        return  $t_rewardCuston_log_count;

   }

    public function getReward($id){

        $t_rewardCuston_rid = t_rewardCuston_rid::select('top','rid','type_rw','rwid','zondid')->where('id',$id)->first();


        $reward_with_server =  config('rewardsC.reward_'.$t_rewardCuston_rid->zondid.'_'.$t_rewardCuston_rid->rwid);

        if(!$reward_with_server){
            $newxxx = t_rewardCuston::where('id',$t_rewardCuston_rid->rwid)->first();
            $reward_with_server =  config('rewardsC.reward_'.$newxxx->zondid.'_'.$t_rewardCuston_rid->rwid);
            if(!$reward_with_server){
              $reward_with_server =  config('rewardsC.reward_def');
            }  
        }

    
        if(!isset($reward_with_server[$t_rewardCuston_rid->type_rw][$t_rewardCuston_rid->top])){
          return redirect()->route('ucp_reward_custon')->with('message','Không tìm thấy phần thưởng!')->with('status',2);
        }

        $log = $this->isReward($t_rewardCuston_rid->rwid,$t_rewardCuston_rid->rid,$t_rewardCuston_rid->type_rw,$t_rewardCuston_rid->top,$t_rewardCuston_rid->zondid);
        $t_rewardCuston = t_rewardCuston::where('id', $t_rewardCuston_rid->rwid)->first();
        
        if($log>0){

            return redirect()->route('ucp_reward_custon')->with('message',__('usercp.reward.custon.get.reward.failed',['name'=>$t_rewardCuston->name]))->with('status',2);
        }
        $goods_parse = Helper::getGoods();
        return view('usercp.rewardCustom.rewards',[
            'title'=>'Server1',
            'topType'=>$t_rewardCuston_rid->type_rw,
            'daytop'=>$t_rewardCuston_rid->top,
            'goods_parse'=>$goods_parse,
            'reward_with_server'=> $reward_with_server,
            'id'=>$id
        ]);
    }
    public function index()
    {   

        $account = Auth::user();
        $server = Server::where('Id', $account->Role)->first();
        $list_rq = t_rewardCuston_rid::join('t_reward_custons','t_reward_custon_rids.rwid','=','t_reward_custons.id')
        ->select('t_reward_custons.*','t_reward_custon_rids.*')
        ->where('t_reward_custon_rids.uid','=',$account->UserID)
        ->where('t_reward_custon_rids.zondid','=',$server->Id)
        ->paginate(10);
        return view('usercp.rewardCustom.index',['lists'=>$list_rq]);

    }

   

   

    public function sendmail(Request $request)
    {   


        $account = Auth::user();
        if(!$account){
           return abort(404);
        }

        $t_rewardCuston_rid = t_rewardCuston_rid::select('top','uid','type_rw','rwid','rid','zondid')
        ->where('uid',$account->UserID)
        ->where('zondid',$account->Role)
        ->where('id',$request->id)->first();
        if(!$t_rewardCuston_rid){
            return redirect()->route('ucp_reward_custon')->with('message',__('usercp.reward.custon.get.reward.message_unknown'))->with('status',2);
        }

        $reward_with_server =  config('rewardsC.reward_'.$t_rewardCuston_rid->zondid.'_'.$t_rewardCuston_rid->rwid);
         if(!$reward_with_server){
            $newxxx = t_rewardCuston::where('id',$t_rewardCuston_rid->rwid)->first();
            $reward_with_server =  config('rewardsC.reward_'.$newxxx->zondid.'_'.$t_rewardCuston_rid->rwid);
            if(!$reward_with_server){
              $reward_with_server =  config('rewardsC.reward_def');
            }  
        }
        

        $t_rewardCuston = t_rewardCuston::where('id', $t_rewardCuston_rid->rwid)->first();
        $server = Server::where('Id',$t_rewardCuston_rid->zondid)->first();
        if($server){
            $role = DB::connection($server->Id)->table('t_roles')->where('rid',$t_rewardCuston_rid->rid)->orderBy('lasttime', 'desc')->first();
            if(!$role) {
                return redirect()->back()->with('message', __('usercp.message_unknown_char'))->with('status', 2);
            }
        } else {
            return redirect()->back()->with('message', __('usercp.message_unknown_server'))->with('status', 2);
        }

        $topType=  $t_rewardCuston_rid->type_rw;
        $daytop =  $t_rewardCuston_rid->top;
    
        switch ($topType) {
            case 'week':
                $mes_rw = __('usercp.rank.character.reward.success',[
                    'type'=>__('usercp.rank.week'),
                    'rank'=>$daytop  ]);
            break;
             case 'month':
               $mes_rw = __('usercp.rank.character.reward.success',[
                'type'=>__('usercp.rank.month'),
                'rank'=>$daytop ]);
            break;
            default:
              
               $mes_rw = __('usercp.rank.character.reward.success',
              [
                'rank'=>$daytop,
                'type'=>__('usercp.rank.day'),
                
            ]  
            );

            break;
        }

        //$id_rw,$rid,$type_rw,$top,$zoneid
        $t_rewardCuston_log_count = $this->isReward(
            $t_rewardCuston_rid->rwid,$t_rewardCuston_rid->rid,$t_rewardCuston_rid->type_rw,$t_rewardCuston_rid->top,$account->Role);
      
        
        if($t_rewardCuston_log_count>0){
            return redirect()->route('ucp_reward_custon')->with('message',__('usercp.reward.custon.get.reward.failed',['name'=>$t_rewardCuston->name]))->with('status',2);
        }

        $dt = Carbon::now();
        if(!isset($reward_with_server[$topType][$daytop])){
          return redirect()->route('ucp_reward_custon')->with('message','Không tìm thấy phần thưởng!')->with('status',2);
        }
        $award =  $reward_with_server[$topType][$daytop];



        foreach($award['custom'] as $v => $customs) {
        $items_ls = $request->input('item' . '_' . $v);
         $items_ls = $request->input('item' . '_' . $v);
            if(count($items_ls) < $customs['number'] ) {
                return redirect()->back()->with('message', __('usercp.message_reward_min', ['number' => $customs['number'], 'item' => Helper::getCustomReward($v)]))->with('status', 2);
                 die();
            }
            elseif(count($items_ls) > $customs['number']) {
                return redirect()->back()->with('message', __('usercp.message_reward_max', ['number' => $customs['number'], 'item' => Helper::getCustomReward($v)]))->with('status', 2);
                 die();
            }

       }
       
        foreach($award['custom'] as $v => $customs) {
            $items_ls = $request->input('item' . '_' . $v);
            if(count($items_ls) < $customs['number'] ) {
                return redirect()->back()->with('message', __('usercp.message_reward_min', ['number' => $customs['number'], 'item' => Helper::getCustomReward($v)]))->with('status', 2);
            }
            elseif(count($items_ls) > $customs['number']) {

                return redirect()->back()->with('message', __('usercp.message_reward_max', ['number' => $customs['number'], 'item' => Helper::getCustomReward($v)]))->with('status', 2);
            }

            switch ($topType) {

                 case 'month':
                   
                    $type_top_mail = __('usercp.rank.month');
                    break;
                case 'week':
                   
                    $type_top_mail = __('usercp.rank.week');
                    break;
                default:
                
                   $type_top_mail = __('usercp.rank.day');
                   break;
            }

            $mailid = DB::connection($server->Id)->table('t_mail')
                ->insertGetId([
                            'senderrid' => 0,
                            'senderrname' => 'GM',
                            'sendtime' => $dt,
                            'receiverrid' => $role->rid,
                            'reveiverrname' => $role->rname,
                            'subject' => __('usercp.rank.mail_reward_subject'),
                            'content' => __('usercp.rank.mail_reward_content',
                             [
                            'char' => $role->rname,
                            'amount' =>$daytop,
                            'type'=>$type_top_mail,
                            'data'=>''
                         ]
                         )
                        ]);

            $items = [];
            foreach($award['default'] as $item) {
                    $goods = explode(',', $item);
                    $items[] = [
                                'mailid' => $mailid,
                                'goodsid' => $goods[0],
                                'gcount' => $goods[1],
                                'binding' => $goods[2],
                                'forge_level' => $goods[3],
                                'appendproplev' => $goods[4],
                                'lucky' => $goods[5],
                                'excellenceinfo' => $goods[6],
                            ];  
                    }

            $tm_g_df = DB::connection($server->Id)->table('t_mailgoods')->insert($items);

            $custom_items = [];
                        foreach($award['custom'] as $v => $customs) {               
                            
                            $item_reward = $request->input('item' . '_' . $v);
                                
                            foreach($customs['item'] as $k => $custom) {
                                
                                if(count($item_reward) > 0 && in_array($k, $item_reward) !== FALSE) {
                                    $goods = explode(',', $custom);
                                    $custom_items[] = [
                                        'mailid' => $mailid,
                                        'goodsid' => $goods[0],
                                        'gcount' => $goods[1],
                                        'binding' => $goods[2],
                                        'forge_level' => $goods[3],
                                        'appendproplev' => $goods[4],
                                        'lucky' => $goods[5],
                                        'excellenceinfo' => $goods[6],
                                    ];
                                }
                            }
                        }
            $tm_g_C =  DB::connection($server->Id)->table('t_mailgoods')->insert($custom_items);   

            t_rewardCuston_log::create([
                'rid'=>$t_rewardCuston_rid->rid,
                'zoneid'=>$account->Role,
                'uid'=>$account->UserID, 
                'top'=>$daytop,
                'type_rw'=>$topType,
                'rw_id'=>$t_rewardCuston_rid->rwid
            ]) ;           
            return redirect()->route('ucp_reward_custon')
            ->with('message',__('usercp.reward.custon.get.reward.message',['name'=>$t_rewardCuston->name,'top'=>$daytop]))
            ->with('status', 1);  

        }

        //return redirect()->back()->with('message', __('usercp.message_reward_error'))->with('status',2);
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
