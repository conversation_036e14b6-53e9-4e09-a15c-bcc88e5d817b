<?php

namespace App\Http\Controllers\UserCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\WC_Pool;
use App\WC_Pool_Log;
use Carbon\Carbon;
use Helper;
use Auth;
class WordCupController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
         $this->middleware('auth');
    }	
	
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
		$logs = WC_Pool::orderby('id','DESC')->get();
		return view('usercp.worldcup.index',['logs'=>$logs]);
    }
    public function playwc(Request $request, $id){


        $getPool = WC_Pool::where('id',$id)->first();
        if( $getPool){
           $check_time = Helper::wcCheckTime($getPool->dayend);

            if(!$check_time){
                return redirect()->route('userwc')->with('message','trận đấu đã đóng.')->with('status',2);
            }
           return view('usercp.worldcup.play',['id'=>$id,'pool'=>$getPool]);
        }else{
            return redirect()->route('userwc')->with('message','Không tìm thấy trận đấu.')->with('status',2);
        }


       
    }

    public function playwc_pool(Request $request){

       
        $getPool = WC_Pool::where('id',$request->id_pool)->first();

        if(!$request->id_pool){
            return redirect()->back()->with('message','Không tìm thấy id trận.')->with('status',2);
        }

        if(!$request->amount){
            return redirect()->back()->withErrors(array('amount'=>'Vui lòng nhập số tiền muốn cược.'))->with('status',2);
        }

        if(!isset($request->pool1)){
            return redirect()->back()->withErrors( array("pool_team1"=>"Vui lòng đặt tỷ số cho  $getPool->Team1") )->with('status',2);
        }
        if(! isset($request->pool2) ){
            return redirect()->back()->withErrors( array('pool_team2'=>"Vui lòng đặt tỷ số cho  $getPool->Team1") )->with('status',2);
        }
       


        
        if( $getPool){
            $account = Auth::user();
            $pool = array(
                'id_pool'=>$request->id_pool,
                'userId'=>$account->id,
                'pool'=>$request->pool1.'-'.$request->pool2,
                'status'=>'0',
                'amount'=>$request->amount,
            );

            $check_time = Helper::wcCheckTime($getPool->dayend);

            if(!$check_time){
                return redirect()->route('userwc')->with('message','trận đấu đã đóng.')->with('status',2);
            }

           


            if($pool){
                if($account->balance < $request->amount){

                    return redirect()->route('userwc')->with('message','Không đủ coin để cược.')->with('status',2);
                }

                $pool= WC_Pool_Log::create($pool);
                $account->balance =  $account->balance-$request->amount;
                $account->save();
                return redirect()->route('userwc')->with('message',"Cược thành công trận đấu giũa: $getPool->Team1 VS $getPool->Team2 ")->with('status',1);
            }else{
                return redirect()->route('userwc')->with('message','Có lỗi trong cược.')->with('status',2);
            }
        }else{
            return redirect()->route('userwc')->with('message','Không tìm thấy trận đấu.')->with('status',2);
        }
    }

    public function editPool_submit(Request $request){

        $getPool = WC_Pool::where('id',$request->id)->first();
         if( $getPool){

            $getPool->Team1=$request->team1;
            $getPool->Team2=$request->team2;
            $getPool->pool=$request->pool?$request->pool:'';
            $getPool->status=0;
            $getPool->dayend=$request->dayend;
            $getPool->amount=$request->amount;

            $getPool->save();

           return redirect()->route('adminwc')->with('message','Cập nhập thành công.')->with('status',1);
        }else{
            return redirect()->route('adminwc')->with('message','Không tìm thấy trận đấu.')->with('status',2);
        }

    }
    public function createpool_submit(Request $request){

        $opts = array(
            'Team1'=>$request->team1,
            'Team2'=>$request->team2,
            'pool'=>'',
            'status'=>0,
            'dayend'=>$request->dayend,
            'amount'=>$request->amount,
        );
        $create = WC_Pool::create($opts);

        
        if( $create){
            return redirect()->route('adminwc')->with('message','Tạo trận thành công')->with('status',1);
        }else{
            return redirect()->back()->with('message','Tạo trận lỗi')->with('status',2);
        }

    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
