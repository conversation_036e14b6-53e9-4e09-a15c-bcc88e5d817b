<?php

namespace App\Http\Controllers\UserCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\History;
use App\Helper;
use Auth;

use App\Server;
use DB;
use Carbon\Carbon;
use App\Rank_Log;

class RanksController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }	
		
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    public function getlistTop( $date=null,$type='day'){

        $account = Auth::user();
        $server = Server::where('Id', $account->zoneid)->first();
        $date = isset($date)?$date:date("Y-m-d"); 

        $rank = [];

        switch ($type) {
            case 'week':
            $rank = DB::connection($server->Id)->table('resetlevel')
                ->leftJoin('t_roles', 't_roles.rid', '=', 'resetlevel.rid')
                ->select(DB::raw('count(*) as total,resetlevel.rid,t_roles.combatforce,MAX(resetlevel.date) as maxdate,t_roles.rname,t_roles.changelifecount,t_roles.`level`,t_roles.occupation'))
                ->whereraw('YEARWEEK(resetlevel.date,5) = YEARWEEK("'. $date .'",5 )')
                ->groupBy('rid')
                ->orderBy('total','DESC')
                ->orderBy('maxdate','ASC')
                ->limit(10)->get();
            break;
            case 'month':
            $rank = DB::connection($server->Id)->table('resetlevel')
                ->leftJoin('t_roles', 't_roles.rid', '=', 'resetlevel.rid')
                ->select(DB::raw('count(*) as total,resetlevel.rid,MAX(resetlevel.date) as maxdate,t_roles.combatforce,t_roles.rname,t_roles.changelifecount,t_roles.`level`,t_roles.occupation'))
                //->whereraw('     DATE(resetlevel.date) = DATE_ADD("'.$date.'", INTERVAL -1 DAY)')
                ->whereraw('Year(resetlevel.date)=Year("'.$date.'") AND Month(resetlevel.date)= Month("'.$date.'")')
                ->groupBy('t_roles.rid')
                ->orderBy('total','DESC')
                ->orderBy('maxdate','ASC')
                ->limit(10)->get();
                break;
            default:
            $rank = DB::connection($server->Id)->table('resetlevel')
                ->leftJoin('t_roles', 't_roles.rid', '=', 'resetlevel.rid')
                ->select(DB::raw('count(*) as total,MAX(resetlevel.date) as maxdate,resetlevel.rid,t_roles.combatforce,t_roles.rname,t_roles.changelifecount,t_roles.`level`,t_roles.occupation'))
                ->whereraw('DATE(resetlevel.date) ="'.$date.'"')
                ->groupBy('resetlevel.rid')
                ->orderBy('total','DESC')
                ->orderBy('maxdate','ASC')
                ->limit(10)->get();
            break;
         }
         return $rank ; 
    }


    public function index(Request $request)
    {
		
        $account = Auth::user();
        $server = Server::where('Id', $account->zoneid)->first();
        $rank_to_day = $this->getlistTop();

        $rank_to_month = $this->getlistTop(null,'month');
        $rank_to_week=$this->getlistTop(null,'week');

        $yesterday = date("Y-m-d", strtotime(" -1 day"));
        $week_day = date("Y-m-d",strtotime(" -1 week"));
        $month_day = date("Y-m-d",strtotime(" -1 month"));

        $daytop = $this->userTop($yesterday);
        $weektop = $this->userTop($week_day,'week');
        $monthtop = $this->userTop($month_day,'month');

        $rank_to_day_befor = $this->getlistTop($yesterday);
        $rank_to_week_befor = $this->getlistTop($week_day,'week');
        $rank_to_month_befor = $this->getlistTop($month_day,'month');


        $isTopday = ($daytop>10||$daytop==0)?false:true;
        $isTopWeek = ($weektop>10||$weektop==0)?false:true;
        $isTopMonth = ($monthtop>10||$monthtop==0)?false:true;



        $curentday = date("Y-m-d");
        $curentWeek = date("W", strtotime($curentday));
        $curentMonth = date("m", strtotime($curentday));


        $btn_week_bf = date("W", strtotime($week_day));
        $btn_month_bf = date("m", strtotime($month_day));



        return view('usercp.rank', [
                'ranks' => $rank_to_day,
                'ranks_week' => $rank_to_week,
                'ranks_month' => $rank_to_month,
                'rank_to_day_befor'=>$rank_to_day_befor,
                'rank_to_week_befor'=>$rank_to_week_befor,
                'rank_to_month_befor'=>$rank_to_month_befor,
                'isTopday'=> $isTopday,
                'isTopWeek'=> $isTopWeek,
                'isTopMonth'=> $isTopMonth,
                'btns'=>[
                    'day'=>__('usercp.rank.btn.reward',['rank'=>$daytop,'type'=>__('usercp.rank.day'),'date'=>$yesterday]),
                    'month'=>__('usercp.rank.btn.reward',['rank'=>$monthtop,'type'=>__('usercp.rank.month'),'date'=>$btn_month_bf]),
                    'week'=>__('usercp.rank.btn.reward',['rank'=>$weektop,'type'=>__('usercp.rank.week'),'date'=>$btn_week_bf])
                ],
                'curentDay'=>[
                     'day'=>$curentday,
                     'week'=>$curentWeek,
                     'month'=>$curentMonth
                ],
                'beforday'=>[
                     'day'=>$yesterday,
                     'week'=>$btn_week_bf,
                     'month'=> $btn_month_bf
                ]
            ]);

    }

    public function userTop($date=null,$type='day'){

        $account = Auth::user();
        $server = Server::where('Id', $account->zoneid)->first();
        $top = 0;
        $rank = $this->getlistTop($date,$type);
        

        $getCharter = DB::connection($server->Id)->table('t_roles')
        ->select('rid')
        ->where('userid', $account->userid)
        ->orderBy('lasttime', 'desc')->first();

        if(! $rank){
            return $top;
        }
        foreach ($rank as $key => $value) { 
           if($value->rid==$getCharter->rid){
            $top = $key+1;
            break;
           }
        }
        
        return $top;

    }
    public function reward( Request $request ){


        $account = Auth::user();
        $server = Server::where('Id', $account->zoneid)->first();
        $goods_parse = Helper::getGoods();
        
        $topType=isset($request->type)?$request->type:'day';

        $yesterday = date("Y-m-d", strtotime(" -1 day"));  

        switch ($topType) {
            case 'week':
               $yesterday = date("Y-m-d",strtotime(" -1 week"));
            break;
             case 'month':
               $yesterday = date("Y-m-d",strtotime(" -1 month"));
            break;
            default:
               $yesterday = date("Y-m-d", strtotime(" -1 day"));
            break;
        }
        
        $daytop = $this->userTop($yesterday,$topType);

        switch ($topType) {
            case 'week':
               $title_text = __('usercp.rank.title.reward',[
                'type'=>__('usercp.rank.week'),
                'number'=>$daytop,'date'=>date("W", strtotime($yesterday))]);
               $title_error_not_in_top = __('usercp.rank.character.not.top',['type'=>__('usercp.rank.week')]);
               $title_error_rw = __('usercp.rank.character.reward.error',
              [
                'rank'=>$daytop,
                'type'=>__('usercp.rank.week'),
                'date'=>date("W", strtotime($yesterday))
            ]  
            );
            break;
             case 'month':
               $title_text = __('usercp.rank.title.reward',[
                'type'=>__('usercp.rank.month'),
                'number'=>$daytop,'date'=> date("m", strtotime($yesterday))  ]);
               $title_error_not_in_top = __('usercp.rank.character.not.top',['type'=>__('usercp.rank.month')]);
               $title_error_rw = __('usercp.rank.character.reward.error',
              [
                'rank'=>$daytop,
                'type'=>__('usercp.rank.month'),
                'date'=>date("m", strtotime($yesterday))
            ]  
            );

            break;
            default:
               $title_text = __('usercp.rank.title.reward',[
                'type'=>__('usercp.rank.day'),
                'number'=>$daytop,'date'=>$yesterday]);
               $title_error_not_in_top = __('usercp.rank.character.not.top',['type'=>__('usercp.rank.day')]);
               $title_error_rw = __('usercp.rank.character.reward.error',
              [
                'rank'=>$daytop,
                'type'=>__('usercp.rank.day'),
                'date'=>$yesterday
            ]  
            );

            break;
        }

        


        $getCharter = DB::connection($server->Id)->table('t_roles')
        ->select('rid')
        ->where('userid', $account->userid)
        ->orderBy('lasttime', 'desc')->first();

        $count_rank_rw_log = Rank_Log::where('top','=',$daytop)
        ->where('topType','=',$topType)
        ->where('uid','=',$account->id)
        ->where('zoneid','=',$server->Id)
        ->where('rid','=',$getCharter->rid);

        switch ($topType) {
            case 'week':
               
                $count_rank_rw_log=$count_rank_rw_log->where('weeks','=',date("W", strtotime($yesterday)))
               ->count();
               $rank_reward_error =__('usercp.rank.character.reward.error',
              [
                'rank'=>$daytop,
                'type'=>__('usercp.rank.week'),
                'date'=>date("W", strtotime($yesterday))
                ]  
            );

            break;
             case 'month':
               
               $count_rank_rw_log=$count_rank_rw_log->where('months','=',date("m", strtotime($yesterday)))
              ->where('years','=',date("y", strtotime($yesterday)))
               ->count();
                $rank_reward_error =__('usercp.rank.character.reward.error',
              [
                'rank'=>$daytop,
                'type'=>__('usercp.rank.month'),
                'date'=>date("m", strtotime($yesterday))
                ]  
            );
            break;
            default:
               $count_rank_rw_log=$count_rank_rw_log->where('topdate','=',$yesterday)->count();
                $rank_reward_error =__('usercp.rank.character.reward.error',
              [
                'rank'=>$daytop,
                'type'=>__('usercp.rank.day'),
                'date'=>$yesterday
                ]  
            );
            break;
        }


        if( $count_rank_rw_log >0){
            return redirect()->route('ranks')->with('message',$rank_reward_error)
            ->with('status',2); 
        }
       

       
        if(!$getCharter){
            return  redirect()->route('ranks')
            ->with('message',__('usercp.rank.character.not.found'))
            ->with('status',2);
        }

        $check_Reset = DB::connection($server->Id)->table('resetlevel')
        ->where('rid',$getCharter->rid)->count();
        
        if(!$check_Reset){
            return redirect()->route('ranks')->with('message',__('usercp.rank.character.not.reset'))
            ->with('status',2);
        }

        
        
        if($daytop ==0 || $daytop > 10 ){
            return redirect()->route('ranks')->with('message',$title_error_not_in_top)
            ->with('status',2); 
        }

        return view('usercp.rank_reward',[
            'goods_parse' => $goods_parse,
            'rid'=>$getCharter->rid,
            'dateTop'=>$yesterday,
            'isReset'=>$check_Reset,
            'daytop'=>$daytop,
            'topType'=>$topType,
            'title'=>$title_text
        ]);

    }

    
    public function checklog($date){



    }

    public function sendmail(Request $request)
    {   

        $account = Auth::user();

        if(!$account){
           return abort(404);
        }
        $server = Server::where('Id', $account->zoneid)->first();

        if($server){
            $role = DB::connection($server->Id)->table('t_roles')->where('userid', $account->userid)->orderBy('lasttime', 'desc')->first();
            if(!$role) {
                return redirect()->back()->with('message', __('usercp.message_unknown_char'))->with('status', 2);
            }
        } else {
            return redirect()->back()->with('message', __('usercp.message_unknown_server'))->with('status', 2);
        }

        $topType=  $request->toptype;

        $yesterday = date("Y-m-d", strtotime(" -1 day"));  
        switch ($topType) {
            case 'week':
               $yesterday = date("Y-m-d",strtotime(" -1 week"));
            break;
             case 'month':
               $yesterday = date("Y-m-d",strtotime(" -1 month"));
            break;
            default:
               $yesterday = date("Y-m-d", strtotime(" -1 day"));
            break;
        }

        $daytop = $this->userTop($yesterday,$topType);
 
        switch ($topType) {
            case 'week':
                $mes_rw = __('usercp.rank.character.reward.success',[
                'type'=>__('usercp.rank.week'),
                'rank'=>$daytop,
                'date'=>date("W", strtotime($yesterday))]);

            break;
             case 'month':
               $mes_rw = __('usercp.rank.character.reward.success',[
                'type'=>__('usercp.rank.month'),
                'rank'=>$daytop,
                'date'=> date("m", strtotime($yesterday))  ]);
            break;
            default:
              
               $mes_rw = __('usercp.rank.character.reward.success',
              [
                'rank'=>$daytop,
                'type'=>__('usercp.rank.day'),
                'date'=>$yesterday
            ]  
            );

            break;
        }


        $dt = Carbon::now();
        $award =  config('TopReward')[$topType][$daytop];
        

       foreach($award['custom'] as $v => $customs) {
        $items_ls = $request->input('item' . '_' . $v);
         $items_ls = $request->input('item' . '_' . $v);
            if(count($items_ls) < $customs['number'] ) {
                return redirect()->back()->with('message', __('usercp.message_reward_min', ['number' => $customs['number'], 'item' => Helper::getCustomReward($v)]))->with('status', 2);
                 die();
            }
            elseif(count($items_ls) > $customs['number']) {
                return redirect()->back()->with('message', __('usercp.message_reward_max', ['number' => $customs['number'], 'item' => Helper::getCustomReward($v)]))->with('status', 2);
                 die();
            }

       }
       

        foreach($award['custom'] as $v => $customs) {

            $items_ls = $request->input('item' . '_' . $v);
            if(count($items_ls) < $customs['number'] ) {
                return redirect()->back()->with('message', __('usercp.message_reward_min', ['number' => $customs['number'], 'item' => Helper::getCustomReward($v)]))->with('status', 2);
            }
            elseif(count($items_ls) > $customs['number']) {

                return redirect()->back()->with('message', __('usercp.message_reward_max', ['number' => $customs['number'], 'item' => Helper::getCustomReward($v)]))->with('status', 2);
            }

            switch ($topType) {

                 case 'month':
                    $date_top   = date("m", strtotime($yesterday));
                    $type_top_mail = __('usercp.rank.month');
                    break;
                case 'week':
                    $date_top   = date("W", strtotime($yesterday));
                    $type_top_mail = __('usercp.rank.week');
                    break;
                default:
                   $date_top   = $yesterday;
                   $type_top_mail = __('usercp.rank.day');
                   break;
            }

            $mailid = DB::connection($server->Id)->table('t_mail')
                ->insertGetId([
                            'senderrid' => 0,
                            'senderrname' => 'GM',
                            'sendtime' => $dt,
                            'receiverrid' => $role->rid,
                            'reveiverrname' => $role->rname,
                            'subject' => __('usercp.rank.mail_reward_subject'),
                            'content' => __('usercp.rank.mail_reward_content',
                             [
                            'char' => $role->rname,
                            'amount' =>$daytop,
                            'type'=>$type_top_mail,
                            'data'=>$date_top
                         ]
                         )
                        ]);

            $items = [];
            foreach($award['default'] as $item) {
                    $goods = explode(',', $item);
                    $items[] = [
                                'mailid' => $mailid,
                                'goodsid' => $goods[0],
                                'gcount' => $goods[1],
                                'binding' => $goods[2],
                                'forge_level' => $goods[3],
                                'appendproplev' => $goods[4],
                                'lucky' => $goods[5],
                                'excellenceinfo' => $goods[6],
                            ];  
                    }

            DB::connection($server->Id)->table('t_mailgoods')->insert($items);
            $custom_items = [];
                        foreach($award['custom'] as $v => $customs) {               
                            
                            $item_reward = $request->input('item' . '_' . $v);
                                
                            foreach($customs['item'] as $k => $custom) {
                                
                                if(count($item_reward) > 0 && in_array($k, $item_reward) !== FALSE) {
                                    $goods = explode(',', $custom);
                                    $custom_items[] = [
                                        'mailid' => $mailid,
                                        'goodsid' => $goods[0],
                                        'gcount' => $goods[1],
                                        'binding' => $goods[2],
                                        'forge_level' => $goods[3],
                                        'appendproplev' => $goods[4],
                                        'lucky' => $goods[5],
                                        'excellenceinfo' => $goods[6],
                                    ];
                                }
                            }
                        }
            DB::connection($server->Id)->table('t_mailgoods')->insert($custom_items);
           // $yesterday = date("Y-m-d", strtotime("-1 day")) ;          
            Rank_Log::create([
                            'uid' => $account->id,
                            'top'=>$daytop,
                            'topdate'=> $yesterday,
                            'rid'=>$role->rid, 
                            'topType'=>$topType,
                             'weeks'=> date("W", strtotime($yesterday)),
                             'months'=>date("m", strtotime($yesterday)),
                              'years'=>date("y", strtotime($yesterday)),
                            'zoneid'=>$server->Id,
                        ]);
                            
                        return redirect()->route('ranks')->with('message', $mes_rw )->with('status', 1);            
        }

        //return redirect()->back()->with('message', __('usercp.message_reward_error'))->with('status',2);
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
