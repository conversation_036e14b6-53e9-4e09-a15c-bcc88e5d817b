<?php 
namespace App\Http\Controllers\UserCP;

use Illuminate\Http\Request;
use App\Http\Requests\RewardRequest;
use App\Http\Controllers\Controller;

use App\Account;
use App\Lottery;
use App\Helper;

use Carbon\Carbon;
use Config;
use Auth;
use Cache;
use App\t_luckywinwheel;

use DB;

/**
 * 
 */
class EventsController extends Controller
{
	
	function __construct()
	{
		 $this->middleware('auth');
	}

	public function winwheel(Request $request){

		$account = Auth::user();
		$time =  time();
		$token = csrf_token();
		$key = Config('luckywinwheel.key');
		$sign = md5($key.$time.$account->UserName);
		$logs = t_luckywinwheel::where('uid','ZT'.$account->UserID)->orderby('created_at','DESC')->limit(50)->get();
		$roles = DB::connection($account->Role)->table('t_roles')->where('userid',"ZT".$account->UserID)->orderBy('lasttime', 'desc')->where('isdel',0)->get();
		// dd($roles);

		return view('usercp.winwheel.index',[ 
			'token'=>$token ,
			'sign'=>$sign,
			'time'=>$time,
			'logs'=>$logs,
			'goods_parse' => Helper::getGoods(),
			'wheelNumber'=>Helper::getNumberWhell(),
			'roles' => $roles,
			'luotquay_left' => $account->luot_quay
		]);

	}
	public function winwheelCheckMoney(RewardRequest $request){
		$account= Auth::user();
		$coin = $account->Money;
		$chip = $account->chip;
		$luotquay_left = $account->luot_quay;
		$need_chip =  Config('luckywinwheel.chip');
		$need_coin =  Config('luckywinwheel.coin');
		$count =  Config('luckywinwheel.count');
		$free_count = Config('luckywinwheel.free_count');

		// dd($request->type);
		// dd($luotquay_left);

		$rid = empty(Helper::getRid()) ? $request->ex_rid : Helper::getRid();
		Helper::setRid($rid);

		// $role = DB::connection($account->Role)->table('t_roles')->where('rid',$rid)->orderBy('lasttime', 'desc')->first();
		$role = DB::connection($account->Role)->table('t_roles')->where('userid',"ZT".$account->UserID)->where('rid', $rid)->first();

		if(!$role){
			return json_encode(array('status'=>2,'msg'=>__('usercp.luckywinwheel.character.unknown') ));
		}

		// số lần đã quay trong ngày
		$getCount= t_luckywinwheel::where('uid',$account->UserID)
					->where('type','free')
					->whereraw("date(created_at) =DATE(CURDATE())")
					->count();
		// dd($getCount);

		if($getCount < $free_count ){
			session(['winwheel_type'=>'free']);

		}else{
			// nếu không phải free => trừ tiền coin hoặc chip. [k cho free . cho coin hoặc lần quay]
			if($request->type=='chip' && $chip < $need_chip ){
				return json_encode(array('status'=>2,'msg'=>__('usercp.luckywinwheel.chip.error',['chipname'=>config('chip.name'),
					'coinname'=>config('mu.currency.name'),])));
			}
			if($request->type=='coin' && $coin < $need_coin ){
				return json_encode(array('status'=>2,'msg'=>__('usercp.luckywinwheel.coin.error',['coinname'=>config('mu.currency.name')])));
			}
			session(['winwheel_type'=>'buy']);

			switch ($request->type) {
				case 'luotquay':
					if(config('luckywinwheel.use_luotquay')) {
						if($luotquay_left <= 0) {
							return json_encode(array('status'=>2,'msg'=> "Bạn đã hết lượt quay" ));
						}
						$account->luot_quay=$account->luot_quay-1;
					} else {
						return json_encode(array('status'=>2,'msg'=> "Không thể dùng lượt quay" ));
					}
					
					break;
				case 'coin':
					$account->Money=$account->Money-$need_coin;
					break;
				case 'chip':
					if($request->type=='chip' && config('luckywinwheel.use_chip') == 1){
						$account->chip=$account->chip-$need_chip;
					} else {
						return json_encode(array('status'=>2,'msg'=> "Không thể dùng chip" ));
					}
					break;
				
				
				default:
					return json_encode(array('status'=>2,'msg'=> "Unknown Error" ));
					break;
			}

			// if($request->type=='coin'){
			// 	$account->Money=$account->Money-$need_coin;
			// } else {
			// 	if($request->type=='chip' && config('luckywheel.use_chip') == 1){
			// 		$account->chip=$account->chip-$need_chip;
			// 	} else {
			// 		return json_encode(array('status'=>2,'msg'=> "Không thể dùng chip" ));
			// 	}
			// }
			
			$account->save();

		}
		
		session(['luckywinwheel_money_type'=>$request->type]);
		$reward_id = Helper::luckywinwheelRandom();
		$key = Config('luckywinwheel.key');

		$wheelNumber = Helper::getNumberWhell();
		
		
		$sign = md5($reward_id.$account->UserName.$key);
		
		Cache::forever('sign' . $account->UserName, $sign);	

		return json_encode(array(

			'status'=>1,
			'reward_id'	=>$reward_id,
			'wheelMsg'=> __('usercp.luckywinwheel.wheel.free',[ 'number'=>$wheelNumber['free'], 'number2'=>Helper::numberFormat($wheelNumber['chip']+$wheelNumber['coin'])  ] )

		));


	}
	public function getReward(RewardRequest $request){

		$account = Auth::user();
		$id_reward = $request->id;
		$key = Config('luckywinwheel.key');
		$item = Helper::getLuckyWinWheelItem($id_reward);


		$items = Config('luckywinwheel.items');
		$rid = Helper::getRid();

		$sign = Cache::get('sign' . $account->UserName);

		if($id_reward > count($items)){
			return json_encode(array('status'=>2,'msg'=>__('usercp.luckywinwheel.reward.null') ));
		}
		if(!is_array($item)){
			return json_encode(array('status'=>2,'msg'=>__('usercp.luckywinwheel.reward.unknown') ));
		}
		if(!$sign){
			return json_encode(array('status'=>2,'msg'=>__('usercp.luckywinwheel.sign.null') )); //luckywinwheel.sign.null
		}
		if($sign !== md5($id_reward.$account->UserName.$key) ){
			return json_encode(array('status'=>2,'msg'=>__('usercp.luckywinwheel.sign.notmatch') ));
		}
		$items_string = '';
		$reward = $item['reward'];
		// reward coin
		$coin = isset($reward['coin'])?$reward['coin']:0;
		$chip = isset($reward['chip'])?$reward['chip']:0;

		if($coin>0){
			$account->Money=$account->Money+$reward['coin'];
			$items_string =$items_string.'<p>'.__('usercp.luckywinwheel.give.money',['amount'=>$coin,
'name'=>'coin']).'</p>';
		}
		// reward chip
		if($chip>0){

			$account->chip=$account->chip+$reward['chip'];	
			$items_string =$items_string.'<p>'.__('usercp.luckywinwheel.give.money',['amount'=>$chip,
'name'=>'chip']).'</p>';
		}
		$account->save();
		$items_reward =isset($reward['items'])?$reward['items'] :[];
		

		if(count($items_reward)>0){
			$goods_parse = Helper::getGoods();
			// $role = DB::connection($account->Role)->table('t_roles')->where('rid',$rid)->orderBy('lasttime', 'desc')->first();
			$role = DB::connection($account->Role)->table('t_roles')->where('userid',"ZT".$account->UserID)->where('rid', $rid)->first();
			$sendmail= Helper::sendMailItem($items_reward,$role->rname,$rid,__('usercp.luckywinwheel.mail.subject'),__('usercp.luckywinwheel.mail.content'));
			foreach ($items_reward as $value){
				$good = explode(',', $value);
				$name_item = Helper::getItemGoods($goods_parse, $good[0])['name'];
				$items_string=$items_string."<p>".$name_item.'x'.$good[1].'</p>';
			}

		}

		$need_chip =  Config('luckywinwheel.chip');
		$need_coin =  Config('luckywinwheel.coin');

		t_luckywinwheel::create([
			'uid'=>'ZT'.$account->UserID,
			'rewardId'=>$id_reward,
			'items'=>implode('|',$reward['items']),
			'name'=>$item['name'], 
			'zoneid'=>$account->Role,
			'rid'=>$rid,
			'chip'=>$chip,
			'coin'=>$coin,
			'buyCoin'=>(session('winwheel_type')=='buy'&&session('luckywinwheel_money_type')=='coin')?$need_coin:0,
			'buyChip'=>(session('winwheel_type')=='buy'&&session('luckywinwheel_money_type')=='chip')?$need_chip:0,
			'type'=>session('winwheel_type'),
		]

		);

		$wheelNumber = Helper::getNumberWhell();

		return json_encode(array('status'=>1,'msg'=>__('usercp.luckywinwheel.give.success',[
			'items'=>$items_string,
			'reward'=>$item['name'],
			'wheelMsg'=> __('usercp.luckywinwheel.wheel.free',[ 'number'=>$wheelNumber['free'], 'number2'=>Helper::numberFormat($wheelNumber['chip']+$wheelNumber['coin'])  ] )
		]) ));
	}
}

?>