<?php

namespace App\Http\Controllers\UserCP;

use Illuminate\Http\Request;
use App\Trumthe;
use App\Trumthe247;
use Illuminate\Support\Facades\DB;
use Auth;
use DataTables;
use Yajra\DataTables\Html\Builder;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Recharge;

class TrumtheController extends Controller
{
    public function process(Request $request) {
        $api_key = $request->input('api_key');
        $api_secret = $request->input('api_secret');
        $status = $request->input('status');
        $desc = $request->input('desc');

        $card_data = $request->input('card_data');
        $amount = $card_data['amount'];
        $serial = $card_data['serial'];
        $pin = $card_data['pin'];
        $card_type = $card_data['card_type'];
        $amount = $card_data['amount'] * 1.5; // đây là tỉ lệ này
        $charge_time = $card_data['charge_time'];

        Log::info('process trumthe'.json_encode($request->all()));

        DB::beginTransaction();
		// 'serial' => $seri,
		// 'code' => $code,
        $trumthe = Recharge::where(['code' => $pin, 'serial' => $serial])->first();
        if($trumthe->status!=0) {
            return response()->json(['status' => false, 'msg' => 'Không thể xử lý lại giao dịch!']);
        }
        try {
            switch ($status) {
                case 1:
                    // thành công
                    $trumthe->status = 1;
                	$trumthe->save();
                    $user = $trumthe->user;
                    $user->Money += $amount;
                    $user->save();
                    break;
                
                default:
                    // thất bại
                    $trumthe->status = 2;
                	// $trumthe->message = $desc;
                	$trumthe->save();
                    break;
            }            
        } catch(\Exception $e) {
            DB::rollback();
            throw $e;
        }

        DB::commit();

    	return response()->json(['status' => true, 'msg' => 'Xử lý giao dịch thành công!']);
    }

    public function cardHistory(Builder $htmlBuilder, Request $request) {
        $user = Auth::user();
        if ($request->ajax()) {
            $azpro_history = Trumthe::where('user_id', $user->UserID)->orderBy('transaction_id', 'desc');

            $table = Datatables::of($azpro_history);
            $table = $table->editColumn('status', function($data) {
                switch ($data->status) {
                    case 0:
                        $html = '<span class="badge badge-warning">Đang chờ</span>';
                        break;
                    case 1:
                        $html = '<span class="badge badge-success">Thành công</span>';
                        break;
                    case 2:
                        $html = '<span class="badge badge-secondary">Thất bại</span>';
                        break;
                    
                    default:
                        $html = '<span class="badge badge-danger">Lỗi</span>';
                        break;
                }

                return $html;
            });

            return $table->rawColumns(['status'])->make(true);
        }


        $html = $htmlBuilder
                ->addColumn(['data' => 'code', 'name' => 'code', 'title' => 'Mã thẻ'])
                ->addColumn(['data' => 'seri', 'name' => 'seri', 'title' => 'Seri'])
                ->addColumn(['data' => 'status', 'name' => 'status', 'title' => 'Trạng thái'])
                ->addColumn(['data' => 'message', 'name' => 'message', 'title' => 'Ghi chú'])
                ->addColumn(['data' => 'created_at', 'name' => 'created_at', 'title' => 'Ngày tạo']);

        $html->parameters([
            'scrollX' => true,
            'autoWidth' => false,
            'searching' => false,
        ]);

        return view('card-history', compact('html', 'user'));
    }

    public function getChar(Request $request) {
    	if(!$request->has('server')) {
    		return response()->json(['status' => false]);
    	}
    	$user = Auth::user();
    	$char_id = $user->UserID;
    	$server = $request->input('server');

    	$charbase = DB::connection('mysql_session'.$server)->table('charbase')->select('ACCNAME', 'NAME', 'LEVEL')->where('ACCNAME', 'like', '%-'.$char_id)->where('ZONE', $server)->get();

    	return response()->json(['status' => true, 'chars' => $charbase]);
    }

    public function gachthe(Request $request) {
    	if(!Auth::check()) {
    		return redirect()->route('login');
    	}
    	
		$user = Auth::user();

    	$products = [
    		'VTT' => 'VIETTEL',
    		'VNP' => 'VINAPHONE',
    		'VMS' => 'MOBIFONE',
    	];

    	if(!$request->has('telco') || empty(trim($request->input('telco')))) {
    		return back()->withInput()->with('error', 'Thông tin telco không hợp lệ, vui lòng liên hệ Admin để kiểm tra!');
    	}

    	$product = $request->input('telco');

    	if(!array_key_exists($product, $products)) {
    		return back()->withInput()->with('error', 'Thông tin product không hợp lệ, vui lòng liên hệ Admin để kiểm tra!');
    	}

    	if(!$request->has('code') || empty(trim($request->input('code')))) {
    		return back()->withInput()->with('error', 'Thông tin code không hợp lệ, vui lòng liên hệ Admin để kiểm tra!');
    	}

    	if(!$request->has('seri') || empty(trim($request->input('seri')))) {
    		return back()->withInput()->with('error', 'Thông tin seri không hợp lệ, vui lòng liên hệ Admin để kiểm tra!');
    	}

    	if(!$request->has('amount') || empty(trim($request->input('amount')))) {
    		return back()->withInput()->with('error', 'Thông tin amount không hợp lệ, vui lòng liên hệ Admin để kiểm tra!');
    	}

    	// if(!$request->has('char_name') || empty(trim($request->input('char_name')))) {
    	// 	return back()->with('error', 'Thông tin char_name không hợp lệ, vui lòng liên hệ Admin để kiểm tra!');
    	// }

    	// if(!$request->has('server') || empty(trim($request->input('server')))) {
    	// 	return back()->with('error', 'Thông tin server không hợp lệ, vui lòng liên hệ Admin để kiểm tra!');
    	// }

    	$code = trim($request->input('code'));
    	$seri = trim($request->input('seri'));
    	$amount = $request->input('amount');
    	// $char_id = $request->input('char_id');
    	$server = $request->input('server');

		// $type = $products[$product];
		$type = $product;
        $note = $user->UserName . " nạp thẻ " . $product . " và chọn mệnh giá " . $amount;
        Log::info($note);

        $trumthe247 = new Trumthe247;
		$charge_result = $trumthe247->ChargeCard($type, $seri, $code, $amount, $note);

        if($charge_result == false) { //Có lỗi trong quá trình đẩy thẻ.
            return back()->withInput()->with('error', 'Có lỗi trong quá trình xử lý, xin thử lại hoặc liên hệ Admin');
        } else if(is_string($charge_result)) { //Có lỗi trả về của hệ thống TRUMTHE247.COM.
            $err = $charge_result;
            return back()->withInput()->with('error', 'Cổng nạp thẻ bị lỗi, phiền bạn liên hệ admin! . error: ' . $err);
        } else if(is_object($charge_result)) { //Gửi thẻ thành công lên hệ thống.
            $success = 'Gửi thẻ thành công!';
            DB::beginTransaction();
            try {
                
				switch($product) {
					case 'VTT':
						$type = 1;
						break;
					case 'VMS':
						$type = 2;
						break;
					case 'VNP':
						$type = 3;
						break;
					default:
						$type = 1;
						break;
				}
				$recharge = Recharge::create([
						'uid' => $user->UserID,
						'gateway' => 2,
						'merchant' => 1234,
						'type' => $type,
						'serial' => $seri,
						'code' => $code,
						'zoneid' => $user->Role,
						'ip' => $request->ip(),
						'status' => 0,
						'amount' =>$amount,
						'transaction_id'=>time()
					]);
            } catch(\Exception $e) {
                DB::rollback();
                return back()->withInput()->with('error', 'Nạp thẻ thất bại, hệ thống bận, vui lòng thử lại hoặc liên hệ admin! '.$e->getMessage());
            }
            DB::commit();

            return back()->with('success', "Nạp thẻ thành công!!!"); // đoạn này redirrect về trang danh sách thẻ nạp, hiện tại bên ông khi nạp xong là nó đi đâu?
        }
        
        $err = 'Có lỗi trong quá trình xử lý';
        return back()->withInput()->with('error', $err);
	}

	public function napthe() {
		$user = Auth::user();
		return view('usercp.napthe', compact('user'));
	}
}
