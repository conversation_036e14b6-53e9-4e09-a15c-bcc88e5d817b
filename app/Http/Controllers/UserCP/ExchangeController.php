<?php

namespace App\Http\Controllers\UserCP;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Server;
use App\History;
use App\Helper;

use Auth;
use Config;
use DB;

use Carbon\Carbon;

class ExchangeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
		
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
		
		$account = Auth::user();
		$server = Server::where('Id', $account->Role)->first();
		
		
		if($server) {
			$role = DB::connection($server->Id)->table('t_roles')->where('userid', $account->UserID)->orderBy('lasttime', 'desc')->first();
			if($role) {
				$rname = $role->rname;
			} else {
				$rname = __('usercp.message_switch_char');
			}
		} else {
			$rname = __('usercp.message_switch_server');
		}

        $charters =  DB::connection($server->Id)->table('t_roles')
        ->select('*')
        ->where('userid', 'ZT'.$account->UserID)
        ->orderBy('lasttime', 'desc')
        ->where('isdel',0)
        ->get();
       

		$promotion = Config::get('mu.promotion.percent');
		$promotion_zoneid = Config::get('mu.promotion.zoneid');
		$promotion_requires = Config::get('mu.promotion.require');
		$promotion_date = Config::get('mu.promotion.date');
		$promotion_weekly = Config::get('mu.promotion.weekly');
		$promotion_limit_number = Config::get('mu.promotion.limit.number');
		$exchanges = Config::get('mu.exchange');


		$dt = Carbon::now();
			
		$today = $dt->dayOfWeek;
		
		$promotion_time = false;
		if($promotion > 0 && $dt >= $promotion_date[0] && $dt <= $promotion_date[1] && ($account->Role == $promotion_zoneid || $promotion_zoneid == 0)) {
			$promotion_time = true;
		}
		
		$promotion_require = false;
		$diamond_times = 0;
		if(count($promotion_requires) > 0 && $dt >= $promotion_date[0] && $dt <= $promotion_date[1] && ($account->Role == $promotion_zoneid || $promotion_zoneid == 0)) {
			$promotion_require = true;
			$diamond_times = History::where('uid', $account->UserID)->where('type', 3)->whereDate('created_at', $dt->toDateString())->count();
		}		

		$promotion_today = $promotion_weekly[$today];
		$diamond = History::where('type', 3)->whereDate('created_at', $dt->toDateString())->count();	
		$promotion_limit = $promotion_limit_number - $diamond;
		
		$exchange_arr = array();
		foreach ($exchanges as $i => $exchange) {
			if( $account->Role >= $exchange) {
				$exchange_arr[] = __('usercp.exchange_note', ['currency' => config('mu.currency.name'), 'rate' => Helper::numberFormat($i * 10000)]);
			}
		}		
		
		$exchange_note = end($exchange_arr);
		
        return view('usercp.exchange', [
                                        'rname' => $rname,
										'promotion_from' => Helper::getFormatDate($promotion_date[0]),
										'promotion_to' => Helper::getFormatDate($promotion_date[1]),
										'promotion_today' => $promotion_today,
										'promotion_time' => $promotion_time,
										'promotion_limit' => $promotion_limit,
										'promotion_require' => $promotion_require,
										'diamond_times' => $diamond_times,
										'exchange_note' => $exchange_note,
                                        'charters'=>$charters
										]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(ExchangeRequest $request)
    {
		//
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
