<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Log;

class RequestLoggerPost
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);

        if(in_array($request->getMethod(), ['POST', 'PUT'])) {
			//here you can check the request to be logged
			$log = [
				'IP' => $request->ip(),
				'URI' => $request->getUri(),
				'METHOD' => $request->getMethod(),
				'REQUEST_BODY' => $request->all(),
				'RESPONSE' => $response->getContent()
			];
			Log::info("REQUEST_LOGGER", $log);
		}
        return $response;
    }
}