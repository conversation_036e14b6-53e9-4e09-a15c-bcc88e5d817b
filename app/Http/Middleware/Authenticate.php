<?php

namespace App\Http\Middleware;

use Illuminate\Auth\AuthenticationException;
use Closure;
use App\Account;
use Auth;
use Config;
use App\Helper;


class Authenticate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
		if (Auth::guest()) {

			if ($request->has('token')) {
				$platform_name = Config::get('mu.platform.name');
				$userid = $platform_name . $request->token;				
				$account = Account::where('userid', $userid)->first();
			
				if($account) {
					
					Auth::login($account);

					$rid = $request->rid;
					
					if(strlen($rid) > 3) {
						
						$zoneid = substr($rid, -3);
						
						$account->Role = (int)$zoneid;
					} else {
						$account->Role = $rid;
					}
					$account->save();
					
					return $next($request);
				}
			}
			else{
				$lang = Helper::getLangCode();
				return redirect()->route('user_login',[
					'act'=>'webpage.main',
					'deviceType'=>'ios',
					'u'=>'%@',
					'u2'=>'%@',
					'pid'=>'%@',
					'lang'=>$lang
				]);

			}
			throw new AuthenticationException('Unauthenticated.');
		} else {
			if ($request->has('rid')) {
				$account = Auth::user();
					
				if(strlen($request->rid) > 3) {
					$zoneid = substr($request->rid, -3);
					
					$rid = (int)$zoneid;
				} else {
					$rid = $request->rid;
				}
			
				if($account->Role != $rid) {
					$account->Role = $rid;
					$account->save();
				}
			}
		}
		
		return $next($request);
    }
}
