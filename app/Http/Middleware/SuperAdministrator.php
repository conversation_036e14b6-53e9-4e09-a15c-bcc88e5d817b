<?php

namespace App\Http\Middleware;

use Closure;

use Auth;

class SuperAdministrator
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
		
		if(Auth::guest()) {
			return redirect()->route('auth.index');
		} else {
			$user = Auth::user();
			
			if ($user->groupid != 1) {
				abort(403);
			}
		}		
		
        return $next($request);
    }
}
