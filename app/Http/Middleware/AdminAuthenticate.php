<?php

namespace App\Http\Middleware;

use Closure;
use Auth;

class AdminAuthenticate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
		
		// $ip = $request->ip();
		
		// $ipv4 = config('admin.ip');
		
		// if(in_array($ip, $ipv4) === false) {
			//return view('admincp.error.403');
			//return redirect()->route('403error');
	
			// abort(403,'Access Denied');
		// }		
		
		if(Auth::guest()) {
			return redirect()->route('auth.index');
		} else {
			$user = Auth::user();
			if (!in_array($user->groupid, [1,2])) {
				abort(403);
			} 		
		}
		
        return $next($request);
    }
}
