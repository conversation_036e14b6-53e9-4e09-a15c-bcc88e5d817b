<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Login extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_login';

	protected $primaryKey = [];	
	
    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'logintime', 'logouttime'
    ];	
	
	
	public $incrementing = false;	
	
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

	/**
     * Get the username record associated with the login.
     */
    public function acc()
    {
        return $this->hasOne('App\Account', 'userid', 'userid');
	}
	
	/**
     * Get the role record associated with the login.
     */
    public function char()
    {
        return $this->hasOne('App\Role', 'rid', 'rid');
	}
}
