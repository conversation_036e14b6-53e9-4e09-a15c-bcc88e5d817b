<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Money extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_money';
	
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'userid', 'money'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'userid'
    ];

	protected $primaryKey = [];	
	
	public $incrementing = false;	
	
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;		
}
