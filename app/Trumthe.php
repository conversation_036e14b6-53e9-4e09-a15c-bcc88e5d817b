<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Trumthe extends Model
{
      protected $primaryKey = 'transaction_id';
    protected $fillable = [
    	'transaction_id', 'code', 'seri', 'status', 'user_id'
    ];

    public function getCreatedAtAttribute($date) {
    	return Carbon::parse($date)->setTimeZone('Asia/Ho_Chi_Minh')->toDateTimeString();
    }

    public function user() {
    	return $this->belongsTo(Account::class, 'user_id', 'id');
    }
}
