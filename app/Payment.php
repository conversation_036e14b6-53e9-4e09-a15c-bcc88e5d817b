<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'account';	
	
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_payment';
		
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'uid', 'gateway', 'merchant', 'type', 'transaction_id', 'amount', 'status', 'ip', 'zoneid'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'ip'
    ];
	
	/**
     * Get the username record associated with the role.
     */
    public function acc()
    {
        return $this->hasOne('App\Account', 'id', 'uid');
	}
}
