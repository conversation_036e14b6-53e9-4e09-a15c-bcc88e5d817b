<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Character extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'mysql';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'zt_role';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'rid';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'rname',
        'userid',
        'serverid',
        'level',
        'exp',
        'gold',
        'pk_count',
        'online_time'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'level' => 'integer',
        'exp' => 'integer',
        'gold' => 'integer',
        'pk_count' => 'integer',
        'online_time' => 'integer',
        'last_login' => 'datetime',
        'created_at' => 'datetime'
    ];

    /**
     * Get the account that owns the character.
     */
    public function account()
    {
        return $this->belongsTo(Account::class, 'userid', 'UserID');
    }

    /**
     * Scope a query to only include characters from a specific server.
     */
    public function scopeServer($query, $serverId)
    {
        return $query->where('serverid', $serverId);
    }

    /**
     * Scope a query to only include characters above a certain level.
     */
    public function scopeMinLevel($query, $level)
    {
        return $query->where('level', '>=', $level);
    }

    /**
     * Get the character's display name with server info.
     */
    public function getDisplayNameAttribute()
    {
        return $this->rname . ' (Server ' . $this->serverid . ')';
    }

    /**
     * Check if character is online.
     */
    public function getIsOnlineAttribute()
    {
        // This would typically check against an online players table
        return false;
    }

    /**
     * Get character's total playtime in hours.
     */
    public function getPlaytimeHoursAttribute()
    {
        return round($this->online_time / 3600, 1);
    }
}
