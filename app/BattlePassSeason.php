<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BattlePassSeason extends Model
{
    use HasFactory;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'account';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'battle_pass_seasons';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'start_date',
        'end_date',
        'max_level',
        'price',
        'status'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'status' => 'boolean'
    ];

    /**
     * Get the rewards for this season.
     */
    public function rewards()
    {
        return $this->hasMany(BattlePassReward::class, 'season_id');
    }

    /**
     * Get user progress for this season.
     */
    public function userProgress()
    {
        return $this->hasMany(UserBattlePass::class, 'season_id');
    }

    /**
     * Check if season is active.
     */
    public function isActive()
    {
        $now = now();
        return $this->status && 
               $this->start_date <= $now && 
               $this->end_date >= $now;
    }

    /**
     * Get current active season.
     */
    public static function getCurrentSeason()
    {
        return self::where('status', true)
                   ->where('start_date', '<=', now())
                   ->where('end_date', '>=', now())
                   ->first();
    }

    /**
     * Get rewards by level.
     */
    public function getRewardsByLevel($level, $isPremium = false)
    {
        $query = $this->rewards()->where('level', $level);
        
        if ($isPremium) {
            return $query->get();
        } else {
            return $query->where('is_premium', false)->get();
        }
    }

    /**
     * Get all rewards up to level.
     */
    public function getRewardsUpToLevel($level, $isPremium = false)
    {
        $query = $this->rewards()->where('level', '<=', $level);
        
        if ($isPremium) {
            return $query->get();
        } else {
            return $query->where('is_premium', false)->get();
        }
    }
}
