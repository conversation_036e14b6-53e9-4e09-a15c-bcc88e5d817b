<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class MailGoods extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_mailgoods';
	
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'mailid', 'goodsid', 'forge_level', 'quality', 'Props', 'gcount', 'binding', 'origholenum', 'rmbholenum', 'jewellist', 'addpropindex', 'bornindex', 'lucky', 'strong', 'excellenceinfo', 'appendproplev', 'equipchangelife'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'mailid'
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;	
}
