<?php

namespace App;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class Account extends Authenticatable
{
	use HasFactory, HasApiTokens;

	/**
	 * The connection name for the model.
	 *
	 * @var string
	 */
	protected $connection = 'mysql';

	protected $primaryKey = 'UserID';

	/**
	 * The table associated with the model.
	 *
	 * @var string
	 */
	protected $table = 'zt_account';

	protected $username = 'UserName';
	protected $password = 'Password';
	const CREATED_AT = 'RegTime';

	/**
	 * The attributes that are mass assignable.
	 *
	 * @var array
	 */
	protected $fillable = [
		// 'username','phone','chip','code_secret','fullName','password', 'email', 'userid', 'zoneid', 'ip', 'hash'
		'UserName',
		'fullName',
		'Password',
		'Email',
		'UserID',
		'Role',
		'IPAddress',
		'phone',
		'code_secret',
		'chip',
		'created_at',
		'luot_quay'
	];

	/**
	 * The attributes that should be hidden for arrays.
	 *
	 * @var array
	 */
	protected $hidden = [
		// 'password', 'userid', 'hash'
		'Password',
		'Role'
	];

	protected static function boot()
	{
		parent::boot();

		static::creating(function ($model) {
			if (empty($model->created_at)) {
				$model->created_at = $model->RegTime;
			}
		});
	}


	public function getBalanceAtribute()
	{
		return $this->Money;
	}

	public function getZoneidAtribute()
	{
		return $this->Role;
	}

	public function getUsernameAtribute()
	{
		return $this->UserName;
	}

	public function getPasswordAtribute()
	{
		return $this->Password;
	}

	public function balance()
	{
		return $this->Money;
	}

	public function zoneid()
	{
		return $this->Role;
	}

	public function username()
	{
		return $this->UserName;
	}

	public function password()
	{
		return $this->Password;
	}

	public function getAuthPassword()
	{
		return ($this->Password);
	}

	/**
	 * Get the characters for the account.
	 */
	public function characters()
	{
		return $this->hasMany(Character::class, 'userid', 'UserID');
	}
}
