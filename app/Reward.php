<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Reward extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'account';	
	
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_reward';
		
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'uid', 'type', 'amount', 'item','rid','zoneid'
    ];
	
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'item' => 'array',
    ];	
}
