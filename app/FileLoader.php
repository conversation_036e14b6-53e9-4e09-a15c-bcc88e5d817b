<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use File;
use Config;

class FileLoader extends Model
{
	public static function winwheel_config($data){
		
		$data = json_decode($data);
		$path = config_path();
		if (is_null($path)){
            return;
        }
        $use_chip = isset($data->use_chip);
     	$xxx = $use_chip?1:0;

        $file = "{$path}/luckywinwheel.php";
        $conf = "\n'chip'=>$data->chip,\n";
        $conf.= "'coin'=>$data->coin,\n";
        $conf.= "'use_chip'=>$xxx,\n";
        $conf.= "'key'=>'$data->key',\n";
        $conf.= "'free_count'=>$data->free_count,\n";
        $conf.= "'maxRand'=>$data->maxRand,\n";
        $conf.= "'items'=>[\n";
        //for($i=0;$i<12;$i++){
        foreach ($data->items as $i => $value) {
     
        	
        	$reward = $value->reward;
        	$reward_item= $reward->items[0];


        	$conf.= "[\n";
			$conf.= "\t'key'=>$value->key,\n";
			$conf.= "\t'id'=>$value->id,\n";
			$conf.= "\t'name'=>'$value->name',\n";
			$conf.= "\t'reward'=>[\n";
			$conf.= "\t\t'items'=>[";
			$conf.= "'$reward_item',";
			$conf.= "],\n";
			$conf.= "\t\t'coin'=>$reward->coin,\n";
			$conf.= "\t\t'chip'=>$reward->chip\n";
			$conf.= "\t],\n";		
			$conf.= "\t'StarValue'=>$value->StarValue,\n";
			$conf.= "\t'EndValue'=>$value->EndValue,\n";		
			$conf.= "],\n";
        }
        $conf.="]\n";
        
      File::put($file, '<?php return [' .  $conf . '];');   
     
	}
    public static function db_config($servers, $group = 'database')
    {
        $path = config_path();

        if (is_null($path))
        {
            return;
        }

        $file = "{$path}/{$group}.php";
		
		$conf = "
			'default' => env('DB_CONNECTION', 'mysql'),
		";
		
		$conf .= "
			'connections' => [
		";
		
		$conf .= "
				'account' => [
					'driver' => 'mysql',
					'host' => env('DB_ROOT_HOST', '127.0.0.1'),
					'port' => env('DB_ROOT_PORT', '3388'),
					'database' => env('DB_ROOT_DATABASE', 'forge'),
					'username' => env('DB_ROOT_USERNAME', 'forge'),
					'password' => env('DB_ROOT_PASSWORD', ''),
					'unix_socket' => env('DB_ROOT_SOCKET', ''),
					'charset' => 'utf8',
					'collation' => 'utf8_unicode_ci',
					'prefix' => '',
					'strict' => true,
					'engine' => null,
				],
			";
		
		foreach($servers as $server) {
			$conf .= "
				'$server->Id' => [
					'driver' => 'mysql',
					'host' => '$server->ServerURL',
					'port' => env('DB_SERVER_PORT', '3388'),
					'database' => '$server->DatabaseName',
					'username' => env('DB_SERVER_USERNAME', 'forge'),
					'password' => env('DB_SERVER_PASSWORD', ''),
					'unix_socket' => env('DB_SERVER_SOCKET', ''),
					'charset' => 'utf8',
					'collation' => 'utf8_unicode_ci',
					'prefix' => '',
					'strict' => false,
					'engine' => null,
				],
			";
		}
		
		$conf .= "
			],
		";
		
		$conf .= "
			'migrations' => 'migrations',
		";

		$conf .= "
			'redis' => [

				'client' => 'predis',

				'default' => [
					'host' => env('REDIS_HOST', '127.0.0.1'),
					'password' => env('REDIS_PASSWORD', null),
					'port' => env('REDIS_PORT', 6379),
					'database' => 0,
				],

			],
		";			
			
        File::put($file, '<?php return [' . $conf . '];');
    }
	
    public static function ip_config($ipv4, $group = 'ipban')
    {
        $path = config_path();

        if (is_null($path))
        {
            return;
        }

        $file = "{$path}/{$group}.php";
		
		$conf = "
			'ip' => [
		";
		
		foreach($ipv4 as $ip) {
			$conf .= "
				'$ip',";			
		}

		$conf .= "
			],
		";

		File::put($file, '<?php return [' . $conf . '];');
	}
	
    public static function general_config($setting, $group = 'mu')
    {
        $path = config_path();

        if (is_null($path))
        {
            return;
        }

        $file = "{$path}/{$group}.php";
		
		$platform_name = Config::get('mu.platform.name');
		$zoneid = Config::get('mu.alpha.zoneid');
		$alpha_date = Config::get('mu.alpha.date');
		$currency_name = Config::get('mu.currency.name');
		$rmb = Config::get('mu.card_month.rmb');
		$money = Config::get('mu.card_month.money');
		
		$promotion_zoneid = Config::get('mu.promotion.zoneid');
		$promotion_percent = Config::get('mu.promotion.percent');
		$promotion_requires = Config::get('mu.promotion.require');
		$promotion_date = Config::get('mu.promotion.date');
		$promotion_weeklys = Config::get('mu.promotion.weekly');
		$promotion_limit_number = Config::get('mu.promotion.limit.number');
		$promotion_limit_percent = Config::get('mu.promotion.limit.percent');
		
		$recharge_date = Config::get('mu.recharge.date');
		$exchange_rates = Config::get('mu.exchange');
		$note = Config::get('note');
		$webshop = Config::get('webshop');
		$reward_server=Config::get('reward_server');

		$ruturnChart = Config::get('mu.chrreturn');

		$alpha_date_start = $alpha_date[0];
		$alpha_date_end = $alpha_date[1];		
		
		$promotion_date_start = $promotion_date[0];
		$promotion_date_end = $promotion_date[1];
		
		$exchange = $setting['exchange'];
		$promotion = $setting['promotion'];
		
		$recharge_date_start = $setting['date_start'];
		$recharge_date_end = $setting['date_end'];		

		$exchange_cfg = '';
		foreach($exchange_rates as $i => $exchange_rate) {
			$exchange_cfg .= "$i => $exchange_rate,";
		}			
		
		$require = '';
		foreach($promotion_requires as $i => $promotion_require) {
			$require .= "$i => $promotion_require,";
		}		
		
		$weeky = '';
		foreach($promotion_weeklys as $i => $promotion_weekly) {
			$weeky .= "$i => $promotion_weekly,";
		}
		
		$note = $setting['note'];
		$webshop = $setting['webshop'];

		$ruturnChart= $setting['chrreturn'];

		$reward_server = isset($setting['reward_server'])?implode('|',$setting['reward_server']):'-1';
		$conf = "
			'platform' => [
				'name' => '$platform_name',
			],
			'alpha' => [
				'zoneid' => $zoneid,
				'date' => [
					'$alpha_date_start',
					'$alpha_date_end'
				],
			],
			'currency' => [
				'name' => '$currency_name',
			],
			'card_month' => [
				'rmb' => $rmb,
				'money' => $money,
			],	
			'recharge' => [
				'exchange' => $exchange,
				'promotion' => $promotion,
				'date' => [
					'$recharge_date_start',
					'$recharge_date_end'
				],
			],				
			'exchange' => [
				$exchange_cfg
			],	
			'promotion' => [
				'zoneid' => $promotion_zoneid,
				'percent' => $promotion_percent,
				'require' => [
					$require
				],
				'date' => [
					'$promotion_date_start',
					'$promotion_date_end'
				],
				'weekly' => [
					$weeky
				],
				'limit' => [
					'number' => $promotion_limit_number,
					'percent' => $promotion_limit_percent,
				]
				
			],
			'note'	=> '$note',
			'webshop'	=> $webshop,
			'reward_server'=>'$reward_server',
			'chrreturn'=>$ruturnChart,
		";	

		File::put($file, '<?php return [' . $conf . '];');
	}	
}
