<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

use App\Helper;

class Giftcode_Log extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'account';	
	
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_giftcode_log';
		
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'uid', 'rid', 'zoneid', 'giftcode', 'groupid'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'uid'
    ];
}
