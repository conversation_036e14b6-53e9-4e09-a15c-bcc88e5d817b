<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Parser\Xml\Reader;
use <PERSON><PERSON>\Parser\Xml\Document;

use Carbon\Carbon;
use GuzzleHttp;
use Config;
use Cache;

use App;
use App\Giftcode_Log;
use App\Giftcode;
use App\Server;
use DB;
use Auth;
use App\Account;

use App\t_rewardCuston_rid;
use App\t_rewardCuston;
use App\t_rewardCuston_log;

use App\WebshopCatelog;
use App\t_luckywinwheel;
use App\Recharge;
use App\Reward;

use App\WC_Pool_Log;
use App\WC_Pool;

use App\t_ranks_list;
use App\Daily;



use Illuminate\Support\Str;

class Helper extends Model
{


	public static function checkGiftUse($code)
	{

		$account = Auth::user();


		$giftcode = Giftcode::where('code', '["' . $code . '"]')->orderBy('id', 'desc')->first();

		if ($giftcode) {

			$dt = Carbon::now();
			$dayOut = Carbon::now($giftcode->period);

			if ($giftcode->period > 0) {

				$dt = Carbon::parse($giftcode->created_at);
				$date = $dt->addDays($giftcode->period);
				$endOfDay = $date->endOfDay();
				$period = $endOfDay->timestamp;


				if (Carbon::now()->timestamp >= $period) {
					return true;
				}
			}

			if ($giftcode->type == 2) {
				$accounts = explode(',', $giftcode->accounts);
				if (!in_array($account->UserName, $accounts)) {
					return true;
				}
			}
			if ($giftcode->limit > 0) {
				$giftcode_log = Giftcode_Log::where('giftcode', $code)->count();
				if ($giftcode_log >= $giftcode->limit) {
					return true;
				}
			}

			if ($giftcode->zoneid > 0) {
				if ($giftcode->zoneid != $account->Role) {
					return true;
				}
			}


			$role = DB::connection($account->Role)->table('t_roles')->where('userid', $account->userid)->orderBy('lasttime', 'desc')->first();



			if ($giftcode->multiple == 1) {
				$check_exist = Giftcode_Log::where('groupid', $giftcode->id)
					->where('uid', $account->UserID)
					->where('zoneid', $account->Role)
					->exists();
			} elseif ($giftcode->type == 1) {
				$check_exist = Giftcode_Log::where('giftcode', $code)
					->where('uid', $account->UserID)
					->where('zoneid', $account->Role)
					->exists();
			} elseif ($giftcode->type == 2) {

				$check_exist = Giftcode_Log::where('giftcode', $code)
					->where('uid', $account->UserID)
					//->where('zoneid', $account->Role)
					->exists();
			} else {

				$check_exist = Giftcode_Log::where('giftcode', $code)
					->where('uid', $account->UserID)
					->where('rid', $role->rid)
					->where('zoneid', $account->Role)
					->exists();
			}


			if ($check_exist !== FALSE) {
				return true;
			}
		}
		return false;
	}

	public static function isReward_custom($id_rw, $uid, $rid, $type_rw, $top, $zoneid)
	{

		//$account = Auth::user();
		$t_rewardCuston = t_rewardCuston::where('id', '=', $id_rw)->first();
		$t_rewardCuston_log_count = t_rewardCuston_log::where('rw_id', '=', $id_rw)
			->where('uid', '=', $uid)
			->where('rid', '=', $rid)
			->where('type_rw', '=', $type_rw)
			->where('zoneid', '=', $zoneid)
			->count();
		return  $t_rewardCuston_log_count > 0 ? true : false;
	}



	// card day

	public static function sendMailItem($items = array(), $rname = false, $rid = false, $subject = false, $content = false)
	{

		$account = Auth::user();
		$zoneid = $account->Role;
		$dt = Carbon::now();

		if (!$rname) {
			return false;
		}
		if (!$rid) {
			return false;
		}
		if (!$subject) {
			return false;
		}
		if (!$content) {
			return false;
		}

		$mailid = DB::connection($zoneid)->table('t_mail')
			->insertGetId([
				'senderrid' => 0,
				'senderrname' => 'GM',
				'sendtime' => $dt,
				'receiverrid' => $rid,
				'reveiverrname' => $rname,
				'subject' => $subject,
				'content' => $content
			]);
		if ($mailid && count($items) > 0) {
			$custom_items = [];
			foreach ($items as $value) {
				$goods = explode(',', $value);
				$custom_items[] = [
					'mailid' => $mailid,
					'goodsid' => $goods[0],
					'gcount' => $goods[1],
					'binding' => $goods[2],
					'forge_level' => $goods[3],
					'appendproplev' => $goods[4],
					'lucky' => $goods[5],
					'excellenceinfo' => $goods[6],
				];
			}
			$insertMail = DB::connection($zoneid)->table('t_mailgoods')->insert($custom_items);
			if ($insertMail) {
				return true;
			} else {
				return false;
			}
		} else {

			return false;
		}
	}


	public static function mobiCarDaily($uid, $amount = false, $code = false)
	{


		$dt = Carbon::now();


		$count_amount = 0;
		$config = Config("daily.mobicard");
		$on = $config['enable'];
		$card_count = $config['card_count'];
		$card_rate = $config['card_rate'];
		$items = $config['items'];


		$account = Account::where('id', $uid)->first();

		if ($items) {

			$role = DB::connection($account->Role)->table('t_roles')->where('userid', $account->userid)->orderBy('lasttime', 'desc')->first();
			$title = __('usercp.dailycard.mail.title', ['x' => $card_rate]);
			$content = __('usercp.dailycard.mail.content', ['code' => $code, 'amount' => $amount]);
			//self::sendMailItem($items,$role->rname,$role->rid,$title,$content);

			$mailid = DB::connection($account->Role)->table('t_mail')
				->insertGetId([
					'senderrid' => 0,
					'senderrname' => 'GM',
					'sendtime' => $dt,
					'receiverrid' => $role->rid,
					'reveiverrname' => $role->rname,
					'subject' => $title,
					'content' => $content
				]);

			if ($mailid && count($items) > 0) {
				$custom_items = [];
				foreach ($items as $value) {
					$goods = explode(',', $value);
					$custom_items[] = [
						'mailid' => $mailid,
						'goodsid' => $goods[0],
						'gcount' => $goods[1],
						'binding' => $goods[2],
						'forge_level' => $goods[3],
						'appendproplev' => $goods[4],
						'lucky' => $goods[5],
						'excellenceinfo' => $goods[6],
					];
				}
				$insertMail = DB::connection($account->Role)->table('t_mailgoods')->insert($custom_items);
			}
		}




		if ($on == 0) {
		} else {


			$check_log  = Daily::where('uid', $account->UserID)
				->where('type', 'plus')
				->whereraw("date(created_at) = CURDATE()")
				->count();
			if ($check_log >= $card_count) {
			} else {
				$in_log  = 	Daily::create(array(
					'code' => $code,
					'type' => 'plus',
					'uid' => $account->UserID,
					'amount' => $amount * $card_rate,
				));
				if ($in_log) {
					$account->balance = $account->balance + ($amount * $card_rate);
					$account->save();
				}
			}
		}
	}
	public static function getStringPlay($id)
	{
		$account = Auth::user();
		$checklog = WC_Pool_Log::where('userId', '=', $account->UserID)->where('id_pool', '=', $id)->first();
		if ($checklog) {
			return $checklog;
		}
		return '';
	}
	public static function iswcPlay($id)
	{
		$getPool = WC_Pool::where('id', $id)->first();
		if ($getPool) {
			$account = Auth::user();
			$checklog = WC_Pool_Log::where('userId', '=', $account->UserID)->where('id_pool', '=', $id)->count();
			if ($checklog > 0) {
				return true;
			} else {
				return false;
			}
		}
		return false;
	}

	public static function wcCheckTime($endTime)
	{

		$dt = Carbon::now();
		$dt_end = new Carbon($endTime);
		if ($dt->timestamp < $dt_end->timestamp) {
			return true;
		}
		return false;
	}

	public static function getInfo()
	{
		$content = Config('infos.content');
		echo $content;
	}
	public static function checklifecount($rid)
	{

		$config = Config('reset.config');
		$lifecount = $config['lifecount'];
		$account = Auth::user();
		$rid = self::getRid();
		$getchangelifecount = DB::connection($account->Role)->table('t_roles')->where('changelifecount', '>=', $lifecount)->count();
		if ($getchangelifecount > 0) {
			return true;
		} else {
			return false;
		}
	}
	public static function getRanksByUser($id, $type = 'day', $date = false)
	{

		$account = Auth::user();
		$rank = self::getRanksbylifecount($id, $type, $date);
		$top = 0;
		$getCharter = DB::connection($account->Role)->table('t_roles')
			->select('rid')
			->where('userid', $account->userid)
			->orderBy('lasttime', 'desc')->first();
		if (!$rank) {
			return $top;
		}
		foreach ($rank as $key => $value) {
			if ($value->rid == $getCharter->rid) {
				$top = $key + 1;
				break;
			}
		}
		return $top;
	}
	public static function getRanksbylifecount($id, $type = 'day', $date = false)
	{

		//$config =Config('reset.config');
		//$lifecount = $config['lifecount'];
		//$limit = $config['limit'];

		$getRankGroup = t_ranks_list::where('id', $id)->first();
		$limit = $getRankGroup->limit;
		$lifecount = $getRankGroup->lifecount;
		$lifecount_limit = $getRankGroup->lifecount_limit;

		$account = Auth::user();
		$dt = $date ? Carbon::parse($date) : Carbon::now();


		$top = DB::connection($account->Role)->table('resetlevel')
			->where('resetlevel.changelifecount', '<', $lifecount)
			->where('resetlevel.changelifecount', '>=', $lifecount_limit)
			->leftJoin('t_roles', 'resetlevel.rid', 't_roles.rid')
			->select(DB::raw('count(*) as total,MAX(resetlevel.date) as maxdate,resetlevel.rid,t_roles.combatforce,t_roles.rname,t_roles.changelifecount,t_roles.`level`,t_roles.occupation'));

		switch ($type) {
			case 'month':

				$top = $top->whereraw('Year(resetlevel.date)=Year("' . $dt->toDateString() . '") AND Month(resetlevel.date)= Month("' . $dt->toDateString() . '")');
				break;
			case 'week':

				$top = $top->whereraw('YEARWEEK(resetlevel.date,5) = YEARWEEK("' . $dt->toDateString() . '",5 )');
				break;
			default:
				$top = $top->whereDate('resetlevel.date', '=', $dt->toDateString());

				break;
		}
		return  $top->groupBy('resetlevel.rid')
			->orderBy('total', 'DESC')
			->orderBy('maxdate', 'ASC')->limit($limit)->get();
	}
	public static function checkResetCount() {}
	public static function getRewardsRankByZoneid() {}

	public static function isReward()
	{

		$account = Auth::user();
		$rewards =  config('rewards.reward_' . $account->Role);
		if (!$rewards) {
			$rewards =  config('rewards.reward_def');
		}
		$date = $rewards['date'];
		$start = $date[0];
		$end = $date[1];

		$dt = Carbon::now();
		$source1  = new Carbon($start);
		$source2  = new Carbon($end);

		if ($dt->timestamp >= $source1->timestamp  && $dt->timestamp <= $source2->timestamp) {
			return true;
		} else {
			return false;
		}
	}
	public static function getRawards()
	{

		$account = Auth::user();
		$rewards =  config('rewards.reward_' . $account->Role);
		if (!$rewards) {
			$rewards =  config('rewards.reward_def');
		}

		return $rewards['recharge'];
	}

	public static function getRaward()
	{

		$account = Auth::user();
		$rewards =  config('rewards.reward_' . $account->Role);

		if (!$rewards) {
			$rewards =  config('rewards.reward_def');
		}
		$rewards = $rewards['recharge'];
		$rq =  Request();
		$amount = $rq->amount;

		return $rewards[$amount];
	}
	public static function checkValueReward()
	{

		$rq =  Request();
		$amount = $rq->amount;

		$account = Auth::user();
		$rewards =  config('rewards.reward_' . $account->Role);
		if (!$rewards) {
			$rewards =  config('rewards.reward_def');
		}
		$rewards = $rewards['recharge'];

		if (array_key_exists($amount, $rewards)) {
			return true;
		} else {
			return false;
		}
	}
	public static function checkReward($amount)
	{

		$account = Auth::user();
		$rewards =  config('rewards.reward_' . $account->Role);
		if (!$rewards) {
			$rewards =  config('rewards.reward_def');
		}

		$date = $rewards['date'];

		$account = Auth::user();
		$check = Reward::where('amount', $amount)
			->where('uid', $account->UserID)
			->whereDate('created_at', '>=', $date[0])
			->whereDate('created_at', '<=', $date[1])
			->count();
		if ($check > 0) {
			return true;
		} else {
			return false;
		}
	}
	public static function getRecharge()
	{


		$account = Auth::user();
		$rewards =  config('rewards.reward_' . $account->Role);
		if (!$rewards) {
			$rewards =  config('rewards.reward_def');
		}
		$date = $rewards['date'];
		$start = $date[0];
		$end = $date[1];
		$payment = 0;


		$reward_from = Carbon::parse($start);
		$reward_to = Carbon::parse($end);

		$method = Config::get('recharge.method');

		$RechargeNumber = Recharge::where('uid', $account->UserID)->where('status', 1)
			->whereraw("DATE(created_at) >=DATE('" . $start . "')")->whereraw("DATE(created_at) <=DATE('" . $end . "')")->sum('amount');

		if ($method == 3) {
			$payment = Payment::where('uid', $account->UserID)->where('status', 1)->whereDate('created_at', '>=', $reward_from->toDateString())->whereDate('created_at', '<=', $reward_to->toDateString())->sum('amount');
			$RechargeNumber = $RechargeNumber + $payment;
		}



		return $RechargeNumber;
	}

	public static function getNumberWhell()
	{

		$account = Auth::user();
		$luckywinwheel_config = Config('luckywinwheel');
		$free = 0;
		$wheelCoin = 0;
		$wheelChip = 0;

		$getCount = t_luckywinwheel::where('uid', $account->UserID)->where('type', 'free')->whereraw("date(created_at) =DATE(CURDATE())")->count();

		if ($getCount < $luckywinwheel_config['free_count']) {
			$free = $luckywinwheel_config['free_count'] -  $getCount;
		}

		$wheelCoin =  $account->Money / $luckywinwheel_config['coin'];
		$wheelChip =  $account->chip / $luckywinwheel_config['chip'];


		return array(
			'free' => (int)$free,
			'coin' => (int)$wheelCoin,
			'chip' => (int)$wheelChip
		);
	}


	public static function getSegmentsWinWheel()
	{
		$item = Config('luckywinwheel.items');
		return count($item);
	}
	public static function getLuckyWinWheelItem($key)
	{
		$item = Config('luckywinwheel.items');
		return $item[$key];
	}
	public static function getItemKey($number)
	{
		$items = Config('luckywinwheel.items');
		foreach ($items as $key => $value) {
			if ($value['StarValue'] <= $number  &&  $number <= $value['EndValue']) {
				return $key;
				break;
			}
			continue;
		}
		return $number;
	}
	public static function luckywinwheelRandom()
	{
		$rand_value = Config('luckywinwheel.maxRand');
		$rand_value_xxxxx = rand(1, $rand_value);
		return self::getItemKey($rand_value_xxxxx);
	}

	public static function luckyWinWheelItems()
	{
		$items = Config('luckywinwheel.items');
		return json_encode($items);
	}
	public  static function  setChip($amount)
	{
		$Chip = Config('chip');
		if ($Chip['open'] == 1) {

			$pValue = $Chip['p']['value'];
			$pGive = $Chip['p']['give'];
			$account = Auth::user();
			$chip = ($amount / $pValue) * $pGive;
			$account->chip = $account->chip + $chip;
			$update = $account->save();

			if ($update) {
				return $chip;
			}
			return false;
		}
		return false;
	}

	public static function getChip()
	{
		$Chip = Config('chip');
		if ($Chip['open'] == 1) {
			$account = Auth::user();
			return $account->chip;
		}
		return 0;
	}

	public static function setRid($rid = '')
	{
		$request  = Request();
		if ($request->rid) {
			$rid = $request->rid;
		}
		session(['selected_rid' => $rid]);
	}
	public static function getRid()
	{
		$value = session('selected_rid');
		return $value;
	}
	public static function getGetNameWebShopcat($id)
	{
		$cat = WebshopCatelog::where('id', $id)->first();
		if (!$cat) {
			return __('admincp.webshop.cat.unknown');
		}
		return $cat->name;
	}
	public static function getWebShopCats()
	{
		$cats = WebshopCatelog::get();
		return $cats;
	}
	public static function getNameTypeRewardCustom($key)
	{
		$type = $list_type_reward = Config('rewardsC.reward_config');
		return $type[$key];
	}
	public static function getListCharters()
	{

		$account = Auth::user();
		$zoneid =  $account->Role;
		$charters = DB::connection($zoneid)
			->table('t_roles')
			->where('userid', "ZT" . $account->UserID)
			->where('isdel', 0)
			->orderBy('lasttime', 'desc')
			->get();
		return $charters;
	}
	public static function getRewardCustonLog($id_rw, $rid, $type_rw, $top, $zoneid)
	{

		$account = Auth::user();
		$t_rewardCuston = t_rewardCuston::where('id', '=', $id_rw)->first();
		$t_rewardCuston_log_count = t_rewardCuston_log::where('rw_id', '=', $id_rw)
			->where('uid', '=', $account->UserID)->where('rid', '=', $rid)->where('type_rw', '=', $type_rw)->where('zoneid', '=', $zoneid)->count();
		return  $t_rewardCuston_log_count;
	}

	/**
	 * @int $stt
	 * @string $msg
	 * @string $uid
	 */
	public static function create_api($stt, $msg, $uid, $ip, $indulge = 1, $uname, $sso, $person)
	{
		$data = array(
			'retcode' => $stt,
			'retmsg' => $msg,
			'data' => array(
				'uid' => $uid,
				'ipv4' => $ip,
				'indulge' => $indulge,
				'uname' => $uname,
				'KL_SSO' => $sso,
				'KL_PERSON' => $person
			)
		);

		return $data;
	}

	/**
	 * @date $date
	 */
	public static function getDateTime($date)
	{
		$dt = Carbon::parse($date);

		$output = $dt->format('H:i d/m/Y');

		return $output;
	}

	/**
	 * @date $seconds
	 */
	public static function getHourLogin($seconds)
	{

		$output = gmdate('H:i:s', $seconds);

		return $output;
	}

	/**
	 * @int $type
	 * @string $serial
	 * @int $code
	 * @int $id
	 */
	public static function vippay_api($type, $serial, $code, $id)
	{

		$vippay = Config::get('recharge.vippay');

		$merchant_id = $vippay[$id]['merchant_id'];
		$api_user = $vippay[$id]['user'];
		$api_password = $vippay[$id]['password'];

		$client = new GuzzleHttp\Client();

		$res = $client->request('POST', "https://vippay.vn/api/api/card", [
			'form_params' => [
				'merchant_id' => $merchant_id,
				'pin' => $code,
				'seri' => $serial,
				'card_type' => $type
			],
			'verify' => false,
			'auth' => [$api_user, $api_password]
		]);

		$data = $res->getBody();

		$data = json_decode($data);

		return $data;
	}


	public static function vippay_api_new($serial, $code, $amount, $transaction_id, $card_type = 2)
	{

		$id = Config('mobiAutoPay.id');
		$vippay = Config::get('recharge.vippay');
		$merchant_id = $vippay[$id]['merchant_id'];
		$api_user = $vippay[$id]['user'];
		$api_password = $vippay[$id]['password'];

		$client = new GuzzleHttp\Client();

		$fields = array(
			'merchant_id' => $merchant_id,
			'pin' =>  $code,
			'seri' =>  $serial,
			'card_type' => $card_type,
			'msg' => '111111111',
			'transaction_id' => $transaction_id,
			'info_card' => $amount
		);

		$ch = curl_init("https://api.skyvina.net/api/card");

		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_TIMEOUT, 120);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_USERPWD, 'cb664f0f3af443f09204eff31df3e029:eb9e8898e1dc49ca94a58663554e25bf');
		$response = curl_exec($ch);
		$err = curl_error($ch);
		curl_close($ch);
		$data = json_decode($response);
		return $data;
	}

	/**
	 * @int $type
	 * @string $serial
	 * @int $code
	 * @int $id
	 */
	public static function baokim_api($type, $serial, $code, $id)
	{

		$baokim = Config::get('recharge.baokim');

		$api_http_usr = $baokim[$id]['http_usr'];
		$api_http_pwd = $baokim[$id]['http_pwd'];

		$merchant_id = $baokim[$id]['merchant_id'];
		$api_user = $baokim[$id]['user'];
		$api_password = $baokim[$id]['password'];
		$secure_code = $baokim[$id]['secure_code'];

		$client = new GuzzleHttp\Client();

		switch ($type) {
			case 1:
				$card_type = 'VIETEL';
				break;
			case 2:
				$card_type = 'MOBI';
				break;
			case 3:
				$card_type = 'VINA';
				break;
			case 4:
				$card_type = 'GATE';
				break;
		}

		$form_params = [
			'merchant_id' => $merchant_id,
			'api_username' => $api_user,
			'api_password' => $api_password,
			'transaction_id' => Carbon::now()->timestamp,
			'card_id' => $card_type,
			'pin_field' => $code,
			'seri_field' => $serial,
			'algo_mode' => 'hmac'
		];

		ksort($form_params);

		$data_sign = hash_hmac('SHA1', implode('', $form_params), $secure_code);

		$form_params['data_sign'] = $data_sign;

		$res = $client->request('POST', "https://www.baokim.vn/the-cao/restFul/send", [
			'form_params' => $form_params,
			'verify' => false,
			'http_errors' => false,
			'auth' => [$api_http_usr, $api_http_pwd]
		]);

		return $res;
	}

	/**
	 * @int $type
	 * @string $serial
	 * @int $code
	 * @int $id
	 */
	public static function nganluong_atm() {}
	public static function nganluong_api($type, $serial, $code, $id, $username = NULL)
	{

		$nganluong = Config::get('recharge.nganluong');

		$merchant_id = $nganluong[$id]['merchant_id'];
		$api_user = $nganluong[$id]['user'];
		$api_password = $nganluong[$id]['password'];
		$api_private = $nganluong[$id]['private'];

		$client = new GuzzleHttp\Client();

		switch ($type) {
			case 1:
				$card_type = 'VIETTEL';
				break;
			case 2:
				$card_type = 'VMS';
				break;
			case 3:
				$card_type = 'VNP';
				break;
			case 4:
				$card_type = 'GATE';
				break;
		}

		if ($api_private == 1) {
			$ref = $username;
		} else {
			$ref = Carbon::now()->timestamp;
		}

		$form_params = [
			'func'					=> 'CardCharge',
			'version'				=> '2.0',
			'merchant_id'			=> $merchant_id,
			'merchanzt_account'		=> $api_user,
			'merchant_password'		=> md5($merchant_id . '|' . $api_password),
			'pin_card'				=> $code,
			'card_serial'			=> $serial,
			'type_card'				=> $card_type,

			'ref_code'				=> $ref,
			'client_fullname'		=> '',
			'client_email'			=> '',
			'client_mobile'			=> ''
		];

		if ($api_private == 1) {
			$url = 'http://xpay.vimo.vn/mobile_card.api.post.v2.php';
		} else {
			$url = 'https://www.nganluong.vn/mobile_card.api.post.v2.php';
		}

		$res = $client->request('POST', $url, [
			'form_params' => $form_params,
			'verify' => false,
		]);

		$data = $res->getBody();

		return $data;
	}

	/**
	 * @int $type
	 * @string $serial
	 * @int $code
	 * @int $id
	 */
	public static function hanoipay_api($type, $serial, $code, $id)
	{

		$hanoipay = Config::get('recharge.hanoipay');

		$api_key = $hanoipay[$id]['access_key'];
		$secure_code = $hanoipay[$id]['secure_code'];

		$client = new GuzzleHttp\Client();

		switch ($type) {
			case 1:
				$card_type = 'VIETTEL';
				break;
			case 2:
				$card_type = 'MOBI';
				break;
			case 3:
				$card_type = 'VINA';
				break;
			case 4:
				$card_type = 'GATE';
				break;
		}

		$form_params = [
			'type' => $card_type,
			'transid' => Carbon::now()->timestamp,
			'accesskey' => $api_key,
			'pin' => $code,
			'serial' => $serial
		];

		ksort($form_params);

		$data_sign = md5(implode('', $form_params) . $secure_code);

		$form_params['signature'] = $data_sign;

		$res = $client->request('POST', "http://card.hanoipay.com/api/card/v2/topup", [
			'form_params' => $form_params,
			'verify' => false
		]);

		$data = $res->getBody();

		$data = json_decode($data);

		return $data;
	}

	public static function tmpay_api($code, $id)
	{

		$tmpay = Config::get('recharge.tmpay');

		$merchant_id = $tmpay[$id]['merchant_id'];

		$client = new GuzzleHttp\Client();

		$res = $client->request('GET', "https://www.tmpay.net/TPG/backend.php", [
			'query' => [
				'merchant_id' => $merchant_id,
				'password' => $code,
				'resp_url' => url('api/recharge'),
			],
			'verify' => false
		]);

		$data = $res->getBody();

		return $data;
	}

	/**
	 * @int $type
	 * @string $serial
	 * @int $code
	 * @int $id
	 */
	public static function maxpay_api($type, $serial, $code, $id)
	{

		$maxpay = Config::get('recharge.maxpay');

		$merchant_id = $maxpay[$id]['merchant_id'];
		$secure_code = $maxpay[$id]['secure_code'];

		$client = new GuzzleHttp\Client();

		switch ($type) {
			case 1:
				$card_type = 'VTE';
				break;
			case 2:
				$card_type = 'VMS';
				break;
			case 3:
				$card_type = 'VNP';
				break;
			case 4:
				$card_type = 'FPT';
				break;
		}

		$form_params = [
			'merchant_id' => $merchant_id,
			'merchant_txn_id' => Carbon::now()->timestamp,
			'pin' => $code,
			'seri' => $serial,
			'card_type' => $card_type,
		];

		ksort($form_params);

		$data_sign = hash_hmac('SHA1', implode('|', $form_params), $secure_code);

		$form_params['checksum'] = $data_sign;

		$res = $client->request('POST', "https://maxpay.vn/apis/card/charge", [
			'form_params' => $form_params,
			'verify' => false
		]);

		$data = $res->getBody();

		$data = json_decode($data);

		return $data;
	}

	/**
	 * @int $type
	 * @string $serial
	 * @int $code
	 * @int $id
	 */
	public static function bapvip_api($type, $serial, $code, $id, $username = NULL, $ip = NULL)
	{

		$bapvip_api = Config::get('recharge.bapvip');

		$client_id = $bapvip_api[$id]['client_id'];
		$service_id = $bapvip_api[$id]['service_id'];
		$service_key = $bapvip_api[$id]['service_key'];

		$client = new GuzzleHttp\Client();

		switch ($type) {
			case 1:
				$card_type = 'viettel';
				break;
			case 2:
				$card_type = 'mobifone';
				break;
			case 3:
				$card_type = 'vinaphone';
				break;
			case 4:
				$card_type = 'gate';
				break;
		}

		$dt = Carbon::now();

		$order_id = md5($username . $dt->timestamp);

		$form_params = [
			'UserName' => $username,
			'ClientID' => $client_id,
			'ServiceID' => $service_id,
			'OrderID' => $order_id,
			'Serial' => $serial,
			'PinNumber' => $code,
			'CardType' => $card_type,
			'IP' => $ip,
			'Signature' => md5($client_id . $service_id . $order_id . 0 . 'direct' . $service_key),
		];

		$res = $client->request('POST', "https://bapvip.com/Payment/TopupCard", [
			'form_params' => $form_params,
			'verify' => false
		]);

		$data = $res->getBody();

		$data = json_decode($data);

		return $data;
	}

	public static function hpay_api($type, $serial, $code, $id)
	{

		$hpay = Config::get('recharge.8pay');

		$merchant_id = $hpay[$id]['merchant_id'];
		$secret_key = $hpay[$id]['secret_key'];

		$data_sign = hash('sha256', $merchant_id . $code . $serial . $type . $secret_key);

		$client = new GuzzleHttp\Client();

		$res = $client->request('GET', "https://api.8pay.vn/api/cardcharging2", [
			'query' => [
				'uid' => $merchant_id,
				'mac' => $data_sign,
				'pin' => $code,
				'seri' => $serial,
				'card_type' => $type,
			],
			'verify' => false
		]);

		$data = $res->getBody();

		$data = json_decode($data);

		return $data;
	}
	public static function recard_api($type, $serial, $code, $amount, $id)
	{

		$recard = Config::get('recharge.recard');

		$merchant_id = $recard[$id]['merchant_id'];
		$secret_key = $recard[$id]['secret_key'];

		$data_sign = $merchant_id . $type .  $serial . $code . $amount;

		$signature = hash_hmac('sha256', $data_sign, $secret_key);

		try {
			$client = new GuzzleHttp\Client();

			$res = $client->request('POST', "https://recard.vn/api/card", [
				'form_params' => [
					'merchant_id' => $merchant_id,
					'secret_key' => $secret_key,
					'type' => $type,
					'serial' => $serial,
					'code' => $code,
					'amount' => $amount,
					'signature' => $signature,
				],
				'verify' => false
			]);

			$data = $res->getBody();
		} catch (GuzzleHttp\Exception\RequestException $e) {
			$status = $e->getResponse()->getStatusCode();

			if ($status === 422) {
				$data = $e->getResponse()->getBody()->getContents();
			} else {
				return false;
			}
		}

		$data = json_decode($data);

		return $data;
	}


	/**
	 *
	 */
	public static function getRandomGateway()
	{

		$gateway = Config::get('recharge.multipe');

		$gateway_id = 0;

		if ($gateway !== NULL) {
			$gateway_arr = [];
			foreach ($gateway as $k => $v) {
				if ($v['enable'] == 1) {
					$gateway_arr[] = $k;
				}
			}

			if (count($gateway_arr) > 0) {
				$id = array_rand($gateway_arr);

				$gateway_id = $gateway_arr[$id];
			}
		}

		return $gateway_id;
	}

	/**
	 * @array $gateway
	 */
	public static function getRandomMerchant($gateway)
	{

		$merchant_id = 0;

		if ($gateway !== NULL) {
			$merchant = [];
			foreach ($gateway as $k => $v) {
				if ($v['enable'] == 1) {
					$merchant[] = $k;
				}
			}

			if (count($merchant) > 0) {
				$id = array_rand($merchant);

				$merchant_id = $merchant[$id];
			}
		}

		return $merchant_id;
	}

	/**
	 * @int $gateway
	 */
	public static function getGatewayInfo($gateway)
	{

		switch ($gateway) {
			case 1:
				$gateway_info = Config::get('recharge.baokim');
				break;
			case 2:
				$gateway_info = Config::get('recharge.vippay');
				break;
			case 3:
				$gateway_info = Config::get('recharge.hanoipay');
				break;
			case 4:
				$gateway_info = Config::get('recharge.tmpay');
				break;
			case 5:
				$gateway_info = Config::get('recharge.paymentwall');
				break;
			case 6:
				$gateway_info = Config::get('recharge.nganluong');
				break;
			case 7:
				$gateway_info = Config::get('recharge.maxpay');
				break;
			case 8:
				$gateway_info = Config::get('recharge.bapvip');
				break;
			case 9:
				$gateway_info = Config::get('recharge.superrewards');
				break;
			case 10:
				$gateway_info = Config::get('recharge.8pay');
				break;
			case 14:
				$gateway_info = Config::get('recharge.mtop');
				break;
			case 16:
				$gateway_info = Config::get('recharge.recard');
				break;
			default:
				$gateway_info = NULL;
		}

		return $gateway_info;
	}

	/**
	 *
	 */
	public static function xoso_data()
	{

		$url = Config::get('lottery.url');

		$client = new GuzzleHttp\Client();

		$res = $client->request('GET', $url, [
			'verify' => false
		]);

		$response = $res->getBody()->getContents();

		$document = new Document();
		$reader = new Reader($document);
		$xoso_xml = $reader->extract($response);

		$xoso_parse = $xoso_xml->parse([
			'title' => ['uses' => 'channel.item.title'],
			'description' => ['uses' => 'channel.item.description'],
		]);

		$xoso = collect($xoso_parse);

		$title = $xoso['title'];
		$description = $xoso['description'];

		$output = [
			'date' => self::getDateXoSo($title),
			'result' => self::getResultXoSo($description),
		];

		return $output;
	}

	/**
	 * @string $data
	 */
	public static function getDateXoSo($data)
	{
		$content = explode(' ', $data);
		$date = explode('/', $content[7]);

		return $date;
	}

	/**
	 * @string $data
	 */
	public static function getResultXoSo($data)
	{
		$data = trim($data);
		$data = explode("\n", $data);

		$dacbiet = str_after($data[0], 'ĐB: ');
		$dacbiet = explode(" - ", $dacbiet);

		$giai1 = str_after($data[1], '1: ');
		$giai1 = explode(" - ", $giai1);

		$giai2 = str_after($data[2], '2: ');
		$giai2 = explode(" - ", $giai2);

		$giai3 = str_after($data[3], '3: ');
		$giai3 = explode(" - ", $giai3);

		$giai4 = str_after($data[4], '4: ');
		$giai4 = explode(" - ", $giai4);

		$giai5 = str_after($data[5], '5: ');
		$giai5 = explode(" - ", $giai5);

		$giai6 = str_after($data[6], '6: ');
		$giai6 = explode(" - ", $giai6);

		$giai7 = str_after($data[7], '7: ');
		$giai7 = explode(" - ", $giai7);

		$result = [
			0 => $dacbiet,
			1 => $giai1,
			2 => $giai2,
			3 => $giai3,
			4 => $giai4,
			5 => $giai5,
			6 => $giai6,
			7 => $giai7
		];

		return $result;
	}

	/**
	 * @string $data
	 */
	public static function getXoSo($data)
	{
		$output = implode(' - ', $data);

		return $output;
	}

	/**
	 * @string $data
	 */
	public static function getXoSoDate($data)
	{

		$dt = Carbon::now();

		$output = implode('/', $data) . '/' . $dt->year;

		return $output;
	}

	/**
	 * @int $code
	 */
	public static function getXoSoName($code)
	{

		switch ($code) {
			case 0:
				$name = __('usercp.lottery_no_special');
				break;
			case 1:
				$name =  __('usercp.lottery_no_1');
				break;
			default:
				$name =  __('usercp.lottery_no') . ' ' . $code;
		}

		return $name;
	}

	/**
	 * @int $code
	 */
	public static function getLotteryStatus($code, $reward = 0)
	{

		switch ($code) {
			case 0:
				$status = '';
				break;
			case 1:
				if ($reward > 1) {
					$status = '<span class="text-success">' . __('usercp.lottery_lo_win', ['reward' => $reward]) . '</span>';
				} else {
					$status =  '<span class="text-success">' . __('usercp.lottery_win') . '</span>';
				}
				break;
			case 2:
				$status = '<span class="text-danger">' . __('usercp.lottery_lost')  . '</span>';
				break;
			default:
				$status =  '';
		}

		return $status;
	}

	/**
	 * @int $type
	 */
	public static function getLotteryType($type)
	{

		switch ($type) {
			case 1:
				$output = __('usercp.de');
				break;
			case 2:
				$output = __('usercp.lo');
				break;
			case 3:
				$output = __('usercp.xien2');
				break;
			case 4:
				$output = __('usercp.xien3');
				break;
			case 5:
				$output = __('usercp.xien4');
				break;
			case 6:
				$output = __('usercp.lotruot');
				break;
			default:
				$output = __('usercp.unknown');
		}

		return $output;
	}

	/**
	 * @int $code
	 */
	public static function statusRecharge($code)
	{

		switch ($code) {
			case 0:
				$status = __('admincp.pending');
				break;
			case 1:
				$status = '<span class="text-success">' . __('admincp.card_correct') . '</span>';
				break;
			case 2:
				$status = '<span class="text-danger">' . __('admincp.card_incorrect') . '</span>';
				break;
			default:
				$status = __('admincp.unknown');
		}

		return $status;
	}

	/**
	 * @int $code
	 */
	public static function statusServer($code)
	{

		switch ($code) {
			case 0:
				$status = 'Good';
				break;
			case 1:
				$status = __('admincp.maintenance');
				break;
			case 2:
				$status = 'Good';
				break;
			case 3:
				$status = 'Good';
				break;
			case 4:
				$status = 'Good NEW';
				break;
			default:
				$status = __('admincp.unknown');
		}

		return $status;
	}

	public static function checkDateAlpha()
	{

		$dt = Carbon::now();

		$date = Config::get('mu.alpha.date');

		$output = false;

		if ($dt >= $date[0] && $dt <= $date[1]) {
			$output = true;
		}

		return $output;
	}

	/**
	 * @int $number
	 */
	public static function numberFormat($number)
	{

		if (self::getLangCode() == 'vi-VN') {
			$output = number_format($number, 0, '.', '.');
		} else {
			$output = number_format($number);
		}

		return $output;
	}

	/**
	 * @string $text
	 */
	public static function str_lower($text)
	{

		$data = Str::lower($text);

		return $data;
	}

	/**
	 * @string $text
	 */
	public static function str_upper($text)
	{

		$data = Str::upper($text);

		return $data;
	}

	/**
	 * @int $code
	 */
	public static function getCustomReward($code)
	{

		switch ($code) {
			case 'weapon':
				$output = __('usercp.weapon');
				break;
			case 'set':
				$output = __('usercp.set');
				break;
			case 'wing':
				$output = __('usercp.wing');
				break;
			case 'jewel':
				$output = __('usercp.jewel');
				break;
			case 'pet':
				$output = __('usercp.pet');
				break;
			case 'ring':
				$output = __('usercp.ring');
				break;
			case 'pedan':
				$output = __('usercp.pedan');
				break;
			case 'dk':
				$output = __('usercp.item_dk');
				break;
			case 'dw':
				$output = __('usercp.item_dw');
				break;
			case 'elf':
				$output = __('usercp.item_elf');
				break;
			case 'mg':
				$output = __('usercp.item_mg');
				break;
			case 'sum':
				$output = __('usercp.item_sum');
				break;
			default:
				$output = __('usercp.award');
		}

		return $output;
	}

	/**
	 * @date $time
	 * @int $day
	 */
	public static function getPeriodDay($time, $day)
	{

		if ($day > 0) {
			$dt = Carbon::parse($time);

			$date = $dt->addDays($day);

			$endOfDay = $date->endOfDay();

			$output = $endOfDay->format('H:i d/m/Y');
		} else {
			$output = __('admincp.na');
		}

		return $output;
	}

	/**
	 * @int $limit
	 */
	public static function getLimitUse($limit)
	{

		if ($limit > 0) {
			$output = $limit . ' ' . __('admincp.times');
		} else {
			$output = __('admincp.na');
		}

		return $output;
	}

	/**
	 * @int $zoneid
	 */
	public static function getServer($zoneid)
	{

		$server = Server::where('Id', $zoneid)->first();

		if ($server) {
			$output = $server->ServerName;
		} else {
			$output = __('admincp.unknown');
		}

		return $output;
	}

	/**
	 *
	 */
	public static function getServerList()
	{

		$servers = Server::orderBy('Id', 'desc')->get();

		return $servers;
	}

	/**
	 * @int $zoneid
	 */
	public static function getServerName($zoneid)
	{

		$server = Server::where('Id', $zoneid)->first();

		if ($server) {
			$output = $server->ServerName;
		} else {
			$output = __('admincp.unknown');
		}

		return $output;
	}

	/**
	 * @int $userid
	 * @boolen $accountid
	 */
	public static function getUserName($userid, $accountid = false)
	{
		if (substr($userid, 0, 2) == Config::get('mu.platform.name')) {
			$userid = substr($userid, 2);
		}
		if ($accountid !== FALSE) {
			$account = Account::select('UserName')->where('UserID', $userid)->first();
		} else {
			$account = Account::select('UserName')->where('UserID', $userid)->first();
		}

		if ($account) {
			$output = $account->UserName;
		} else {
			$output = __('admincp.unknown');
		}

		return $output;
	}

	/**
	 * @int $rid
	 * @int $zoneid
	 */
	public static function getCharName($rid, $zoneid)
	{

		$server = Server::where('Id', $zoneid)->first();

		$role = DB::connection($server->Id)->table('t_roles')->where('rid', $rid)->first();

		if ($role) {
			$output = $role->rname;
		} else {
			$output = __('admincp.unknown');
		}

		return $output;
	}

	/**
	 * @int $userid
	 */
	public static function getBalance($userid)
	{

		$account = Account::select('balance')->where('userid', $userid)->first();

		if ($account) {
			$output = self::numberFormat($account->balance);
		} else {
			$output = 0;
		}

		return $output;
	}

	/**
	 * @int $userid
	 * @int $zoneid
	 */
	public static function getMoney($userid, $zoneid)
	{

		$server = Server::where('Id', $zoneid)->first();

		$account = DB::connection($server->Id)->table('t_money')->select('money')->where('userid', $userid)->first();

		if ($account) {
			$output = self::numberFormat($account->money);
		} else {
			$output = 0;
		}

		return $output;
	}

	/**
	 * @string $database
	 */
	public static function getOnlineTotal($database = NULL)
	{

		if ($database !== NULL) {

			$output = Cache::remember("online.total.$database", 5, function () use ($database) {

				$online = DB::connection($database)->table('t_onlinenum')->select('num')->orderBy('Id', 'desc')->first();

				if ($online && $online->num > 0) {
					$num = $online->num;
				} else {
					$num = 1;
				}

				return $num;
			});
		} else {

			$output = Cache::remember('online.total.server', 5, function () {
				$servers = Server::all();

				$total = 0;
				foreach ($servers as $server) {

					$platform_name = Config::get('mu.platform.name');

					$online = DB::connection($server->Id)
						->table('t_roles')
						->where('userid', 'like', $platform_name . '%')
						->whereColumn('lasttime', '>', 'logofftime')
						->count();

					$total += $online;
				}

				return $total;
			});
		}

		return $output;
	}

	/**
	 * @string $database
	 * @boolen $total
	 */
	public static function getOnline($database)
	{

		$output = Cache::remember("online.$database", 5, function () use ($database) {

			$platform_name = Config::get('mu.platform.name');

			$online = DB::connection($database)
				->table('t_roles')
				->where('userid', 'like', $platform_name . '%')
				->whereColumn('lasttime', '>', 'logofftime')
				->count();

			return $online;
		});

		return $output;
	}

	/**
	 * @int $groupid
	 */
	public static function getUsergroup($groupid)
	{

		switch ($groupid) {
			case 0:
				$group = 'Gamer';
				break;
			case 1:
				$group = 'Super Administrator';
				break;
			case 2:
				$group = 'Administrator';
				break;
			case 3:
				$group = 'Moderator';
				break;
			case 9:
				$group = 'Banned';
				break;
			default:
				$group = __('admincp.unknown');
		}

		return $group;
	}

	/**
	 * @int $code
	 */
	public static function getOccupation($code)
	{

		switch ($code) {
			case 0:
				$class = __('admincp.warrior');
				break;
			case 1:
				$class = __('admincp.magician');
				break;
			case 2:
				$class = __('admincp.archer');
				break;
			case 3:
				$class = __('admincp.duel_master');
				break;
			case 4:
				$class = __('admincp.summon');
				break;
			default:
				$class = __('admincp.unknown');
		}

		return $class;
	}

	/**
	 *
	 */
	public static function getGoods()
	{

		$locale = App::getLocale();

		switch ($locale) {
			case 'vi':
				$goods_file = 'Goods_vi.xml';
				break;
			case 'en':
				$goods_file = 'Goods_en.xml';
				break;
			case 'th':
				$goods_file = 'Goods_th.xml';
				break;
			default:
				$goods_file = 'Goods.xml';
		}

		$goods_path = config_path($goods_file);

		$document = new Document();
		$reader = new Reader($document);
		$goods_xml = $reader->load($goods_path);

		$goods_parse = $goods_xml->parse([
			'items' => ['uses' => 'Goods.Item[::ID>id,::Categoriy>category,::Title>name,::ResName>image]'],
		]);

		return collect($goods_parse['items']);
	}

	/**
	 * @array $goods
	 * @string $item
	 */
	public static function getItemGoods($goods, $item)
	{

		$items = $goods->where('id', $item);

		if (count($items) == 0) {
			$items = $goods->where('name', $item);
		}

		$output = $items->first();

		return $output;
	}

	/**
	 * @array $goods
	 * @array $items
	 */
	public static function getNameGoods($goods, $items)
	{

		$goods_id = [];
		foreach ($items as $item) {
			$code = explode(',', $item);
			$goods_id[] = $code[0];
		}

		$name = $goods->whereIn('id', $goods_id);

		return $name->all();
	}

	/**
	 * @int $limit
	 * @string $code
	 */
	public static function getCountRemain($limit, $code, $zoneid = 1)
	{
		// $giftcode = Giftcode_Log::where('giftcode', $code)->count();
		$giftcode = DB::connection($zoneid)->table('z_giftcoderecord')->where('code', $code)->count();

		$remain = $limit - $giftcode;

		$output =  __('admincp.number_giftcode_remain', ['remain' => self::numberFormat($remain)]);

		return $output;
	}

	/**
	 * @array $code
	 */
	public static function getNumberGiftcode($code)
	{

		$giftcode = count($code);

		$output = Helper::numberFormat($giftcode);

		return $output;
	}

	/**
	 * @array $userid
	 */
	public static function getUserID($userid)
	{

		$platform_name = Config::get('mu.platform.name');

		$output = substr($userid, strlen($platform_name));

		return $output;
	}

	/**
	 *
	 */
	public static function getLangCode()
	{

		$locale = App::getLocale();

		switch ($locale) {
			case 'vi':
				$code = 'vi-VN';
				break;
			case 'en':
				$code = 'en-US';
				break;
			case 'th':
				$code = 'th-TH';
				break;
			default:
				$code = 'en-US';
		}

		return $code;
	}

	/**
	 * @int $userid
	 * @int $money
	 * @int $date
	 */
	public static function getGCC($userid, $money, $date)
	{

		$c = Str::random(8);

		$c = Str::upper($c);

		$format = 'jOU81>.fjoeanl3fw16d21f.*' . $c . 'YY' . $userid . '3sl3e5.' . $money . '=' . $date;

		$cc = md5($format);

		$cc = Str::upper($cc);

		$output = substr($cc, 0, 24) . $c;

		return $output;
	}

	/**
	 * @int $code
	 */
	public static function getGateway($code)
	{

		switch ($code) {
			case 1:
				$gateway = 'Bao Kim';
				break;
			case 2:
				$gateway = 'VipPay';
				break;
			case 3:
				$gateway = 'HanoiPay';
				break;
			case 4:
				$gateway = 'TMPay';
				break;
			case 6:
				$gateway = 'Ngan Luong';
				break;
			case 7:
				$gateway = 'Maxpay';
				break;
			case 14:
				$gateway = 'Mtop';
				break;
			case 15:
				$gateway = 'PayPal';
				break;
			case 16:
				$gateway = 'reCARD';
				break;
			default:
				$gateway = __('admincp.unknown');
		}

		return $gateway;
	}

	/**
	 * @int $code
	 */
	public static function cardType($code)
	{

		switch ($code) {
			case 1:
				$type = 'Viettel';
				break;
			case 2:
				$type = 'Mobifone';
				break;
			case 3:
				$type = 'Vinaphone';
				break;
			case 4:
				$type = 'Vcoin';
				break;
			case 11:
				$type = 'True Money';
				break;
			case 12:
				$type = 'ATM';
				break;
			case 13:
				$type = 'PayPal sale';
				break;
			default:
				$type = __('admincp.unknown');
		}

		return $type;
	}

	/**
	 * @int $amount
	 * @boolen $format
	 */
	public static function getBonus($amount, $format = false)
	{

		$exchange = Config::get('mu.recharge.exchange');

		$bonus_info = Config::get('recharge.bonus');

		switch ($amount) {
			case 50:
				$bonus = $bonus_info['50'];
				break;
			case 90:
				$bonus = $bonus_info['90'];
				break;
			case 150:
				$bonus = $bonus_info['150'];
				break;
			case 300:
				$bonus = $bonus_info['300'];
				break;
			case 500:
				$bonus = $bonus_info['500'];
				break;
			case 1000:
				$bonus = $bonus_info['1000'];
				break;
			default:
				$bouns = 0;
		}

		$output = ($amount * $exchange) + $bonus;

		if ($format !== FALSE) {
			$output = Helper::numberFormat($output);
		}

		return $output;
	}

	/**
	 * @int $day
	 */
	public static function getDayWeek($day)
	{

		$dt = Carbon::now();

		$week = $dt->dayOfWeek;

		if ($week < $day) {

			switch ($day) {
				case 0:
					$date = $dt->next(Carbon::SUNDAY);
					break;
				case 1:
					$date = $dt->next(Carbon::MONDAY);
					break;
				case 2:
					$date = $dt->next(Carbon::TUESDAY);
					break;
				case 3:
					$date = $dt->next(Carbon::WEDNESDAY);
					break;
				case 4:
					$date = $dt->next(Carbon::THURSDAY);
					break;
				case 5:
					$date = $dt->next(Carbon::FRIDAY);
					break;
				case 6:
					$date = $dt->next(Carbon::SATURDAY);
					break;
			}

			$output = self::getFormatDay($date);
		} elseif ($week > $day) {

			switch ($day) {
				case 0:
					$date = $dt->previous(Carbon::SUNDAY);
					break;
				case 1:
					$date = $dt->previous(Carbon::MONDAY);
					break;
				case 2:
					$date = $dt->previous(Carbon::TUESDAY);
					break;
				case 3:
					$date = $dt->previous(Carbon::WEDNESDAY);
					break;
				case 4:
					$date = $dt->previous(Carbon::THURSDAY);
					break;
				case 5:
					$date = $dt->previous(Carbon::FRIDAY);
					break;
				case 6:
					$date = $dt->previous(Carbon::SATURDAY);
					break;
			}

			$output = self::getFormatDay($date);
		} else {
			$output = self::getFormatDay($dt);
		}

		return $output;
	}

	/**
	 * @date $dt
	 */
	public static function getFormatDay($dt)
	{
		return $dt->format('d/m/Y');
	}

	/**
	 * @int $date
	 */
	public static function getFormatDate($date)
	{
		$dt = Carbon::parse($date);

		return $dt->format('H:i d/m/Y');
	}

	/**
	 * @int $amount
	 * @int $type
	 */
	public static function getDiscount($recharge)
	{

		$gateway = self::getGatewayInfo($recharge->gateway);

		$gateway_collect = collect($gateway);

		$gateway_info = $gateway_collect->where('merchant_id', $recharge->merchant);

		$gateway_plucked = $gateway_info->pluck('discount');

		$discount = $gateway_plucked[0];

		switch ($recharge->type) {
			case 1:
				$revenue = $recharge->total * $discount['viettel'] / 100;
				break;
			case 2:
				$revenue = $recharge->total * $discount['mobifone'] / 100;
				break;
			case 3:
				$revenue = $recharge->total * $discount['vinaphone'] / 100;
				break;
			case 4:
				$revenue = $recharge->total * $discount['gate'] / 100;
				break;
			case 11:
				$revenue = $recharge->total * $discount['truemoney'] / 100;
				break;
		}

		$output = self::numberFormat($revenue);

		return $output;
	}

	/**
	 * @int $amount
	 * @int $type
	 */
	public static function getDiscountPercent($recharge)
	{

		$gateway = self::getGatewayInfo($recharge->gateway);

		$gateway_collect = collect($gateway);

		$gateway_info = $gateway_collect->where('merchant_id', $recharge->merchant);

		$gateway_plucked = $gateway_info->pluck('discount');

		$discount = $gateway_plucked[0];

		switch ($recharge->type) {
			case 1:
				$output = $discount['viettel'];
				break;
			case 2:
				$output = $discount['mobifone'];
				break;
			case 3:
				$output = $discount['vinaphone'];
				break;
			case 4:
				$output = $discount['gate'];
				break;
			case 11:
				$output = $discount['truemoney'];
				break;
		}

		return $output;
	}

	/**
	 * @array $revenues
	 */
	public static function getTotalRevenue($revenues)
	{

		$amount = 0;
		foreach ($revenues as $revenue) {
			$amount += $revenue->total;
		}

		$output = self::numberFormat($amount);

		return $output;
	}

	/**
	 * @array $revenues
	 * @boolen $format
	 */
	public static function getTotalDiscount($revenues, $format = false)
	{

		$amount = 0;

		foreach ($revenues as $revenue) {

			$gateway = self::getGatewayInfo($revenue->gateway);

			$gateway_collect = collect($gateway);

			foreach ($gateway_collect as $gateway) {
				if ($gateway['merchant_id'] == $revenue->merchant) {
					$gateway_info = $gateway;
				}
			}

			//$gateway_info = $gateway_collect->where('merchant', $revenue->merchant);

			$gateway_plucked = $gateway_info['discount'];

			$discount = $gateway_plucked;

			switch ($revenue->type) {
				case 1:
					$amount += $revenue->total * $discount['viettel'] / 100;
					break;
				case 2:
					$amount += $revenue->total * $discount['mobifone'] / 100;
					break;
				case 3:
					$amount += $revenue->total * $discount['vinaphone'] / 100;
					break;
				case 4:
					$amount += $revenue->total * $discount['gate'] / 100;
					break;
				case 11:
					$amount += $revenue->total * $discount['truemoney'] / 100;
					break;
			}
		}

		if ($format !== FALSE) {
			$amount = self::numberFormat($amount);
		}

		return $amount;
	}

	/**
	 * @int $type
	 */
	public static function getHistoryType($type)
	{

		switch ($type) {
			case 1:
				$output = __('admincp.change_acc_info');
				break;
			case 2:
				$output = __('usercp.change_password');
				break;
			case 3:
				$output = __('usercp.withdraw_diamond');
				break;
			case 4:
				$output = __('usercp.monthly_card');
				break;
			default:
				$output = __('admincp.unknown');
		}

		return $output;
	}

	/**
	 * @int $type
	 */
	public static function getLogType($type)
	{

		switch ($type) {
			case 1:
				$output = __('admincp.add') . ' ' . Config::get('mu.currency.name');
				break;
			case 2:
				$output = __('admincp.sub') . ' ' . Config::get('mu.currency.name');
				break;
			case 3:
				$output = __('usercp.change_password');
				break;
			case 4:
				$output = __('admincp.change_phone');
				break;
			case 5:
				$output = __('admincp.change_email');
				break;
			case 6:
				$output = __('admincp.announce');
				break;
			case 7:
				$output = __('admincp.give_item');
				break;
			case 8:
				$output = __('admincp.ban');
				break;
			case 9:
				$output = __('admincp.unban');
				break;
			case 10:
				$output = __('admincp.change_secret');
				break;
			case 11:
				$output = __('admincp.giftcode');
				break;
			case 12:
				$output = __('admincp.ban');
				break;
			case 13:
				$output = __('admincp.unban');
				break;
			default:
				$output = __('admincp.unknown');
		}

		return $output;
	}

	/**
	 * @int $type
	 */
	public static function getGiftcodeType($type)
	{

		switch ($type) {
			case 0:
				$output = __('admincp.all_char');
				break;
			case 1:
				$output = __('admincp.one_char');
				break;
			case 2:
				$output = "Sử dụng cho tài khoản";
				break;
			default:
				$output = __('admincp.unknown');
		}

		return $output;
	}

	/**
	 *
	 */
	public static function getListCountry()
	{

		$country_csv = storage_path('app/country-codes.csv');

		$countries = array_map('str_getcsv', file($country_csv));

		array_shift($countries);

		$output = [];
		foreach ($countries as $country) {
			$code = $country[4];

			$output[$code] = $country[0];
		}

		return $output;
	}

	/**
	 * @int $type
	 * @array $content
	 * @int $balance
	 */
	public static function getHistoryContent($type, $content, $balance)
	{

		switch ($type) {
			case 1:
				$output = __('admincp.history_content_1', ['phone' => $content['phone'], 'email' => $content['email']]);
				break;
			case 3:
				$output = __('admincp.history_content_3', ['diamond' => self::numberFormat($content['money']), 'bonus' => isset($content['bonus']) ? self::numberFormat($content['bonus']) : 0, 'balance' => self::numberFormat($balance), 'currency' => Config::get('mu.currency.name')]);
				break;
			case 2:
				$output = 'Thay đổi mật khẩu, nếu không phải bạn đổi vui lòng kiểm tra lại.';
				break;
			case 5:
				$output = 'Thay đổi mã game, nếu không phải bạn đổi vui lòng kiểm tra lại.';
				break;
			case 4:
				$output = __('admincp.history_content_4', ['money' => self::numberFormat($content['money']), 'balance' => self::numberFormat($balance), 'currency' => Config::get('mu.currency.name')]);
				break;
			default:
				$output = __('admincp.unknown');
		}

		return $output;
	}

	/**
	 * @int $type
	 * @array $content
	 */
	public static function getLogContent($type, $content)
	{

		switch ($type) {
			case 1:
				$output = __('admincp.log_content_1', [
					'money' => self::numberFormat($content['coin']),
					'currency' => Config::get('mu.currency.name'),
					'username' => self::getUserName($content['userid'], true),
					'reason' => isset($content['reason']) ? $content['reason'] : ''
				]);
				break;
			case 2:
				$output = __('admincp.log_content_2', [
					'money' => self::numberFormat($content['coin']),
					'currency' => Config::get('mu.currency.name'),
					'username' => self::getUserName($content['userid'], true),
					'reason' => isset($content['reason']) ? $content['reason'] : ''
				]);
				break;
			case 3:
				$output = __('admincp.log_content_3', ['username' =>  self::getUserName($content['userid'], true), 'password' => $content['password']]);
				break;
			case 4:
				$output = __('admincp.log_content_4', ['username' =>  self::getUserName($content['userid'], true), 'phone' => $content['phone']]);
				break;
			case 5:
				$output = __('admincp.log_content_5', ['username' =>  self::getUserName($content['userid'], true), 'email' => $content['email']]);
				break;
			case 6:
				$output = $content['message'];
				break;
			case 7:

				$goods_parse = self::getGoods();

				$item_arr = [];

				$goods = self::getNameGoods($goods_parse, $content['items']);

				foreach ($goods as $item) {
					$item_arr[] = $item['name'];
				}

				$items = collect($item_arr)->implode(', ');

				$output = __('admincp.log_content_7', ['char' =>  self::getCharName($content['rid'], $content['zoneid']), 'items' => $items, 'message' => $content['message']]);
				break;
			case 8:
				$output = __('admincp.log_content_8', ['char' =>  self::getCharName($content['rid'], $content['zoneid']), 'reason' => $content['reason']]);
				break;
			case 9:
				$output = __('admincp.log_content_9', ['char' =>  self::getCharName($content['rid'], $content['zoneid']), 'reason' => $content['reason']]);
				break;
			case 10:
				$output = __('admincp.log_content_10', ['username' =>  self::getUserName($content['userid'], true)]);
				break;
			case 11:

				$goods_parse = self::getGoods();

				$item_arr = [];

				$goods = self::getNameGoods($goods_parse, $content['items']);

				foreach ($goods as $item) {
					$item_arr[] = $item['name'];
				}

				$items = collect($item_arr)->implode(', ');

				$output = __('admincp.log_content_11', ['giftcode' => implode('-', $content['giftcode']), 'zoneid' => self::getServer($content['zoneid']), 'items' => $items, 'message' => $content['message']]);
				break;
			case 12:
				$output = __('admincp.log_content_12', ['username' =>  self::getUserName($content['userid'], true), 'reason' => $content['reason']]);
				break;
			case 13:
				$output = __('admincp.log_content_13', ['username' =>  self::getUserName($content['userid'], true), 'reason' => $content['reason']]);
				break;
			default:
				$output = __('admincp.unknown');
		}

		return $output;
	}

	/**
	 * @int $error_code
	 */
	public static function GetErrorMessage($error_code)
	{
		$arrCode = [
			'00' =>  'Giao dịch thành công',
			'99' =>  'Lỗi, tuy nhiên lỗi chưa được định nghĩa hoặc chưa xác định được nguyên nhân',
			'01' =>  'Lỗi, địa chỉ IP truy cập API của NgânLượng.vn bị từ chối',
			'02' =>  'Lỗi, tham số gửi từ merchant tới NgânLượng.vn chưa chính xác (thường sai tên tham số hoặc thiếu tham số)',
			'03' =>  'Lỗi, Mã merchant không tồn tại hoặc merchant đang bị khóa kết nối tới NgânLượng.vn',
			'04' =>  'Lỗi, Mã checksum không chính xác (lỗi này thường xảy ra khi mật khẩu giao tiếp giữa merchant và NgânLượng.vn không chính xác, hoặc cách sắp xếp các tham số trong biến params không đúng)',
			'05' =>  'Tài khoản nhận tiền nạp của merchant không tồn tại',
			'06' =>  'Tài khoản nhận tiền nạp của merchant đang bị khóa hoặc bị phong tỏa, không thể thực hiện được giao dịch nạp tiền',
			'07' =>  'Thẻ đã được sử dụng ',
			'08' =>  'Thẻ bị khóa',
			'09' =>  'Thẻ hết hạn sử dụng',
			'10' =>  'Thẻ chưa được kích hoạt hoặc không tồn tại',
			'11' =>  'Mã thẻ sai định dạng',
			'12' =>  'Sai số serial của thẻ',
			'13' =>  'Mã thẻ và số serial không khớp',
			'14' =>  'Thẻ không tồn tại',
			'15' =>  'Thẻ không sử dụng được',
			'16' =>  'Số lần thử (nhập sai liên tiếp) của thẻ vượt quá giới hạn cho phép',
			'17' =>  'Hệ thống Telco bị lỗi hoặc quá tải, thẻ chưa bị trừ',
			'18' =>  'Hệ thống Telco bị lỗi hoặc quá tải, thẻ có thể bị trừ, cần phối hợp với NgânLượng.vn để tra soát',
			'19' =>  'Kết nối từ NgânLượng.vn tới hệ thống Telco bị lỗi, thẻ chưa bị trừ (thường do lỗi kết nối giữa NgânLượng.vn với Telco, ví dụ sai tham số kết nối, mà không liên quan đến merchant)',
			'20' =>  'Kết nối tới telco thành công, thẻ bị trừ nhưng chưa cộng tiền trên NgânLượng.vn'
		];

		return $arrCode[$error_code];
	}
}
